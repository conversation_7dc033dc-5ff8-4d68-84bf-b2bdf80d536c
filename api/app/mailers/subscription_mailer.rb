# app/mailers/subscription_mailer.rb
class SubscriptionMailer < ApplicationMailer
  default from: '<EMAIL>'

  # Send when payment is successfully processed
  def payment_success(subscriber, invoice_id, test = false)
    @subscriber = subscriber
    @invoice_id = invoice_id

    begin
      # Fetch the invoice details from Stripe
      @invoice = Stripe::Invoice.retrieve({
        id: invoice_id,
        expand: ['subscription', 'customer']
      })
    rescue Stripe::StripeError => e
      Rails.logger.error "Error retrieving invoice in payment_success email: #{e.message}"
      # Use mock data in development/test or if there's an error
      @invoice = development_or_test? || test ? mock_invoice(invoice_id) : nil
    end

    # Only proceed if we have invoice data
    if @invoice
      # Format amount for display
      @amount = format_amount(@invoice.total, @invoice.currency)

      # Get products from invoice
      @products = get_products_from_invoice(@invoice)

      mail(to: @subscriber.email, subject: 'Payment Successful - Developing Experts Subscription', cc: @subscriber.cc_email)
    else
      Rails.logger.error "Cannot send payment_success email: No invoice data available"
      nil
    end
  end

  # Send when payment fails
  def payment_failed(subscriber, invoice_id, test = false)
    @subscriber = subscriber
    @invoice_id = invoice_id

    begin
      # Fetch the invoice details from Stripe
      @invoice = Stripe::Invoice.retrieve({
        id: invoice_id,
        expand: ['subscription', 'customer']
      })
    rescue Stripe::StripeError => e
      Rails.logger.error "Error retrieving invoice in payment_failed email: #{e.message}"
      # Use mock data in development/test or if there's an error
      @invoice = development_or_test? || test ? mock_invoice(invoice_id) : nil
    end

    if @invoice
      # Format amount for display
      @amount = format_amount(@invoice.total, @invoice.currency)

      # Get retry date - typically 3-5 days later
      @retry_date = (Time.now + 3.days).strftime("%B %d, %Y")

      mail(to: @subscriber.email, subject: 'Action Required: Payment Failed - Developing Experts Subscription', cc: @subscriber.cc_email)
    else
      Rails.logger.error "Cannot send payment_failed email: No invoice data available"
      nil
    end
  end

  # Send when subscription status changes
  def status_change(subscriber, old_status, new_status, test = false)
    @subscriber = subscriber
    @old_status = old_status
    @new_status = new_status

    # Set subject based on status change
    subject = if new_status == 'active'
                'Your Developing Experts Subscription is Now Active'
              elsif new_status == 'past_due'
                'Action Required: Your Developing Experts Subscription is Past Due'
              elsif new_status == 'unpaid'
                'Your Developing Experts Subscription is Unpaid'
              else
                'Your Developing Experts Subscription Status Has Changed'
              end

    # Get subscription details
    begin
      if subscriber.stripe_subscription_id
        @subscription = Stripe::Subscription.retrieve({
          id: subscriber.stripe_subscription_id,
          expand: ['items.data.price.product']
        })
      else
        @subscription = development_or_test? || test ? mock_subscription("sub_test_#{SecureRandom.hex(10)}") : nil
      end
    rescue Stripe::StripeError => e
      Rails.logger.error "Error retrieving subscription in status_change email: #{e.message}"
      # Use mock data in development/test or if there's an error
      @subscription = development_or_test? || test ? mock_subscription("sub_test_#{SecureRandom.hex(10)}") : nil
    end

    if @subscription
      # Get products from subscription
      @products = get_products_from_subscription(@subscription)
    end

    mail(to: @subscriber.email, subject: subject, cc: @subscriber.cc_email)
  end

  # Send when subscription is scheduled for cancellation at period end
  def scheduled_cancellation(subscriber, end_date, test = false)
    @subscriber = subscriber
    @end_date = end_date.strftime('%B %d, %Y')

    # Convert end_date to Time if it's not already, then calculate days remaining
    end_time = end_date.is_a?(Time) ? end_date : end_date.to_time
    @days_remaining = ((end_time - Time.now) / 86400).round

    mail(to: @subscriber.email, subject: 'Your Developing Experts Subscription Will Be Cancelled', cc: @subscriber.cc_email)
  end

  # Send when subscription is cancelled
  def subscription_canceled(subscriber, test = false)
    @subscriber = subscriber
    @cancellation_date = subscriber.cancellation_date&.strftime('%B %d, %Y') || Time.now.strftime('%B %d, %Y')

    mail(to: @subscriber.email, subject: 'Your Developing Experts Subscription Has Been Cancelled', cc: @subscriber.cc_email)
  end

  # Send when subscription is scheduled for cancellation at period end
  def admin_scheduled_cancellation(subscriber, end_date, test = false, recipient_emails = ENV['EMAILS_NOTIFY_ON_NEW_SUBSCRIPTION'])
    # Get the notification recipients from environment variable
    notification_emails = recipient_emails&.split(',')&.map(&:strip)&.reject(&:empty?).presence || []
    if notification_emails.nil? || notification_emails.empty?
      Rails.logger.warn 'No recipient emails provided for new subscription notification'
      return nil
    end

    @subscriber = subscriber
    @end_date = end_date.strftime('%B %d, %Y')

    # Convert end_date to Time if it's not already, then calculate days remaining
    end_time = end_date.is_a?(Time) ? end_date : end_date.to_time
    @days_remaining = ((end_time - Time.now) / 86400).round

    @cancellation_date = subscriber.cancellation_date&.strftime('%B %d, %Y') || Time.now.strftime('%B %d, %Y')

    @subscription = Stripe::Subscription.retrieve({
      id: subscriber.stripe_subscription_id,
      expand: ['items.data.price.product']
    }) unless test
    @subscription = mock_subscription("sub_test_#{SecureRandom.hex(10)}") if test

    @products = get_products_from_subscription(@subscription)
    @subscription_type = @subscriber.subscriber.is_a?(User) ? 'Personal' : 'School'

    # Format amount
    total_amount = @products.sum { |p| p[:raw_amount].to_i }
    @formatted_amount = format_amount(total_amount, @subscription.currency || 'gbp')
    @interval = @subscription.items&.data&.first&.plan&.interval.presence || 'year'

    Rails.logger.info "Sending new subscription notification to: #{notification_emails.join(', ')}"
    mail(to: notification_emails, subject: 'A Developing Experts Subscription Will Be Cancelled')
  end

  # Send when subscription is cancelled
  def admin_subscription_canceled(subscriber, test = false, recipient_emails = ENV['EMAILS_NOTIFY_ON_NEW_SUBSCRIPTION'])
    # Get the notification recipients from environment variable
    notification_emails = recipient_emails&.split(',')&.map(&:strip)&.reject(&:empty?).presence || []
    if notification_emails.nil? || notification_emails.empty?
      Rails.logger.warn 'No recipient emails provided for new subscription notification'
      return nil
    end

    @subscriber = subscriber

    @subscription_type = @subscriber.subscriber.is_a?(User) ? 'Personal' : 'School'

    mail(to: notification_emails, subject: 'A Developing Experts Subscription Has Been Cancelled')
  end

  # Send when a subscription is first created
  def subscription_created(subscriber, test = false)
    @subscriber = subscriber

    begin
      if subscriber.stripe_subscription_id
        @subscription = Stripe::Subscription.retrieve({
          id: subscriber.stripe_subscription_id,
          expand: ['items.data.price.product']
        })
      else
        @subscription = development_or_test? || test ? mock_subscription("sub_test_#{SecureRandom.hex(10)}") : nil
      end
    rescue Stripe::StripeError => e
      Rails.logger.error "Error retrieving subscription in subscription_created email: #{e.message}"
      # Use mock data in development/test or if there's an error
      @subscription = development_or_test? || test ? mock_subscription("sub_test_#{SecureRandom.hex(10)}") : nil
    end

    if @subscription
      # Calculate renewal date
      @renewal_date = Time.at(@subscription.current_period_end).strftime('%B %d, %Y')

      # Get products from subscription
      @products = get_products_from_subscription(@subscription)
    end

    mail(to: @subscriber.email, subject: 'Welcome to Developing Experts!', cc: @subscriber.cc_email)
  end

  # Send when an upcoming invoice is generated
  def upcoming_invoice_notification(subscriber, invoice_id, test = false)
    @subscriber = subscriber
    @invoice_id = invoice_id

    begin
      # Fetch the invoice details from Stripe
      @invoice = Stripe::Invoice.retrieve({
        id: invoice_id,
        expand: ['subscription', 'customer']
      })
    rescue Stripe::StripeError => e
      Rails.logger.error "Error retrieving invoice in upcoming_invoice_notification email: #{e.message}"
      # Use mock data in development/test or if there's an error
      @invoice = development_or_test? || test ? mock_invoice(invoice_id) : nil
    end

    if @invoice
      # Format amount for display
      @amount = format_amount(@invoice.total, @invoice.currency)

      # Set due date
      @due_date = Time.at(@invoice.due_date || (@invoice.created + 30.days)).strftime('%B %d, %Y')

      # Get products from invoice
      @products = get_products_from_invoice(@invoice)

      mail(to: @subscriber.email, subject: 'Your Upcoming Developing Experts Invoice', cc: @subscriber.cc_email)
    else
      Rails.logger.error "Cannot send upcoming_invoice_notification email: No invoice data available"
      nil
    end
  end

  # Send when a subscription is reactivated
  def subscription_reactivated(subscriber, test = false)
    @subscriber = subscriber

    begin
      if subscriber.stripe_subscription_id
        @subscription = Stripe::Subscription.retrieve({
          id: subscriber.stripe_subscription_id,
          expand: ['items.data.price.product']
        })
      else
        @subscription = development_or_test? || test ? mock_subscription("sub_test_#{SecureRandom.hex(10)}") : nil
      end
    rescue Stripe::StripeError => e
      Rails.logger.error "Error retrieving subscription in subscription_reactivated email: #{e.message}"
      # Use mock data in development/test or if there's an error
      @subscription = development_or_test? || test ? mock_subscription("sub_test_#{SecureRandom.hex(10)}") : nil
    end

    if @subscription
      # Calculate renewal date
      @renewal_date = Time.at(@subscription.current_period_end).strftime('%B %d, %Y')

      # Get products from subscription
      @products = get_products_from_subscription(@subscription)
    end

    mail(to: @subscriber.email, subject: 'Your Developing Experts Subscription Has Been Reactivated', cc: @subscriber.cc_email)
  end

  # Send when an invoice is ready to be viewed/paid
  def invoice_notification(subscriber, invoice_id, notification_type = :standard, test = false)
    @subscriber = subscriber
    @invoice_id = invoice_id
    @notification_type = notification_type

    begin
      @invoice = Stripe::Invoice.retrieve({
        id: invoice_id,
        expand: ['subscription', 'customer', 'lines.data.price.product']
      })
    rescue Stripe::StripeError => e
      Rails.logger.error "Error retrieving invoice in invoice_notification email: #{e.message}"
      @invoice = development_or_test? || test ? mock_invoice(invoice_id) : nil
    end

    if @invoice
      @amount = format_amount(@invoice.total, @invoice.currency)
      @due_date = Time.at(@invoice.due_date || (@invoice.created + 30.days)).strftime('%B %d, %Y')
      @products = get_products_from_invoice(@invoice)

      # Set subject based on notification type
      subject = case notification_type
                when :subscription_change
                  "Your Subscription Change Invoice - Developing Experts"
                else
                  "Your Developing Experts Invoice is Ready"
                end

      mail(to: @subscriber.email, subject: subject, cc: @subscriber.cc_email)
    else
      Rails.logger.error "Cannot send invoice_notification email: No invoice data available"
      nil
    end
  end

  # Send when a subscription is updated (upgraded/downgraded)
  def subscription_updated(subscriber, subscription_id, test = false)
    @subscriber = subscriber
    @subscription_id = subscription_id

    begin
      @subscription = Stripe::Subscription.retrieve({
        id: subscription_id,
        expand: ['items.data.price.product']
      })
    rescue Stripe::StripeError => e
      Rails.logger.error "Error retrieving subscription in subscription_updated email: #{e.message}"
      @subscription = development_or_test? || test ? mock_subscription(subscription_id) : nil
    end

    if @subscription
      # Get products from subscription
      @products = get_products_from_subscription(@subscription)
      @renewal_date = Time.at(@subscription.current_period_end).strftime('%B %d, %Y')

      mail(to: @subscriber.email, subject: 'Your Developing Experts Subscription Has Been Updated', cc: @subscriber.cc_email)
    else
      Rails.logger.error "Cannot send subscription_updated email: No subscription data available"
      nil
    end
  end

  # Send notification for overdue invoice
  def invoice_overdue(subscriber, invoice_id, test = false)
    @subscriber = subscriber
    @invoice_id = invoice_id

    begin
      @invoice = Stripe::Invoice.retrieve({
        id: invoice_id,
        expand: ['lines.data.price.product', 'subscription']
      })
    rescue Stripe::StripeError => e
      Rails.logger.error "Error retrieving invoice in invoice_overdue email: #{e.message}"
      @invoice = development_or_test? || test ? mock_invoice(invoice_id) : nil
    end

    if @invoice
      # Format amount for display
      @amount = format_amount(@invoice.total, @invoice.currency)

      # Format due date
      @due_date = Time.at(@invoice.due_date || (@invoice.created + 30.days)).strftime('%B %d, %Y')

      # Get products from invoice
      @products = get_products_from_invoice(@invoice)

      # More urgent subject line
      subject = "IMPORTANT: Your Developing Experts Invoice is Overdue - Action Required"

      mail(to: @subscriber.email, subject: subject, cc: @subscriber.cc_email)
    else
      Rails.logger.error "Cannot send invoice_overdue email: No invoice data available"
      nil
    end
  end

  def invoice_reminder(subscriber, invoice_id, test = false)
    @subscriber = subscriber
    @invoice_id = invoice_id

    begin
      # Fetch the invoice details from Stripe
      @invoice = Stripe::Invoice.retrieve({
        id: invoice_id,
        expand: ['lines.data.price.product', 'subscription']
      })
    rescue Stripe::StripeError => e
      Rails.logger.error "Error retrieving invoice in invoice_reminder email: #{e.message}"
      # Use mock data in development/test or if there's an error
      @invoice = development_or_test? || test ? mock_invoice(invoice_id) : nil
    end

    if @invoice
      # Format amount for display
      @amount = format_amount(@invoice.total, @invoice.currency)

      # Calculate due date
      @due_date = Time.at(@invoice.due_date || (@invoice.created + 30.days)).strftime("%B %d, %Y")

      # Calculate days until due
      days_until_due = [(@invoice.due_date || (@invoice.created + 30.days)) - Time.now.to_i, 0].max / 86400
      @days_until_due = days_until_due.to_i

      # Get products from invoice
      @products = get_products_from_invoice(@invoice)

      # Send the email
      mail(to: @subscriber.email, subject: 'Reminder: Your Developing Experts Invoice is Due Soon', cc: @subscriber.cc_email)
    else
      Rails.logger.error 'Cannot send invoice_reminder email: No invoice data available'
      nil
    end
  end

  def admin_new_subscription_notification(subscriber, subscription_id, test = false, recipient_emails = ENV['EMAILS_NOTIFY_ON_NEW_SUBSCRIPTION'])
    if recipient_emails.nil? || recipient_emails.empty?
      Rails.logger.warn 'No recipient emails provided for new subscription notification'
      return nil
    end

    @subscriber = subscriber
    @subscription = Stripe::Subscription.retrieve({
      id: subscription_id,
      expand: ['items.data.price.product']
    }) unless test
    @subscription = mock_subscription("sub_test_#{SecureRandom.hex(10)}") if test

    @products = get_products_from_subscription(@subscription)
    @subscription_type = @subscriber.subscriber.is_a?(User) ? 'Personal' : 'School'

    # Format the date
    @created_date = Time.at(@subscription.current_period_start).strftime('%B %d, %Y at %H:%M:%S')

    # Calculate next billing date
    @next_billing_date = Time.at(@subscription.current_period_end).strftime('%B %d, %Y')

    # Format amount
    total_amount = @products.sum { |p| p[:raw_amount].to_i }
    @formatted_amount = format_amount(total_amount, @subscription.currency || 'gbp')
    @interval = @subscription.items.data.first.plan.interval || 'year'

    # Get the notification recipients from environment variable
    notification_emails = recipient_emails.split(',').map(&:strip).reject(&:empty?)

    # Only send if we have recipients
    if notification_emails.any?
      Rails.logger.info "Sending new subscription notification to: #{notification_emails.join(', ')}"
      mail(
        to: notification_emails,
        subject: "New Subscription: #{@subscriber.name} (#{@subscription_type})"
      )
    else
      Rails.logger.warn 'No notification recipients configured for new subscription alerts'
      nil
    end
  end

  def subscription_renewed(subscriber)
    @subscriber = subscriber
    # Send the email
    mail(to: @subscriber.email, subject: 'Thank You For Renewing', cc: @subscriber.cc_email)
  end

  private

  def development_or_test?
    Rails.env.development? || Rails.env.test?
  end

  def format_amount(amount, currency)
    amount_decimal = amount.to_f / 100.0

    case currency.downcase
    when 'gbp'
      "£#{sprintf('%.2f', amount_decimal)}"
    when 'usd'
      "$#{sprintf('%.2f', amount_decimal)}"
    when 'eur'
      "€#{sprintf('%.2f', amount_decimal)}"
    else
      "#{sprintf('%.2f', amount_decimal)} #{currency.upcase}"
    end
  end

  def get_products_from_invoice(invoice)
    products = []

    if invoice.respond_to?(:lines) && invoice.lines.respond_to?(:data)
      invoice.lines.data.each do |line|
        if line.respond_to?(:price) && line.price
          # Get product information, handling both expanded and non-expanded cases
          product_name = if line.price.respond_to?(:product)
            # If product is expanded to a full object
            line.price.product.is_a?(String) ? "Developing Experts Product" : line.price.product.name
          elsif line.price.respond_to?(:nickname)
            # If we only have the price nickname
            line.price.nickname
          else
            "Developing Experts Product"
          end

          raw_amount = line.respond_to?(:amount) ? line.amount : 0
          currency = invoice.respond_to?(:currency) ? invoice.currency : 'gbp'

          products << {
            name: product_name,
            description: line.respond_to?(:description) && line.description ? line.description : product_name,
            raw_amount: raw_amount,
            amount: format_amount(raw_amount, currency)
          }
        end
      end
    end

    # Return default product if no products were found
    if products.empty? && development_or_test?
      products << {
        name: 'Science Subscription',
        description: 'Science Subscription - Small School',
        raw_amount: 10000,
        amount: '£100.00'
      }
    end

    products
  end

  def get_products_from_subscription(subscription)
    products = []

    if subscription.respond_to?(:items) && subscription.items.respond_to?(:data)
      subscription.items.data.each do |item|
        if item.respond_to?(:price) && item.price
          if item.price.respond_to?(:product) && item.price.product
            raw_amount = item.price.respond_to?(:unit_amount) ? item.price.unit_amount : 0
            currency = item.price.respond_to?(:currency) ? item.price.currency : 'gbp'

            products << {
              name: item.price.product.name,
              description: item.price.respond_to?(:nickname) && item.price.nickname ?
                item.price.nickname : item.price.product.name,
              raw_amount: raw_amount,
              amount: format_amount(raw_amount, currency)
            }
          end
        end
      end
    end

    # Throw an error if no products were found
    if products.empty?
      raise "No products found in subscription"
    end

    products
  end

  # Mock an invoice for testing
  def mock_invoice(invoice_id)
    # Create a mock invoice with essential properties
    OpenStruct.new(
      id: invoice_id,
      total: 10000, # $100.00
      currency: 'gbp',
      due_date: (Time.now + 30.days).to_i,
      created: Time.now.to_i,
      hosted_invoice_url: 'https://pay.stripe.com/invoice/example',
      status: 'open',
      lines: OpenStruct.new(
        data: [
          OpenStruct.new(
            price: OpenStruct.new(
              product: OpenStruct.new(name: 'Science'),
              nickname: 'Science - Small School',
              unit_amount: 10000,
              currency: 'gbp'
            ),
            description: 'Science Subscription - Small School',
            amount: 10000,
            quantity: 1
          ),
          OpenStruct.new(
            price: OpenStruct.new(
              product: OpenStruct.new(name: 'Geography'),
              nickname: 'Geography - Small School',
              unit_amount: 10000,
              currency: 'gbp'
            ),
            description: 'Geography Subscription - Small School',
            amount: 10000,
            quantity: 1
          )
        ]
      ),
      payment_intent: OpenStruct.new(
        next_action: OpenStruct.new(
          redirect_to_url: OpenStruct.new(
            url: 'https://pay.stripe.com/example'
          )
        )
      )
    )
  end

  # Mock a subscription for testing
  def mock_subscription(subscription_id)
    # Create a mock subscription with essential properties
    OpenStruct.new(
      id: subscription_id,
      status: 'active',
      current_period_end: (Time.now + 365.days).to_i,
      current_period_start: Time.now.to_i,
      plan: OpenStruct.new(interval: 'month'),
      items: OpenStruct.new(
        data: [
          OpenStruct.new(
            price: OpenStruct.new(
              product: OpenStruct.new(name: 'Science'),
              nickname: 'Science - Small School',
              unit_amount: 10000,
              currency: 'gbp'
            ),
            quantity: 1
          ),
          OpenStruct.new(
            price: OpenStruct.new(
              product: OpenStruct.new(name: 'Geography'),
              nickname: 'Geography - Small School',
              unit_amount: 10000,
              currency: 'gbp'
            ),
            quantity: 1
          )
        ]
      )
    )
  end
end
