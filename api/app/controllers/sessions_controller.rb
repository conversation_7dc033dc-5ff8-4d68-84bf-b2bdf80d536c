# frozen_string_literal: true

require 'blacklist_ip_range'

class SessionsController < ApplicationController
  api_version 1

  def sign_in_action
    warnings = []
    warnings << :already_signed_in if current_user
    ai_redirect = params[:ai].present?
    if params[:email].present?
      params[:uid] = params[:email].downcase
      params[:provider] = 'email'
    end
    if params[:identifier].present?
      attrs = { identifier: params.require(:identifier), school_id: params.require(:school_id) }
      user = Pupil.find_by(attrs)

      unless user
        render json: { success: false, errors: [:login_invalid] }
        return
      end

      # if is_blocked
      if user.is_blocked
        render json: { success: false, errors: [:blocked] }
        return
      end

      sign_in(user, skip_authentication: true) if user
    else
      attrs = { uid: params.require(:uid), provider: params.require(:provider) }
      user = User.find_by(attrs)

      if user&.is_blocked
        render json: { success: false, errors: [:blocked] }
        return
      end

      if user&.school&.demo?
        render json: { success: false, errors: [:demo_school] }
        return
      end
      if user
        sign_in(user, params[:password])
      end
    end

    if current_user && current_user == user
      result = { success: true }
    else
      puts 'sign_in_action LINE1 ELSE'
      result = { success: false, error: [:login_invalid] }
    end
    result[:warnings] = warnings if warnings.any?
    result[:redirect] = '/user/subscription/subscriptions?signup_redirect=1' if ai_redirect && !!result[:success]

    render json: result
  end

  def sign_out_action
    current_user.device_logins.destroy_all if current_user
    sign_out
    render json: { success: true }
  end

  def token_sign_in
    user = User.find_by(sign_in_token: params.require(:token))
    if user
      user.update!(sign_in_token: nil)
      sign_in(user, skip_authentication: true)
      render json: { success: true }
    else
      render json: { success: false, errors: [:invalid_token] }
    end
  end

  def recover_password
    user = User.find_by({ email: params.require(:email) })

    if user&.is_blocked
      render json: { success: false, errors: [:blocked] }
      return
    end

    if user
      user.send_password_reset_email(token: user.new_recovery_token)
      render json: { success: true }
    else
      render json: { success: false, errors: [:email_not_found] }
    end
  end

  def oauth_callback
    data = request.env['omniauth.auth'].to_h.with_indifferent_access.merge(
      provider: params[:provider]
    )

    begin
      user = User.find_or_create_by(data.slice(:uid, :provider)) do |u|
        u.password = u.send(:new_token)
        u.email = data[:info][:email]
        u.name = data[:info][:name]
        u.type = 'IndividualUser'
      end

      if user.persisted?
        result = sign_in(user, skip_authentication: true)

        result['expiry'] = result['expiry'].split('.')[0]
        result['user_id'] = user.id
      else
        result = { record: user.errors.to_h }
      end
    rescue => e
      if Rails.env.production?
        ErrorLog.create!(error: { errorData: e, backtrace: e.backtrace }.to_json)
      else
        puts e
      end
      result = { fatal: true }
    end

    redirect_to "#{Rails.application.config.x.frontend_url}/social-callback?" + result.to_param
  end

  def current
    has_community_content_count = current_user
                                  &.accessible_lesson_templates
                                  &.where(user_generated: true, available: true)
                                  &.count || 0

    current_user&.ai_subscription&.sync_down

    render json: {
      user: current_user && {
        id: current_user.id,
        email: current_user.email,
        name: current_user.name,
        school_id: current_user.school_id,
        organisation_id: current_user.organisation_id,
        demo: !current_user.school&.subscribed?,
        home_school: false,
        is_school_admin: current_user.is_school_admin,
        has_campaign: !!current_user.organisation&.campaigns&.first,
        country: current_user.country,
        expired: current_school&.expired?,
        has_subscription: current_user.has_subscription,
        school_subscribed: current_user.school&.subscribed?,
        free_subscription: current_user.school&.free_subscription,
        points: current_user.user_achievement_points,
        type: current_user.type,
        alias: current_user.alias,
        has_xls_import: current_school &&
                        XlsImport.where(school: current_school, complete: true).exists?,
        teacher_count: current_school&.teachers&.count,
        rocketParts: current_user&.avatar_configuration.present? ? current_user&.avatar_configuration&.[]('rocketParts') : nil,
        job_hunter_interests: current_user&.job_hunter_interests,
        has_career_profile: current_user&.has_career_profile,
        interest_tags: current_user&.interest_tags,
        wonde_request_status: current_school && current_school.wonde_request_status,
        wonde_id: current_user.wonde_id,
        school_wonde_id: current_school&.wonde_id,
        wonde_auto_sync: !!current_user&.school&.sync_wonde_weekly,
        wonde_enabled: current_school&.wonde_enabled || false,
        whiteLabelOrganisation: current_user.organisation || current_school&.organisation || current_user.white_label_organisation,
        in_trial: current_user&.school&.in_trial?,
        trial_pending: current_user&.school&.trial_pending?,
        trial_ended: current_user&.school&.trial_ended?,
        restricted: current_user.account_restricted?,
        subscribed_but_school_restricted: !current_user.has_school_feature_access?,
        subscribed: current_user.subscribed_to_any_service?,
        approaching_renewal: current_user&.school&.subscribed? && !!current_user&.school&.approaching_renewal,
        showPupilLessonPlan: current_user&.school&.show_lesson_plan_pupil_area,
        can_create_lessons: current_user&.can_create_lessons?,
        renewal_month: current_user.school&.renewal_month,
        has_community_content: has_community_content_count > 0,
        can_manage_schools: current_user.admin? && current_user.can_view_school_data,
        example_school: current_user&.school&.is_industry_account?,
        questionnaire_taken: !!current_user&.questionnaire_taken?,
        is_admin: current_user&.admin?,
        is_home_school_cat: current_user&.school&.home_school_category?,
        uk_school: !!current_user&.school&.uk_school&.present?,
        finance_contact: current_user.school&.finance_name && {
          name: current_user.school.finance_name,
          email: current_user.school.finance_email
        },
        school_memo: current_user.school&.memo,
        subscription_status: current_user.subscribed_to_any_service? ? 'Subscribed' : current_user&.school&.hubspot_subscription_status,
        science_leaders: current_user.school&.science_leaders&.map { |user| { id: user.id, email: user.email, name: user.name, science_lead: user.science_lead, is_school_admin: user.is_school_admin } },
        renewal_date: current_user.school&.hubspot_renewal_date,
        can_view_curriculum_documents: current_user&.can_view_curriculum_documents?,
        can_view_glossary: current_user&.school&.can_view_glossary,
        can_access_lesson_ai: current_user&.can_access_lesson_ai,
        can_access_career_builder: current_user&.can_access_career_builder,
        can_access_ai_builder: false,
        ai_subscription_status: current_user&.school&.hubspot_ai_subscription_status,
        ai_subscription: if current_user.ai_subscription&.stripe_cache&.present?
                           {
                             id: current_user.ai_subscription.stripe_cache['id'],
                             status: current_user.ai_subscription.stripe_cache['status'],
                             cancel_at_period_end: current_user.ai_subscription.stripe_cache['cancel_at_period_end']
                           }
                         else
                           nil
                         end,
        use_new_presentation_player: current_user&.use_new_presentation_player,
        curriculum_id: current_user&.school&.new_library_curriculum_id,
        is_new_stripe_subscriber: !!current_user&.subscription
      }
    }
  end

  private def uk_school_from_teacher_payload(params)
    uk_school = nil
    conflict_school = nil

    uk_school = UkSchool.find_by_id(params[:uk_school][:id]) if params[:uk_school].present?

    conflict_school = School.find_by_uk_school_id(uk_school.id) if uk_school.present?

    [uk_school, conflict_school]
  end

  private def validate_teacher_payload(teacher_params, conflict_school = nil)
    errors = {}

    errors[:jobTitle] = 'is required' if teacher_params[:jobTitle].blank?

    if teacher_params[:actualCountryId] == 1
      if teacher_params[:uk_school].blank?
        errors[:uk_school] = 'is required'
      elsif conflict_school.present?
        errors[:uk_school] = 'is in use'
      end
    else
      errors[:category] = 'is required' if teacher_params[:category].blank?
      errors[:schoolName] = 'is required' if teacher_params[:schoolName].blank?
    end

    errors
  end

  # called from teacher/tutor area if user chooses to create jobs profile
  def job_hunter_create_profile
    job_hunter_params = params.permit(
      :fileboy_image_id,
      :linked_in_url,
      :cv_fileboy_id,
      :gender,
      :ethnicity,
      :dob,
      :preferred_location,
      :years_of_experience,
      job_hunter_interests: [:value],
      preferred_career_ids: [],
      interest_tags: []
    )

    job_hunter_interests = format_job_hunter_interests(job_hunter_params[:job_hunter_interests])
    job_hunter_params[:job_hunter_interests] = job_hunter_interests

    user = User.find(current_user.id)
    user.assign_attributes(job_hunter_params.merge({ has_career_profile: true }))

    if user.save
      render json: { success: true, errors: {} }
    else
      render json: {
        success: false,
        errors: user.errors.to_h,
      }
    end
  end

  private def format_job_hunter_interests(job_hunter_interests)
    # the form has a form2arrayfield thing which submits them as { value: string; key: string}[]
    # so i've opted to coerce the list of objects with value keys into a string here
    # we join with the \n delimiter because an fn called on save splits the string by \n
    return '' unless job_hunter_interests.present?
    job_hunter_interests = Array.wrap(job_hunter_interests)
    job_hunter_interests = job_hunter_interests.map { |obj| obj[:value] }
    job_hunter_interests.join("\n")
  end

  def dev_login
    raise ActionController::RoutingError.new('Not Found') unless Rails.env.development?

    school = School.find_by(hubspot_subscription_status: 'Subscribed')

    return unless school

    if params[:user_type] == 'admin teacher'
      user = User.where(email: '<EMAIL>', is_school_admin: true).first
    elsif params[:user_type] == 'teacher'
      user = User.where(type: 'Teacher', is_school_admin: [nil, false], school_id: school.id).first
    elsif params[:user_type] == 'pupil'
      user = User.where(type: 'Pupil', school_id: school.id).first
    elsif params[:user_type] == 'admin'
      user = User.where(type: 'Admin', email: '<EMAIL>').first
    else
      raise ActionController::RoutingError.new('Not Found')
    end

    puts "Dev login for user: #{user&.email} (#{user&.id}), #{params[:user_type]}"

    if user
      sign_in(user, skip_authentication: true)
      redirect_to '/accounts/dashboard'
    else
      redirect_back(fallback_location: root_path)
    end
  end
rescue ActiveRecord::RecordInvalid => e
  # This will catch validation failures for both School and Teacher
  render json: { success: false, errors: e.record.errors.as_json, conflict_school: nil }, status: :unprocessable_entity
rescue => e
  render json: { success: false, errors: { error: e.message }, conflict_school: nil }, status: :unprocessable_entity
end
