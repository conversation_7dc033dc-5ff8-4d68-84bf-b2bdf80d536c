class V2::UsersController < V2::ApplicationController
  def index
    users = User.unscoped.accessible_by(current_ability)

    if params[:ids].present?
      users = users.where(id: params[:ids].split(","))
    end

    if params[:organisationId].present?
      users = users.where(organisation_id: params[:organisationId])
    end

    if params[:whiteLabelOrganisationId].present?
      users = users.where(organisation_id: params[:whiteLabelOrganisationId])
    end

    if params[:userType].present? && ["User", "Teacher"].include?(params[:userType])
      users = users.where(type: params[:userType])
    end

    if params[:query].present?
      users = users.where("name ilike ? OR email ilike ?", "%#{params[:query]}%", "%#{params[:query]}%")
    end

    meta, records = paginate(users.order(created_at: :desc))

    render json: { records: records, meta: meta }
  end

  def show
    user = User.find(params[:id])
    authorize! :read, user
    render json: user.v2_as_json.merge({
                                         preferred_career_ids: user.preferred_career_ids,
                                         interest_tags: user.interest_tags
                                       })
  end

  def update
    user = User.find(params[:id])
    authorize! :update, user

    if (user.update(user_params))

      render json: { saved: true, record: user }
    else
      render json: { saved: false, errors: user.errors.full_messages }
    end
  end

  def destroy
    user = User.find(params[:id])
    authorize! :destroy, user
    if params[:permanentlyDelete] == "true"
      user.permanently_delete_user
      render json: { success: true }
    else
      destroy_record user
    end
  end

  def career_profile_user_stats
    career_profile_users = User.where(has_career_profile: true).or(User.where(type: ["Guardian", "Tutor"]))
    career_profile_users_online = career_profile_users.where('last_activity_at > ?', Time.current - 10.minutes)

    render json: { total: career_profile_users.size, online: career_profile_users_online.size }
  end

  def career_feed
    user = User.find(params[:id])
    meta, records = paginate(user.career_feed)
    records = records.preload(:taggable).as_json(methods: :taggable)
    render json: { records: records, meta: meta }
  end

  def user_provider
    user = User.find(params[:id])
    authorize! :update, user
    render json: { provider: user.provider }
  end

  def update_user_provider_to_email
    user = User.find(params[:id])
    authorize! :update, user
    user.update!(provider: "email", uid: user.email)
    render json: { saved: true }
  end

  def track_template_viewed
    user = User.find_by(id: params[:id])
    unless user
      render json: { nothing: true }
      return
    end
    template = Lesson::Template.find_by(id: params[:templateId])
    TrackingService.track_lesson_template_view(user, template) if template
    render json: { saved: true }
  end

  def hubspot_visitor_verification
    token = HubspotManager.verify_visitor(current_user)
    render json: { token: token, email: current_user.email }
  rescue => error
    if error.is_a? HubspotApiError or error.is_a? Exception
      render json: {
        error: {
          message: error.message,
          backtrace: error.backtrace,
          error: error,
        }
      }
    else
      puts "Unknown error: #{error}"
      render json: {
        error: {
          message: "Unknown error",
          error: error,
        }
      }
    end
  end

  def hubspot_contact_sync
    user = User.find(params[:id])
    authorize! :manage, user

    result = HubspotManager.wrap_with_error_handler({ user: user }) do
      HubspotManager.update_contact(user)
    end

    if result.error
      render json: { saved: true }
    else
      render json: { saved: false, message: result.error }
    end
  end

  def create_employer
    unless current_user.admin?
      render json: { saved: false, errors: ["permission denied"] }
      return
    end
    data = params.permit(:email, :name, :password, :organisation_id)
    user = Employer.new(
      name: data[:name],
      email: data[:email],
      password: data[:password],
      organisation_id: data[:organisation_id]
    )
    if user.save!
      render json: { saved: true, record: user }
    else
      render json: { saved: false, errors: user.errors.full_messages }
    end
  end

  def get_sign_in_token
    user = User.find(params[:id])
    authorize! :manage, user
    tokens = {
      target_token: user.generate_sign_in_token,
      return_token: current_user.generate_sign_in_token,
    }
    render json: tokens
  end

  private

  def user_params
    params.permit(
      :uid,
      :alias,
      :email,
      :name,
      :school_id,
      :last_activity_at,
      :last_sign_out_at,
      :updated_at,
      :created_at,
      :last_sign_in_at,
      :login_count,
      :fileboy_image_id,
      :identifier,
      :dob,
      :gender,
      :ethnicity,
      :location,
      :job_title,
      :working_days,
      :specified_class,
      :specified_year,
      :lead_source,
      :points,
      :is_school_admin,
      :password,
      :years_of_experience,
      :education,
      :linked_in_url,
      :cv_fileboy_id,
      :job_hunter_interests,
      :preferred_location,
      :has_career_profile,
      :white_label_organisation_id,
      :organisation_id,
      :can_create_lessons,
      :questionnaire_taken,
      :science_lead,
      work_experience: [:employer, :jobTitle, :startDate, :endDate],
      preferred_career_ids: [],
      interest_tags: [],
    )
  end
end
