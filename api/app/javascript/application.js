// Configure your import map in config/importmap.rb. Read more: https://github.com/rails/importmap-rails

import "controllers";
import Rails from "@rails/ujs";

window.copyContent = function (e, el, name) {
  e.preventDefault();
  navigator.clipboard.writeText(name).then(
    () => {
      if (el.classList.contains("fa-copy")) {
        el.classList.toggle("fa-copy");
        el.classList.toggle("fa-check");
      }
      showToast(`${name} copied to clipboard`);
    },
    () => {
      console.error(`failed to copy ${name}`);
    }
  );
};

window.toggleGlossaryFav = function (e, el, id, remove = false) {
  e.preventDefault();

  fetch(`/glossary/${id}/toggle_favourite`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "X-CSRF-Token": document.querySelector("meta[name='csrf-token']").content,
    },
  })
    .then((response) => {
      response.json().then((data) => {
        if (data.is_being_added) {
          showToast("Added to favourites");
        }
      });
      console.log(`${id} added/removed`);
      el.classList.toggle("fa-solid");
      el.classList.toggle("fa-regular");

      const parentDiv = el.closest(".glossary-container");
      if (parentDiv && remove) {
        parentDiv.parentNode.removeChild(parentDiv);
      }
    })
    .catch((error) => {
      console.error("Error: ", error);
    });
};

window.showToast = function (messageText, duration = 3000) {
  const container = document.getElementById("toast-container");
  const toast = document.createElement("div");
  const message = document.createElement("div"); // Create a div for the message
  const closeIcon = document.createElement("i");
  const progressBar = document.createElement("div");

  toast.classList.add("toast");
  message.classList.add("message"); // Apply class for styling if needed
  closeIcon.classList.add("fas", "fa-times", "close-icon");
  progressBar.classList.add("progress-bar");

  message.textContent = messageText; // Set the text inside the message div
  toast.appendChild(message); // Add the message div to the toast
  toast.appendChild(closeIcon);
  toast.appendChild(progressBar);

  // Close toast when the icon is clicked
  closeIcon.addEventListener("click", () => {
    toast.style.opacity = "0";
    setTimeout(() => container.removeChild(toast), 500); // Sync with CSS opacity transition
  });

  // Add the toast to the container
  container.appendChild(toast);

  // Show the toast and start the progress bar animation
  setTimeout(() => {
    toast.classList.add("show");
    progressBar.style.width = "100%";
    progressBar.style.transition = `width ${duration}ms linear`;
  }, 100);

  // Listen for the progress bar to finish, then start fade out
  progressBar.addEventListener("transitionend", () => {
    toast.style.opacity = "0";
    setTimeout(() => {
      if (container.contains(toast)) {
        container.removeChild(toast);
      }
    }, 500); // Ensure this matches the CSS opacity transition time
  });
};

document.addEventListener("DOMContentLoaded", () => {
  const colors = [
    "bg-red-500",
    "bg-green-500",
    "bg-blue-500",
    "bg-pink-500",
    "bg-yellow-500",
  ];
  const shapes = ["rounded-none", "rounded-full", "rounded-lg"];

  const randomizeDiv = (div) => {
    // Apply random color
    const randomColor = colors[Math.floor(Math.random() * colors.length)];
    div.classList.add(randomColor);

    // Apply random shape
    const randomShape = shapes[Math.floor(Math.random() * shapes.length)];
    div.classList.add(randomShape);

    // Apply random rotation
    const randomRotation = Math.floor(Math.random() * 360); // Random rotation in degrees
    div.style.transform = `rotate(${randomRotation}deg)`;

    // Add common size and transition classes
    div.classList.add(
      "w-12",
      "h-12",
      "transition-transform",
      "duration-500",
      "mb-8"
    );
  };

  // Select all elements with the class "random-div"
  const divs = document.querySelectorAll(".shape-gen");
  divs.forEach((div) => randomizeDiv(div));
});

document.addEventListener("DOMContentLoaded", () => {
  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        entry.target.classList.add("opacity-100");
        entry.target.classList.remove("opacity-0");
        observer.unobserve(entry.target); // Stop observing once it animates
      }
    });
  });

  // Observe all elements with data-observe
  const observeElements = document.querySelectorAll(".data-observe");
  observeElements.forEach((el) => observer.observe(el));
});

document.addEventListener("DOMContentLoaded", function () {
  const tabs = document.querySelectorAll(".tab-heading");
  const tabContents = document.querySelectorAll(".tab-content");

  tabs.forEach((tab) => {
    tab.addEventListener("click", () => {
      // Remove active state from all tabs and contents
      tabs.forEach((t) => t.classList.remove("active-tab-heading"));
      tabContents.forEach((content) => content.classList.remove("active"));

      // Add active state to clicked tab and corresponding content
      tab.classList.add("active-tab-heading");
      const tabName = tab.getAttribute("data-tab");
      const activeContent = document.querySelector(
        `.tab-content[data-tab="${tabName}"]`
      );
      activeContent.classList.add("active");
    });
  });
});

document.addEventListener("DOMContentLoaded", () => {
  new Swiper(".swiper-container", {
    loop: true, // Enable looping
    slidesPerView: 1, // Adjust for responsiveness
    spaceBetween: 12, // Space between slides
    breakpoints: {
      640: {
        slidesPerView: 4,
        spaceBetween: 12,
      },
      1024: {
        slidesPerView: 4,
        spaceBetween: 12,
      },
    },
    navigation: {
      nextEl: ".swiper-button-next",
      prevEl: ".swiper-button-prev",
    },
  });
});

document.addEventListener("DOMContentLoaded", () => {
  const form = document.getElementById("package-builder-form");

  function calculatePrice() {
    const formData = new FormData(form);
    const queryParams = new URLSearchParams(formData).toString();
    const url = `${form.action}?${queryParams}`;

    fetch(url, {
      method: "GET",
      headers: {
        Accept: "application/json",
      },
    })
      .then((response) => response.json())
      .then((data) => {
        const priceElement = document.getElementById("price");
        priceElement.innerText = `£${data.price}`;
        
        // Display discount information if applicable
        const discountElement = document.getElementById("discount-info");
        if (discountElement) {
          if (data.discount && data.discount > 0) {
            discountElement.innerText = `(${data.discount}% discount applied)`;
            discountElement.classList.remove("hidden");
          } else {
            discountElement.classList.add("hidden");
          }
        }
      })
      .catch((error) => {
        console.error("There was a problem with the fetch operation:", error);
      });
  }

  if (form) {
    // Add event listener for form changes
    form.addEventListener("change", () => {
      calculatePrice();
    });

    // Run calculatePrice on page load
    calculatePrice();
  }
});

document.addEventListener("DOMContentLoaded", () => {
  document.querySelectorAll("[data-is-drop-down] > button").forEach((button) => {
    const dropdown = button.nextElementSibling;

    if (!dropdown) {
      return
    }

    // Initialize Popper.js
    const popperInstance = Popper.createPopper(button, dropdown, {
      placement: button.dataset.placement || "bottom-end",
      modifiers: [
        {
          name: "offset",
          options: {
            offset: [0, 8],
          },
        },
      ],
    });

    button.addEventListener("click", () => {
      const isExpanded = button.getAttribute("aria-expanded") === "true";
      button.setAttribute("aria-expanded", !isExpanded);
      dropdown.classList.toggle("hidden", isExpanded);

      if (!isExpanded) {
        popperInstance.update();
      }
    });

    // Close the dropdown when clicking outside
    document.addEventListener("click", (event) => {
      if (dropdown && !button.contains(event.target) && !dropdown.contains(event.target)) {
        button.setAttribute("aria-expanded", "false");
        dropdown.classList.add("hidden");
      }
    });
  });
});

window.togglePasswordVisibility = function (btn) {
  const passwordField = document.getElementById(btn.dataset.passwordId);
  const icon = btn.querySelector("i");

  if (passwordField.type === "password") {
    passwordField.type = "text";
    icon.classList.remove("fa-eye");
    icon.classList.add("fa-eye-slash");
  } else {
    passwordField.type = "password";
    icon.classList.remove("fa-eye-slash");
    icon.classList.add("fa-eye");
  }
};
window.togglePasswordVisibility = function (btn) {
  const passwordField = document.getElementById(btn.dataset.passwordId);
  const icon = btn.querySelector("i");

  if (passwordField.type === "password") {
    passwordField.type = "text";
    icon.classList.remove("fa-eye");
    icon.classList.add("fa-eye-slash");
  } else {
    passwordField.type = "password";
    icon.classList.remove("fa-eye-slash");
    icon.classList.add("fa-eye");
  }
};

///////////////////////////////
//
// EXTERNAL LINK CONFIRMATION
//
// Modal functions for right side panel links
window.handleExternalLinkConfirmation = function (element) {
  const link = element.dataset.link;
  const dialog = document.getElementById("external-link-confirmation");
  const linkPathEl = dialog.querySelector('[name="link-path"]');
  const confirmButton = dialog.querySelector('[data-name="confirm-action"]');

  console.log(link);

  linkPathEl.textContent = link;

  confirmButton.dataset.userId = element.dataset.user_id;
  confirmButton.dataset.templateId = element.dataset.template_id;
  confirmButton.dataset.linkType = element.dataset.link_type;
  confirmButton.dataset.lessonId = element.dataset.lesson_id;
  confirmButton.dataset.targetUrl = link;
  dialog.showModal();
};
window.handleGenericExternalLinkConfirmation = function (element, link) {
  const dialog = document.getElementById("external-link-confirmation");
  const linkPathEl = dialog.querySelector('[name="link-path"]');
  const confirmButton = dialog.querySelector('[data-name="confirm-action"]');

  console.log(link);

  linkPathEl.textContent = link;
  confirmButton.dataset.targetUrl = link;
  dialog.showModal();
};
// Event delegation for confirm button
document.addEventListener("DOMContentLoaded", function () {
  const dialog = document.getElementById("external-link-confirmation");

  if (!dialog) {
    return;
  }

  const confirmButton = dialog.querySelector('[data-name="confirm-action"]');

  confirmButton.addEventListener("click", function () {
    const targetUrl = this.dataset.targetUrl;
    if (targetUrl) {
      if(confirmButton.dataset.templateId) {
        const params = new URLSearchParams({
          userId: confirmButton.dataset.userId,
          templateId: confirmButton.dataset.templateId,
          linkType: confirmButton.dataset.linkType,
          lessonId: confirmButton.dataset.lessonId,
          targetUrl: confirmButton.dataset.targetUrl,
        })
        fetch(`/events/link_view?${params.toString()}`, { method: "GET" })
      }
      window.open(targetUrl, "_blank");
      dialog.close();
    }
  });
});
//
///////////////////////////////////////
//
// SET UP VIDEO PLAYER
//
///////////////////////////////////////
window.videoView = function (id) {
  console.log("video view", id);
  fetch(`/video/${id}`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
  });
};

window.handleVideoHover = function (element) {
  // Only track the view once per session
  if(element.dataset.shouldTrack === "false") {
    return;
  }
  if (element.dataset.videoViewTracked === "false" && element.dataset.shouldTrack !== "false") {
    const videoId = element.dataset.videoId || element.dataset.videoFileboyId || element.dataset.videoUrl;
    videoView(videoId);
    element.dataset.videoViewTracked = "true";
  }
};

window.handleLoadFileboyDownloadLinks = async function () {
  // caching the video download paths so we can skip this if they click it more than once.
  window.fileboy_video_info = window.fileboy_video_info || {};

  const elements = Array.from(
    document.querySelectorAll("[data-video-download]")
  );

  async function getFileboyDownloadUrl(videoId, element) {
    if (window.fileboy_video_info[videoId]) {
      return window.fileboy_video_info[videoId];
    }
    try {
      element.disabled = true;
      if (element.classList.contains("btn")) {
        // only add btn-disabled if the element has a btn class so we don't cause unforeseen styling
        // issues for non btns
        element.classList.add("btn-disabled");
      }
      const response = await fetch(`/videos/download/${videoId}`);
      const url = await response.text();
      if (url) {
        window.fileboy_video_info[videoId] = url;
        return url;
      }
      return null;
    } catch (e) {
      console.error("Video Download error", e);
      return null;
    } finally {
      element.disabled = false;
      element.classList.remove("btn-disabled");
    }
  }

  for (const element of elements) {
    const videoId = element.dataset.videoDownload;
    if (!videoId) {
      continue;
    }
    const downloadPath = await getFileboyDownloadUrl(videoId, element);
    if (!downloadPath) {
      console.error("Video Download error no path for:", videoId);
      element.style.display = "none";
      continue;
    }
    element.style.display = "";
    element.setAttribute("href", downloadPath + "?download=true");
  }
};
document.addEventListener("DOMContentLoaded", function () {
  handleLoadFileboyDownloadLinks();
});

window.bindVideoViewTimer = async function (container) {
  if(container.dataset.shouldTrack === "false") {
    return;
  }
  if (container.getAttribute("data-timer-bound")) {
    return;
  }
  const videoId = container.dataset.videoId || container.dataset.videoFileboyId || container.dataset.videoUrl;
  container.setAttribute("data-timer-bound", "true");
  // Bind the film views handling
  if (!container) {
    return;
  }
  let trackingData = {};
  try {
    trackingData = JSON.parse(container.getAttribute("data-properties"));
  } catch (e) {
    return;
  }

  console.log("Bind timer to", videoId, "for data", trackingData);
  let iteration = 0;
  const videoElement = await new Promise((resolve) => {
    const interval = setInterval(() => {
      const videoElement = container?.querySelector("video");
      if (videoElement) {
        clearInterval(interval);
        resolve(videoElement);
      }
      iteration++;
      if (iteration > 10) {
        clearInterval(interval);
        resolve(null);
      }
    }, 1000);
  });
  if (!videoElement) {
    console.error(videoId, "Unable to locate video element");
    return;
  }
  console.log("Bound", videoId, "timer");
  let watchTime = 0;

  videoElement.addEventListener("timeupdate", function (event) {
    const time = Math.floor(videoElement.currentTime);
    if (time > watchTime) {
      watchTime = time;
    }
  });

  function handleEnd() {
    console.log("Video end");
    if (!trackingData.lesson_id) {
      console.error("No lesson id for video", videoId);
    }

    if (watchTime === 0) {
      return;
    }

    const submittedWatchTime = videoElement.getAttribute("data-submitted");

    if (submittedWatchTime && Number(submittedWatchTime) >= watchTime) {
      // Prevent this event from being fired multiple times by opening/closing dialogs
      return;
    }

    videoElement.setAttribute("data-submitted", watchTime);

    const duration = videoElement.duration;
    const dataValues = {
      ...trackingData,
      time_viewing: watchTime,
      film_duration: duration,
    }
    // delete nulls/undefineds so we dont send them up as strings of 'null' etc
    Object.keys(dataValues).forEach(key => {
      if (dataValues[key] == null || dataValues[key] == undefined) {
        delete dataValues[key]
      }
    })
    const params = new URLSearchParams(dataValues);

    //
    fetch("/events/film-view" + "?" + params.toString(), {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "X-CSRF-Token": document.querySelector('meta[name="csrf-token"]')
          .content,
      },
      keepalive: true, // required for beforeunload event to work
    });
  }

  videoElement.addEventListener("ended", handleEnd);
  // videoElement.addEventListener("pause", handleEnd)
  window.addEventListener("beforeunload", handleEnd);
  if (trackingData.dialog) {
    videoElement.closest("dialog").addEventListener("close", handleEnd);
  }
};

///////////////////////////////
//
// VIDEO LOOP
//
// Make videos loop and add auto play attribute
// will attempt to set the video loop 10 times with a 1 second delay incase the video element is not loaded yet
function setVideoLoop(container, iteration = 0) {
  if(!container) {
      return;
  }
  const videoElement = container.querySelector("video")
  if(videoElement) {
      videoElement.setAttribute("loop", true)
  } else {
      if (iteration < 10) {
          setTimeout(() => setVideoLoop(container, iteration + 1), 1000)
      }
  }
}
window.handleVideoLooping = function () {
  const loopVideoContainers = Array.from(document.querySelectorAll(`[data-loop="true"]`))
  for(const videoContainer of loopVideoContainers) {
      setVideoLoop(videoContainer, 0)
  }
}

window.bindModalVideOnClose = function(element) {
  const dialog = element.closest("dialog")
  const mounted = element.hasAttribute("data-close-mounted")
  if(mounted) {
    return
  }
  if(dialog) {
    element.setAttribute("data-close-mounted", "true")
    dialog.addEventListener("close", () => {
      const video = element.querySelector("video")
      if(video) {
        video.pause()
      }
    })
  }
}

window.handleCampaignLinkFollow = function(event, id, userId) {
  event.preventDefault();
  window.location.href = event.target.href || event.target.parentElement.href
  fetch(`/events/campaign/${id}/click?user_id=${userId}`, {
    method: "GET",
  })
}

document.addEventListener('DOMContentLoaded', () => {
  const dialog = document.getElementById('flagMediaDialog');
  if (!dialog) return;

  const form = dialog.querySelector('[data-flag-form]');
  const cancelBtn = dialog.querySelector('[data-close]');
  
  if (cancelBtn) {
    cancelBtn.addEventListener('click', () => {
      dialog.close();
    });
  }

  form.addEventListener('submit', async (e) => {
    e.preventDefault();
    e.stopPropagation();

    const opts = dialog.reportOptions;
    if (!opts) {
      console.error("Report options not found on dialog!");
      return;
    }

    const selectedReason = form.querySelector('input[name="reason"]:checked');
    if (!selectedReason) {
      window.addFlash("Please select a reason.", "error"); 
      return;
    }

    const formData = new FormData(form);
    formData.append('type', opts.type);
    formData.append('reference', opts.reference);
    if (opts.source) formData.append('source', opts.source);
    if (opts.id) formData.append('id', opts.id);

    try {
      const response = await fetch(form.action, {
        method: form.method,
        headers: {
          'X-CSRF-Token': formData.get('authenticity_token'),
          'Accept': 'application/json',
        },
        body: formData
      });

      dialog.close();

      if (response.ok) {
        document.querySelectorAll(`[data-reportable='${opts.reference}']`).forEach(el => el.remove());
        showToast("This image has been flagged for review.");
      } else {
        const errorData = await response.json();
        console.error("Failed to flag media:", errorData);
        showToast(errorData.error || "An error occurred.");
      }
    } catch (error) {
      console.error("Network or JS error:", error);
      dialog.close();
      showToast("A network error occurred.");
    }
  });

  cancelBtn.addEventListener('click', () => {
    dialog.close();
  });
});
console.log("Application JS loaded");
window.reportMedia = (event, opts) => {
  event.stopPropagation();
  event.preventDefault();
  
  const dialog = document.getElementById('flagMediaDialog');
  if (!dialog) {
    console.error("Flag Media Dialog not found!");
    return;
  }
  
  const form = dialog.querySelector('[data-flag-form]');
  const subjectReason = dialog.querySelector('#flagReasonSubject');
  
  dialog.reportOptions = opts;

  form.reset();
  if (opts.source) {
    subjectReason.classList.remove('hidden');
    subjectReason.classList.add('flex');
  } else {
    subjectReason.classList.add('hidden');
    subjectReason.classList.remove('flex');
  }

  dialog.showModal();
};

/**
 * Traverses up the DOM tree to find a child element with the specified selector
 * @param {HTMLElement} node - Starting node to traverse from
 * @param {string} selector - CSS selector to search for
 * @returns {HTMLElement|null} - Found element or null if not found
 */
window.findChildNodeInParentTree = function(node, selector) {
  let parent = node;
  let depth = 0;
  let selected = null;
  
  while (!selected && depth < 4) {
    parent = parent.parentElement;
    selected = parent.querySelector(selector);
    depth++;
  }
  
  if (!selected) {
    console.warn("Could not find a parent element with selector: " + selector);
  }
  
  return selected;
}
// ===================================
// VIDEO PREVIEW - these are modified versions of the video preview code from units.html.erb 
// - for use with simple_video_field
// ===================================
/**
 * Builds a div that will trigger the preview video to load
 * @param {Object} args - Video configuration arguments
 * @param {string} uniqId - Unique identifier for the video container
 * @returns {HTMLElement} - The video loader element
 */
window.appJsBuildPreviewTemplate = function(args, uniqId) {
  const containerId = "#preview-video-container-" + uniqId;
  const previewContainer = document.querySelector(containerId);
  console.log("CONTAINER", uniqId, containerId, previewContainer);
  previewContainer.innerHTML = '';
  
  const div = document.createElement('div');
  div.id = 'preview-video';
  const authenticityToken = document.querySelector('meta[name="csrf-token"]').content;
  div.setAttribute("hx-headers", `{"X-Csrf-Token": "${authenticityToken}"}`);
  div.setAttribute('hx-post', "/videos/from_data");
  div.setAttribute('hx-vals', JSON.stringify(args));
  div.setAttribute('hx-trigger', 'load_preview');
  div.setAttribute('hx-target', containerId);
  div.setAttribute('hx-swap', 'innerHTML');
  div.setAttribute('form', 'none');
  
  if (args.fileboy_video_id || args.source == 'fileboy') {
    div.setAttribute('hx-on', 'htmx:afterRequest: if (event.target === this) { fb.reloadVideoPlayers() }');
  }
  
  return div;
}
/**
 * Handles video field preview creation and updates
 * @param {HTMLElement} node - Field within the video fields panel
 */
window.appJsHandleVideoFieldPreview = function(node) {
  const uniqId = findChildNodeInParentTree(node, '[data-uniq-id]').getAttribute('data-uniq-id');
  const loader = document.querySelector('#preview-video-loader');
  const scope = node.name.split("[")[0]
  const videoSource = findChildNodeInParentTree(node, `[name="${scope}[source]"]`)?.value;
  const externalVideoId = findChildNodeInParentTree(node, `[name="${scope}[external_id]"]`)?.value;

  const videoLoader = appJsBuildPreviewTemplate({
    source: videoSource, 
    external_id: externalVideoId,
  }, uniqId);

  if (videoLoader && loader) {
    loader.innerHTML = '';
    loader.appendChild(videoLoader);
    htmx.process(videoLoader);
    htmx.trigger(videoLoader, 'load_preview');
  }
}

Rails.start();
