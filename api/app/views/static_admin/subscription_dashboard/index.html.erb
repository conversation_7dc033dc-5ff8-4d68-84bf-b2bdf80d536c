<!-- app/views/static_admin/subscription_dashboard/index.html.erb -->
<% content_for :title, "Subscription Dashboard" %>
<div class="subscription-dashboard">
  <div class="mb-6">
    <div>
      <h1 class="text-2xl font-bold text-white">Subscription Dashboard</h1>
      <p class="text-gray-200">Manage and monitor subscription activity</p>
    </div>
  </div>
  <!-- Quick Stats -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
    <div class="bg-white rounded-lg shadow p-4">
      <h3 class="text-sm font-medium text-gray-500">Active Subscribers</h3>
      <p class="text-2xl font-bold text-indigo-600"><%= @stats[:active_subscribers] %></p>
    </div>
    <div class="bg-white rounded-lg shadow p-4">
      <h3 class="text-sm font-medium text-gray-500">Past Due</h3>
      <p class="text-2xl font-bold text-orange-600"><%= @stats[:past_due] %></p>
    </div>
    <div class="bg-white rounded-lg shadow p-4">
      <h3 class="text-sm font-medium text-gray-500">Cancelled</h3>
      <p class="text-2xl font-bold text-gray-600"><%= @stats[:canceled] %></p>
    </div>
    <div class="bg-white rounded-lg shadow p-4">
      <h3 class="text-sm font-medium text-gray-500">Total Subscribers</h3>
      <p class="text-2xl font-bold text-blue-600"><%= @stats[:total] %></p>
    </div>
  </div>
  <!-- Navigation Cards -->
  <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
    <%= link_to subscribers_admin_subscription_dashboard_index_path, class: "block bg-white rounded-lg shadow p-4 hover:bg-gray-50 transition duration-150" do %>
      <div class="flex items-center">
        <div class="rounded-md bg-indigo-50 p-3 mr-4">
          <svg class="h-6 w-6 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
        </div>
        <div>
          <h3 class="text-lg font-medium text-gray-900">Subscriber Management</h3>
          <p class="text-sm text-gray-500">View and manage all subscribers</p>
        </div>
      </div>
    <% end %>
    <% if @current_user&.beta_feature_enabled?(:aug_18) %>
      <%= link_to analytics_admin_subscription_dashboard_index_path, class: "block bg-white rounded-lg shadow p-4 hover:bg-gray-50 transition duration-150" do %>
        <div class="flex items-center">
          <div class="rounded-md bg-green-50 p-3 mr-4">
            <svg class="h-6 w-6 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <div>
            <h3 class="text-lg font-medium text-gray-900">Analytics</h3>
            <p class="text-sm text-gray-500">Subscription metrics and trends</p>
          </div>
        </div>
      <% end %>
    <% end %>
    <%= link_to admin_stripe_events_path, class: "block bg-white rounded-lg shadow p-4 hover:bg-gray-50 transition duration-150" do %>
      <div class="flex items-center">
        <div class="rounded-md bg-blue-50 p-3 mr-4">
          <svg class="h-6 w-6 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        </div>
        <div>
          <h3 class="text-lg font-medium text-gray-900">Webhook Events</h3>
          <p class="text-sm text-gray-500">Monitor and manage Stripe events</p>
        </div>
      </div>
    <% end %>
    <%= link_to admin_discount_codes_path, class: "block bg-white rounded-lg shadow p-4 hover:bg-gray-50 transition duration-150" do %>
      <div class="flex items-center">
        <div class="rounded-md bg-yellow-50 p-3 mr-4">
          <svg class="h-6 w-6 text-yellow-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <div>
          <h3 class="text-lg font-medium text-gray-900">Discount Codes</h3>
          <p class="text-sm text-gray-500">Create and manage discount codes</p>
        </div>
      </div>
    <% end %>
    <%= link_to test_emails_admin_subscription_dashboard_index_path, class: "block bg-white rounded-lg shadow p-4 hover:bg-gray-50 transition duration-150" do %>
      <div class="flex items-center">
        <div class="rounded-md bg-purple-50 p-3 mr-4">
          <svg class="h-6 w-6 text-purple-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        </div>
        <div>
          <h3 class="text-lg font-medium text-gray-900">Test Email Templates</h3>
          <p class="text-sm text-gray-500">Preview all subscription email templates</p>
        </div>
      </div>
    <% end %>

    <%= link_to sync_status_admin_subscription_dashboard_index_path, class: "block bg-white rounded-lg shadow p-4 hover:bg-gray-50 transition duration-150" do %>
      <div class="flex items-center">
        <div class="rounded-md bg-blue-50 p-3 mr-4">
          <svg class="h-6 w-6 text-purple-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        </div>
        <div>
          <h3 class="text-lg font-medium text-gray-900">Sync Status</h3>
          <p class="text-sm text-gray-500">Monitor and manage stripe syncing</p>
        </div>
      </div>
    <% end %>
  </div>
  <!-- Two Column Layout for Recent Activity and Alerts -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Recent Activity -->
    <div class="bg-white rounded-lg shadow">
      <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
        <h3 class="text-lg font-medium leading-6 text-gray-900">Recent Activity</h3>
      </div>
      <div class="px-4 py-3">
        <% if @recent_subscribers.any? %>
          <h4 class="text-sm font-medium text-gray-500 mb-3">New Subscribers</h4>
          <ul class="divide-y divide-gray-200">
            <% @recent_subscribers.each do |subscriber| %>
              <li class="py-3">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm font-medium text-gray-900"><%= subscriber.name %></p>
                    <p class="text-sm text-gray-500"><%= subscriber.email %></p>
                  </div>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                               <%= status_badge_color(subscriber.subscription_status) %>">
                    <%= subscriber.subscription_status&.humanize || 'Unknown' %>
                  </span>
                </div>
                <p class="text-xs text-gray-500 mt-1">
                  Added <%= time_ago_in_words(subscriber.created_at) %> ago
                </p>
              </li>
            <% end %>
          </ul>
        <% else %>
          <p class="text-gray-500 text-sm py-4">No recent subscribers.</p>
        <% end %>
      </div>
      <div class="bg-gray-50 px-4 py-3 text-right sm:px-6">
        <%= link_to "View All Subscribers", subscribers_admin_subscription_dashboard_index_path, class: "text-sm text-indigo-600 hover:text-indigo-900" %>
      </div>
    </div>
    <!-- Alerts and Monitoring -->
    <div class="bg-white rounded-lg shadow">
      <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
        <h3 class="text-lg font-medium leading-6 text-gray-900">System Health</h3>
      </div>
      <div class="px-4 py-3">
        <h4 class="text-sm font-medium text-gray-500 mb-3">Webhook Status</h4>
        <div class="flex items-center justify-between mb-4">
          <div class="flex flex-col">
            <span class="text-2xl font-bold <%= @webhook_health[:critical_errors] > 0 ? 'text-red-600' : 'text-green-600' %>">
              <%= @webhook_health[:critical_errors] %>
            </span>
            <span class="text-sm text-gray-500">Critical Errors</span>
          </div>
          <div class="flex flex-col">
            <span class="text-2xl font-bold <%= @webhook_health[:errors] > 0 ? 'text-orange-600' : 'text-green-600' %>">
              <%= @webhook_health[:errors] %>
            </span>
            <span class="text-sm text-gray-500">Total Errors</span>
          </div>
          <div class="flex flex-col">
            <span class="text-2xl font-bold <%= @webhook_health[:pending_retry] > 0 ? 'text-blue-600' : 'text-green-600' %>">
              <%= @webhook_health[:pending_retry] %>
            </span>
            <span class="text-sm text-gray-500">Pending Retry</span>
          </div>
        </div>
      </div>
      <%= render partial: 'recent_events', locals: {
            events: @recent_events,
            title: "Recent Events",
            link_path: admin_stripe_events_path,
            link_text: "View All Events",
            empty_message: "No recent events."
        } %>
      </div>
    </div>
  </div>
</div>
