<div class="career-path-edit">
  <!-- Header -->
  <div class="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
    <div>
      <%= link_to admin_career_builder_index_path, class: "inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-de-brand mb-2" do %>
        <i class="fa-solid fa-arrow-left mr-2"></i>
        Back to All Career Paths
      <% end %>
      <h1 class="text-2xl font-bold text-white">Edit Career Path</h1>
      <p class="text-gray-200">
        <%= @career_path.career_name %> • <%= @career_path.education_level_short %>
      </p>
    </div>
    <div class="mt-4 sm:mt-0 flex space-x-3">
      <% if @career_path.status == 'completed' %>
        <%= link_to show_v2_career_builder_path(id: @career_path.id), target: "_blank", class: "inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-de-brand" do %>
          <i class="fa-duotone fa-external-link-alt mr-2"></i>
          View Live
        <% end %>
      <% end %>
    </div>
  </div>

  <!-- Status Banner -->
  <% status_class = case @career_path.status
       when 'completed' then 'bg-green-50 text-green-800 border-green-300'
       when 'generating' then 'bg-yellow-50 text-yellow-800 border-yellow-300'
       else 'bg-gray-50 text-gray-800 border-gray-300'
     end
  %>
  <div class="rounded-md border <%= status_class %> px-4 py-3 mb-6">
    <div class="flex">
      <div class="flex-shrink-0">
        <% if @career_path.status == 'completed' %>
          <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
        <% elsif @career_path.status == 'generating' %>
          <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
          </svg>
        <% else %>
          <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
          </svg>
        <% end %>
      </div>
    </div>
  </div>

  <%= form_with model: @career_path, url: admin_career_builder_path(@career_path), method: :patch, local: true, class: "space-y-6" do |form| %>
    <% if @career_path.errors.any? %>
      <div class="rounded-md bg-red-50 p-4">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">Please fix the following errors:</h3>
            <div class="mt-2 text-sm text-red-700">
              <ul class="list-disc pl-5 space-y-1">
                <% @career_path.errors.full_messages.each do |message| %>
                  <li><%= message %></li>
                <% end %>
              </ul>
            </div>
          </div>
        </div>
      </div>
    <% end %>

    <!-- Two column layout -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Left Column - Main Content -->
      <div class="lg:col-span-2 space-y-6">
        
        <!-- Basic Information Card -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
            <h3 class="text-lg font-medium leading-6 text-gray-900">Basic Information</h3>
            <p class="mt-1 text-sm text-gray-500">Core details about this career path</p>
          </div>
          <div class="px-4 py-5 sm:p-6 space-y-6">
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <%= form.label :career_name, class: "block text-sm font-medium text-gray-700" %>
                <%= form.text_field :career_name, class: "mt-1 focus:ring-de-brand focus:border-de-brand block w-full shadow-sm sm:text-sm border-gray-300 rounded-md", required: true %>
              </div>
              
              <div>
                <%= form.label :age, class: "block text-sm font-medium text-gray-700" %>
                <%= form.number_field :age, class: "mt-1 focus:ring-de-brand focus:border-de-brand block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" %>
              </div>
            </div>
          </div>
        </div>

        <!-- Career Info Card -->
        <%= form.fields_for :info, OpenStruct.new(@career_path.info) do |info_form| %>
          <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
              <h3 class="text-lg font-medium leading-6 text-gray-900">Career Information</h3>
              <p class="mt-1 text-sm text-gray-500">Additional details and metadata</p>
            </div>
            <div class="px-4 py-5 sm:p-6 space-y-6">
              <div>
                <%= info_form.label :about, class: "block text-sm font-medium text-gray-700" %>
                <%= info_form.text_area :about, rows: 4, class: "mt-1 focus:ring-de-brand focus:border-de-brand block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" %>
              </div>

              <div>
                <%= info_form.label "Tags (comma separated)", class: "block text-sm font-medium text-gray-700" %>
                <%= info_form.text_field :tags, value: (@career_path.info["tags"]&.join(', ') if @career_path.info["tags"].present?), class: "mt-1 focus:ring-de-brand focus:border-de-brand block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" %>
                <p class="mt-2 text-sm text-gray-500">Enter tags separated by commas</p>
              </div>
            </div>
          </div>

          <!-- Stats Card -->
          <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
              <div class="flex justify-between items-center">
                <div>
                  <h3 class="text-lg font-medium leading-6 text-gray-900">Career Statistics</h3>
                  <p class="mt-1 text-sm text-gray-500">Key statistics about this career</p>
                </div>
                <button type="button" id="add-stat-btn" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                  <i class="fa-solid fa-plus mr-2"></i>
                  Add Stat
                </button>
              </div>
            </div>
            <div class="px-4 py-5 sm:p-6">
              <div id="stats-container" class="space-y-4">
                <% 3.times do |i| %>
                  <% stat = @career_path.info["stats"][i] if @career_path.info["stats"] %>
                  <div id="stats-<%= i %>" class="stat-row border border-gray-200 rounded-lg p-4 <%= 'hidden' if stat.blank? || stat['title'].blank? %>" data-index="<%= i %>">
                    <div class="flex justify-between items-start mb-3">
                      <h4 class="text-sm font-medium text-gray-900">Stat <%= i + 1 %></h4>
                      <button type="button" class="remove-stat text-red-600 hover:text-red-800" onclick="removeStat('stats-<%= i %>')">
                        <i class="fa-solid fa-times"></i>
                      </button>
                    </div>
                    <div class="space-y-3">
                      <div>
                        <label class="block text-xs font-medium text-gray-500">Title</label>
                        <input type="text" name="career_path[info][stats][<%= i %>][title]" value="<%= stat&.dig('title') %>" class="mt-1 focus:ring-de-brand focus:border-de-brand block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" placeholder="e.g., Average Salary">
                      </div>
                      <div>
                        <label class="block text-xs font-medium text-gray-500">Text</label>
                        <textarea name="career_path[info][stats][<%= i %>][text]" rows="2" class="mt-1 focus:ring-de-brand focus:border-de-brand block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" placeholder="e.g., £45,000 per year"><%= stat&.dig('text') %></textarea>
                      </div>
                    </div>
                  </div>
                <% end %>
              </div>
            </div>
          </div>

          <!-- Related Careers Card -->
          <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
              <div class="flex justify-between items-center">
                <div>
                  <h3 class="text-lg font-medium leading-6 text-gray-900">Related Careers</h3>
                  <p class="mt-1 text-sm text-gray-500">Careers related to this path</p>
                </div>
                <button type="button" id="add-related-btn" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                  <i class="fa-solid fa-plus mr-2"></i>
                  Add Related Career
                </button>
              </div>
            </div>
            <div class="px-4 py-5 sm:p-6">
              <div id="related-container" class="space-y-4">
                <% 3.times do |i| %>
                  <% career = @career_path.info["relatedCareers"][i] if @career_path.info["relatedCareers"] %>
                  <div id="related-<%= i %>" class="related-row border border-gray-200 rounded-lg p-4 <%= 'hidden' if career.blank? || career['name'].blank? %>" data-index="<%= i %>">
                    <div class="flex justify-between items-start mb-3">
                      <h4 class="text-sm font-medium text-gray-900">Related Career <%= i + 1 %></h4>
                      <button type="button" class="remove-related text-red-600 hover:text-red-800" onclick="removeRelated('related-<%= i %>')">
                        <i class="fa-solid fa-times"></i>
                      </button>
                    </div>
                    <div class="space-y-4">
                      <%= render "shared/form_fields/image_field",
                          form: info_form,
                          field_name: "relatedCareers[#{i}][fileboy_image_id]",
                          current_image_id: career&.dig('fileboy_image_id').presence || nil,
                          label: "Career Image", 
                          preview_size: "200x_",
                          image_style: "cover",
                          uniq_id: "related_career_#{i}",
                          image_field: "relatedCareers[#{i}][image]",
                          image_url: career&.dig("image"),
                          reportable: {
                            type: 'image',
                            source: 'CareerPath',
                            id: @career_path.id,
                          }
                      %>
                      <div>
                        <label class="block text-xs font-medium text-gray-500">Career Name</label>
                        <input type="text" name="career_path[info][relatedCareers][<%= i %>][name]" value="<%= career&.dig('name') %>" class="career-name mt-1 focus:ring-de-brand focus:border-de-brand block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" placeholder="e.g., Data Scientist">
                      </div>
                      <div>
                        <label class="block text-xs font-medium text-gray-500">Description</label>
                        <textarea name="career_path[info][relatedCareers][<%= i %>][description]" rows="2" class="mt-1 focus:ring-de-brand focus:border-de-brand block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" placeholder="Brief description of this related career"><%= career&.dig('description') %></textarea>
                      </div>
                    </div>
                  </div>
                <% end %>
              </div>
            </div>
          </div>

          <!-- Further Careers Card -->
          <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
              <div class="flex justify-between items-center">
                <div>
                  <h3 class="text-lg font-medium leading-6 text-gray-900">Further Careers</h3>
                  <p class="mt-1 text-sm text-gray-500">Additional career opportunities</p>
                </div>
                <button type="button" id="add-further-btn" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                  <i class="fa-solid fa-plus mr-2"></i>
                  Add Further Career
                </button>
              </div>
            </div>
            <div class="px-4 py-5 sm:p-6">
              <div id="further-container" class="space-y-4">
                <% 3.times do |i| %>
                  <% career = @career_path.info["further_careers"][i] if @career_path.info["further_careers"] %>
                  <div id="further-<%= i %>" class="further-row border border-gray-200 rounded-lg p-4 <%= 'hidden' if career.blank? || career['name'].blank? %>" data-index="<%= i %>">
                    <div class="flex justify-between items-start mb-3">
                      <h4 class="text-sm font-medium text-gray-900">Further Career <%= i + 1 %></h4>
                      <button type="button" class="remove-further text-red-600 hover:text-red-800" onclick="removeFurther('further-<%= i %>')">
                        <i class="fa-solid fa-times"></i>
                      </button>
                    </div>
                    <div class="space-y-4">
                      <%= render "shared/form_fields/image_field",
                          form: info_form,
                          field_name: "further_careers[#{i}][fileboy_image_id]",
                          current_image_id: career&.dig('fileboy_image_id').presence || nil,
                          label: "Career Image", 
                          preview_size: "200x_",
                          image_style: "cover",
                          uniq_id: "further_career_#{i}",
                          image_field: "further_careers[#{i}][image]",
                          image_url: career&.dig("image"),
                          reportable: {
                            type: 'image',
                            source: 'CareerPath',
                            id: @career_path.id,
                          }
                      %>
                      <div>
                        <label class="block text-xs font-medium text-gray-500">Career Name</label>
                        <input type="text" name="career_path[info][further_careers][<%= i %>][name]" value="<%= career&.dig('name') %>" class="career-name mt-1 focus:ring-de-brand focus:border-de-brand block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" placeholder="e.g., Research Scientist">
                      </div>
                      <div>
                        <label class="block text-xs font-medium text-gray-500">Description</label>
                        <textarea name="career_path[info][further_careers][<%= i %>][description]" rows="2" class="mt-1 focus:ring-de-brand focus:border-de-brand block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" placeholder="Brief description of this further career"><%= career&.dig('description') %></textarea>
                      </div>
                    </div>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        <% end %>

        <!-- Career Path Main Content -->
        <% if @career_path.career_path != [] %>
          <%= form.fields_for :career_path, OpenStruct.new(@career_path.career_path) do |career_form| %>
            <!-- Career Image Card -->
            <div class="bg-white shadow rounded-lg">
              <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
                <h3 class="text-lg font-medium leading-6 text-gray-900">Main Career Image</h3>
                <p class="mt-1 text-sm text-gray-500">Primary image for the career path</p>
              </div>
              <div class="px-4 py-5 sm:p-6">
                <%= render "shared/form_fields/image_field",
                    form: career_form,
                    field_name: :fileboy_image_id,
                    current_image_id: @career_path.career_path&.dig('fileboy_image_id').presence || nil,
                    label: "Career Image", 
                    preview_size: "400x_",
                    image_style: "cover",
                    uniq_id: "career_path_main",
                    image_field: :image,
                    image_url: @career_path.career_path&.dig("image"),
                    reportable: {
                      type: 'image',
                      source: 'CareerPath',
                      id: @career_path.id,
                    }
                %>
              </div>
            </div>

            <!-- Career Description Card -->
            <div class="bg-white shadow rounded-lg">
              <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
                <h3 class="text-lg font-medium leading-6 text-gray-900">Career Description</h3>
                <p class="mt-1 text-sm text-gray-500">Main description of the career</p>
              </div>
              <div class="px-4 py-5 sm:p-6">
                <%= career_form.label :description, class: "block text-sm font-medium text-gray-700" %>
                <%= career_form.text_area :description, rows: 6, class: "mt-1 focus:ring-de-brand focus:border-de-brand block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" %>
              </div>
            </div>

            <!-- Career Path Stages -->
            <% if @career_path.career_path&.dig("careerPath") %>
              <% @career_path.career_path["careerPath"].each_with_index do |stage, i| %>
                <div class="bg-white shadow rounded-lg">
                  <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
                    <h3 class="text-lg font-medium leading-6 text-gray-900">Stage <%= i + 1 %></h3>
                    <p class="mt-1 text-sm text-gray-500">Career path stage details</p>
                  </div>
                  <div class="px-4 py-5 sm:p-6 space-y-6">
                    <%= career_form.fields_for "careerPath[#{i}]", OpenStruct.new(stage) do |stage_form| %>
                      <div class="grid grid-cols-1 gap-6 sm:grid-cols-3">
                        <div>
                          <%= stage_form.label :title, "Stage Title", class: "block text-sm font-medium text-gray-700" %>
                          <%= stage_form.text_field :title, class: "mt-1 focus:ring-de-brand focus:border-de-brand block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" %>
                        </div>
                        <div>
                          <%= stage_form.label :emoji, class: "block text-sm font-medium text-gray-700" %>
                          <%= stage_form.text_field :emoji, class: "mt-1 focus:ring-de-brand focus:border-de-brand block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" %>
                        </div>
                        <div>
                          <%= stage_form.label :keyword, class: "block text-sm font-medium text-gray-700" %>
                          <%= stage_form.text_field :keyword, class: "mt-1 focus:ring-de-brand focus:border-de-brand block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" %>
                        </div>
                      </div>

                      <!-- Steps -->
                      <% if stage["steps"] %>
                        <div class="border-t border-gray-200 pt-6">
                          <h4 class="text-base font-medium text-gray-900 mb-4">Steps</h4>
                          <div class="space-y-6">
                            <% stage["steps"].each_with_index do |step, j| %>
                              <%= stage_form.fields_for "steps[#{j}]", OpenStruct.new(step) do |step_form| %>
                                <div class="border border-gray-200 rounded-lg p-4">
                                  <h5 class="text-sm font-medium text-gray-900 mb-4">Step <%= j + 1 %></h5>
                                  <div class="space-y-4">
                                    <%= render "shared/form_fields/image_field",
                                        form: step_form,
                                        field_name: :fileboy_image_id,
                                        current_image_id: step&.dig('fileboy_image_id').presence || nil,
                                        label: "Step Image", 
                                        preview_size: "200x_",
                                        image_style: "cover",
                                        uniq_id: "step_#{i}_#{j}",
                                        image_field: :image,
                                        image_url: step&.dig("image"),
                                        reportable: {
                                          type: 'image',
                                          source: 'CareerPath',
                                          id: @career_path.id,
                                        }
                                    %>
                                    <div>
                                      <%= step_form.label :title, class: "block text-sm font-medium text-gray-700" %>
                                      <%= step_form.text_field :title, class: "mt-1 focus:ring-de-brand focus:border-de-brand block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" %>
                                    </div>
                                    <div>
                                      <%= step_form.label :description, class: "block text-sm font-medium text-gray-700" %>
                                      <%= step_form.text_area :description, rows: 3, class: "mt-1 focus:ring-de-brand focus:border-de-brand block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" %>
                                    </div>
                                    <div>
                                      <%= step_form.label :furtherInfo, "Further Info", class: "block text-sm font-medium text-gray-700" %>
                                      <%= step_form.text_area :furtherInfo, rows: 3, class: "mt-1 focus:ring-de-brand focus:border-de-brand block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" %>
                                    </div>
                                  </div>
                                </div>
                              <% end %>
                            <% end %>
                          </div>
                        </div>
                      <% end %>
                    <% end %>
                  </div>
                </div>
              <% end %>
            <% end %>
          <% end %>
        <% end %>

        <!-- Videos Card -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
            <div class="flex justify-between items-center">
              <div>
                <h3 class="text-lg font-medium leading-6 text-gray-900">Career Videos</h3>
                <p class="mt-1 text-sm text-gray-500">Videos related to this career path</p>
              </div>
              <span class="px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                <span id="video-count"><%= @career_path.videos.count %></span> <%= "video".pluralize(@career_path.videos.count) %>
              </span>
            </div>
          </div>
          <div class="px-4 py-5 sm:p-6">
            <div id="video-fields-container">
              <!-- Always render a single video container - otherwise we lose access to the
              initVideoScript required to add additional videos -->
              <div data-video-field>
                <%= render 'shared/form_fields/video_select_field', 
                    form: form, 
                    field_name: "video_ids[#{0}]",
                    current_video_id: @career_path.video_ids.sort.first,
                    label: "Select Videos",
                    uniq_id: "career_path_videos",
                    multiple: true %>
              </div>
              <!-- Skip the first video as its always rendered above -->
              <% (@career_path.video_ids&.sort&.[](1..-1) || []).each_with_index do |id, index| %>
                <div data-video-field>
                  <%= render 'shared/form_fields/video_select_field', 
                      form: form, 
                      field_name: "video_ids[#{index+1}]",
                      current_video_id: id,
                      label: "Select Videos",
                      uniq_id: "career_path_videos",
                      multiple: true %>
                </div>
              <% end %>
            </div>
            <button type="button" id="add-video-btn" class="mt-2 px-3 py-2 bg-blue-600 text-white rounded">Add video</button>
            <template id="video-field-template">
              <div data-video-field>
                <%= render 'shared/form_fields/video_select_field', 
                    form: form, 
                    field_name: "video_ids[__INDEX__]",
                    current_video_id: nil,
                    label: "Select Videos",
                    uniq_id: "career_path_videos___INDEX__",
                    multiple: true %>
              </div>
            </template>
            <script>
              document.addEventListener('DOMContentLoaded', function() {
                const addVideoBtn = document.getElementById('add-video-btn');
                const videoFieldsContainer = document.getElementById('video-fields-container');
                const videoFieldTemplate = document.getElementById('video-field-template').innerHTML;

                addVideoBtn.addEventListener('click', function() {
                  const index = videoFieldsContainer.querySelectorAll('[data-video-field]').length;
                  const newFieldHtml = videoFieldTemplate
                    .replace(/__INDEX__/g, index);
                  const wrapper = document.createElement('div');
                  wrapper.innerHTML = newFieldHtml;
                  videoFieldsContainer.appendChild(wrapper.firstElementChild);

                  // Give it a chance to load
                  setTimeout(() => {
                    const internalIdField = videoFieldsContainer.children[videoFieldsContainer.children.length-1].querySelector('[data-internal-id]')
                    const internalId = internalIdField.id
                    const modal_id_internal = `video-search-modal-${internalId}`


                    initVideoSelectField(internalId, modal_id_internal);
                  }, 100)
                });
              });
            </script>
          </div>
        </div>
      </div>

      <!-- Right Column - Metadata and Actions -->
      <div class="lg:col-span-1 space-y-6">
        <!-- Status Card -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
            <h3 class="text-lg font-medium leading-6 text-gray-900">Career Path Status</h3>
          </div>
          <div class="px-4 py-5 sm:p-6">
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-gray-900">Current Status</span>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= @career_path.status == 'completed' ? 'bg-green-100 text-green-800' : @career_path.status == 'generating' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800' %>">
                  <%= @career_path.status&.capitalize %>
                </span>
              </div>

              <% if @career_path.created_at %>
                <div>
                  <dt class="text-sm font-medium text-gray-500">Created On</dt>
                  <dd class="mt-1 text-sm text-gray-900"><%= @career_path.created_at.strftime("%B %d, %Y at %I:%M %p") %></dd>
                </div>
              <% end %>
            </div>
          </div>
        </div>

        <!-- Metadata Card -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
            <h3 class="text-lg font-medium leading-6 text-gray-900">Metadata</h3>
          </div>
          <div class="px-4 py-5 sm:p-6">
            <dl class="space-y-4">
              <div>
                <dt class="text-sm font-medium text-gray-500">Career ID</dt>
                <dd class="mt-1 text-sm text-gray-900 font-mono"><%= @career_path.id %></dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Education Level</dt>
                <dd class="mt-1 text-sm text-gray-900"><%= @career_path.education_level_display %></dd>
              </div>
              <% if @career_path.job_family %>
                <div>
                  <dt class="text-sm font-medium text-gray-500">Job Family</dt>
                  <dd class="mt-1 text-sm text-gray-900">
                    <%= link_to @career_path.job_family.name, edit_admin_job_family_path(@career_path.job_family), class: "text-de-brand hover:text-de-brand-dark" %>
                  </dd>
                </div>
              <% end %>
            </dl>
          </div>
        </div>

        <!-- Save Actions Card -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <div class="space-y-3">
              <%= form.submit "Save Changes", class: "w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-de-brand hover:bg-de-brand-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-de-brand" %>
              
              <%= link_to admin_career_builder_index_path, class: "w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-de-brand" do %>
                Cancel
              <% end %>
            </div>
          </div>
        </div>

        <!-- Sibling CareerPaths -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
            <h3 class="text-lg font-medium leading-6 text-gray-900">Sibling Careers</h3>
          </div>
          <div class="px-4 py-5 sm:p-6 flex flex-col w-full gap-4">
            <% @related_career_paths.includes(:job_family).each do |path| %>
              <div class="border border-gray-200 rounded-lg p-4">
                <dl class="space-y-4">
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Career ID</dt>
                    <dd class="mt-1 text-sm text-gray-900 font-mono">
                      <%= link_to path.id.to_s, edit_admin_career_builder_path(path), class: "text-de-brand hover:text-de-brand-dark"  %>
                    </dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Education Level</dt>
                    <dd class="mt-1 text-sm text-gray-900"><%= path.education_level_display %></dd>
                  </div>
                  <% if path.job_family %>
                    <div>
                      <dt class="text-sm font-medium text-gray-500">Job Family</dt>
                      <dd class="mt-1 text-sm text-gray-900">
                        <%= link_to path.job_family.name, edit_admin_job_family_path(path.job_family), class: "text-de-brand hover:text-de-brand-dark" %>
                      </dd>
                    </div>
                  <% end %>
                </dl>
              </div>
            <% end %>
          </div>
          <div class="px-4 py-5 border-t border-gray-200 sm:px-6">
            <button onclick="handleDisplayUpdateJobFamily()" type="button" class="inline-flex items-center px-2.5 py-1.5 border border-blue-300 text-sm font-medium rounded text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              <i class="fa-solid fa-pen-to-square mr-1"></i>Bulk Update Job Family
            </button>
            <button onclick="handleDisplayUpdateImage()" type="button" class="inline-flex items-center px-2.5 py-1.5 border border-blue-300 text-sm font-medium rounded text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              <i class="fa-solid fa-pen-to-square mr-1"></i>Bulk Update Primary Image
            </button>
          </div>
        </div>
        <div class="bg-white shadow rounded-lg flex flex-col gap-2">
          <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
            <h3 class="text-lg font-medium leading-6 text-gray-900">Regenerate</h3>
          </div>
          <div class="p-6 flex flex-col gap-2">
            <button id="regenerate-btn" onclick="disableButtonAndRegenerateCareerPathV2(<%= @career_path.id %>)" class="w-full flex items-center justify-center gap-2 bg-gradient-to-r from-blue-600 to-cyan-500 hover:from-blue-700 hover:to-cyan-600 text-white px-4 py-2 rounded-lg font-semibold shadow transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-400">
              <i class="fa fa-sync-alt"></i>
              This Education Level
            </button>
            <button id="regenerate-all-btn" onclick="disableButtonAndRegenerateCareerPathV2(<%= @career_path.id %>, true)" class="w-full flex items-center justify-center gap-2 bg-gradient-to-r from-purple-600 to-blue-500 hover:from-purple-700 hover:to-blue-600 text-white px-4 py-2 rounded-lg font-semibold shadow transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-400">
              <i class="fa fa-layer-group"></i>
              Regenerate all Education Levels
            </button>
          </div>
          <script>
            function disableButtonAndRegenerateCareerPathV2(careerPathId, withSiblings = false) {
              const btn = document.getElementById("regenerate-btn");
              const allBtn = document.getElementById("regenerate-all-btn");
              btn.disabled = true;
              allBtn.disabled = true;
              btn.classList.add('opacity-60', 'cursor-not-allowed');
              allBtn.classList.add('opacity-60', 'cursor-not-allowed');

              // Start regeneration request
              fetch(`/career-builder/${careerPathId}/regenerate_career_path_v2`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({ logUUID: crypto.randomUUID(), with_siblings: withSiblings })
              })
                .then(response => response.json())
                .then(data => {
                  if (data.success) {
                    // Start polling for completion
                    console.log('Regeneration started, polling for status...');
                    const poller = new CareerGenerationPoller({
                      careerPathId: careerPathId,
                      userId: <%= @current_user&.id || 'null' %>,
                      onComplete: () => window.location.reload(),
                      onError: () => window.location.reload()
                    });
                    poller.start();
                  } else {
                    btn.disabled = false;
                    allBtn.disabled = false;
                    btn.classList.remove('opacity-60', 'cursor-not-allowed');
                    allBtn.classList.remove('opacity-60', 'cursor-not-allowed');
                    alert('Failed to start regeneration. Please try again.');
                  }
                })
                .catch(e => {
                  btn.disabled = false;
                  allBtn.disabled = false;
                  btn.classList.remove('opacity-60', 'cursor-not-allowed');
                  allBtn.classList.remove('opacity-60', 'cursor-not-allowed');
                  alert('An error occurred. Please try again.');
                });
            }

            class CareerGenerationPoller {
                constructor(options = {}) {
                    this.careerPathId = options.careerPathId;
                    this.userId = options.userId;
                    this.pollInterval = options.pollInterval || 2000; // 2 seconds
                    this.maxAttempts = options.maxAttempts || 150; // 5 minutes max
                    this.onStatusUpdate = options.onStatusUpdate || (() => {});
                    this.onComplete = options.onComplete || (() => {});
                    this.onError = options.onError || (() => {});
                    
                    this.attempts = 0;
                    this.polling = false;
                    this.timeoutId = null;
                }

                start() {
                    if (this.polling) return;
                    
                    this.polling = true;
                    this.attempts = 0;
                    this.poll();
                }

                stop() {
                    this.polling = false;
                    if (this.timeoutId) {
                        clearTimeout(this.timeoutId);
                        this.timeoutId = null;
                    }
                }

                async poll() {
                    if (!this.polling || this.attempts >= this.maxAttempts) {
                        this.stop();
                        if (this.attempts >= this.maxAttempts) {
                            this.onError('Polling timeout - generation taking too long');
                        }
                        return;
                    }

                    this.attempts++;

                    try {
                        const response = await fetch(`/career-builder/career_path_status_v2?career_path_id=${this.careerPathId}&user_id=${this.userId}`, {
                            method: 'GET',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-Requested-With': 'XMLHttpRequest'
                            }
                        });

                        const data = await response.json();

                        if (data.success) {
                            this.onStatusUpdate(data);

                            if (data.status === 'completed') {
                                this.stop();
                                this.onComplete(data);
                                return;
                            } else if (data.status === 'error' || data.status === 'flagged') {
                                this.stop();
                                this.onError(data.message || 'Generation failed');
                                return;
                            }
                        } else {
                            console.warn('Status check failed:', data.message);
                        }

                    } catch (error) {
                        console.error('Error polling status:', error);
                    }

                    // Schedule next poll
                    if (this.polling) {
                        this.timeoutId = setTimeout(() => this.poll(), this.pollInterval);
                    }
                }
            }
          </script>
        </div>
        <%= link_to 'Delete', admin_career_builder_path(@career_path), method: :delete, data: { confirm: 'Are you sure?' }, class: 'admin-btn admin-delete-btn' %>
      </div>
    </div>
  <% end %>
</div>

<script>
  function handleCloseModel(id) {
    const dialog = document.querySelector(`#${id}`);
    dialog.close();
  }
  function handleDisplayUpdateJobFamily() {
    const dialog = document.querySelector('#update_job_family');
    dialog.showModal();
  }
  function handleDisplayUpdateImage() {
    const dialog = document.querySelector('#bulk_update_image');
    dialog.showModal();
  }
</script>

<%= render ModalComponent.new(title: "Update Job Family", external_control: true, id: 'update_job_family') do %>
  <%= form_with(
      url: bulk_update_job_family_admin_career_builder_path(@career_path),
      local: true,
      id: "bulk-update-family-form"
    ) do |f| %>
    <div class="space-y-4">
      <%= f.collection_select :job_family_id, 
          JobFamily.all, 
          :id, 
          :name,
          { include_blank: "Select a job family" },
          { class: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2" } 
      %>
      <div class="flex justify-end space-x-2 pb-16">
        <button type="button" class="px-4 py-2 border rounded text-gray-700" onclick="handleCloseModel('update_job_family')">
          Cancel
        </button>
        <button onclick="this.form.submit()" type="button" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">Update</button>
      </div>
    </div>
  <% end %>
<% end %>

<%= render ModalComponent.new(title: "Update Job Family", external_control: true, id: 'bulk_update_image') do %>
  <%= form_with(
      url: bulk_update_primary_image_admin_career_builder_path(@career_path),
      local: true,
      id: "bulk-update-family-form"
    ) do |f| %>
    <div class="space-y-4">
      <%= render "shared/form_fields/image_field",
          form: f,
          field_name: :fileboy_image_id,
          current_image_id: @career_path.career_path&.dig('fileboy_image_id').presence || nil,
          label: "Career Image", 
          preview_size: "400x_",
          image_style: "cover",
          uniq_id: "career_path_main",
          image_field: :image,
          image_url: @career_path.career_path&.dig("image")
      %>
      <div class="flex justify-end space-x-2 pb-16">
        <button type="button" class="px-4 py-2 border rounded text-gray-700" onclick="handleCloseModel('bulk_update_image')">
          Cancel
        </button>
        <button onclick="this.form.submit()" type="button" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">Update</button>
      </div>
    </div>
  <% end %>
<% end %>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const MAX_STATS = 3;
  const MAX_CAREERS = 3;

  // Stats management
  const statsContainer = document.getElementById('stats-container');
  const addStatBtn = document.getElementById('add-stat-btn');

  function updateStatsVisibility() {
    const visibleStats = Array.from(statsContainer.children).filter(div => !div.classList.contains('hidden'));
    if (visibleStats.length >= MAX_STATS) {
      addStatBtn.classList.add('hidden');
    } else {
      addStatBtn.classList.remove('hidden');
    }
  }

  function removeStat(id) {
    const section = document.getElementById(id);
    const inputs = section.querySelectorAll("input, textarea");
    inputs.forEach(field => {
      field.value = "";
    });
    section.classList.add("hidden");
    updateStatsVisibility();
  }

  function addStat() {
    const firstHidden = statsContainer.querySelector('.hidden');
    if (firstHidden) {
      firstHidden.classList.remove('hidden');
    }
    updateStatsVisibility();
  }

  // Related careers management
  const relatedContainer = document.getElementById('related-container');
  const addRelatedBtn = document.getElementById('add-related-btn');

  function updateRelatedVisibility() {
    const visibleRelated = Array.from(relatedContainer.children).filter(div => !div.classList.contains('hidden'));
    if (visibleRelated.length >= MAX_CAREERS) {
      addRelatedBtn.classList.add('hidden');
    } else {
      addRelatedBtn.classList.remove('hidden');
    }
  }

  function removeRelated(id) {
    const section = document.getElementById(id);
    const inputs = section.querySelectorAll("input, textarea");
    inputs.forEach(field => {
      field.value = "";
    });
    section.classList.add("hidden");
    updateRelatedVisibility();
  }

  function addRelated() {
    const firstHidden = relatedContainer.querySelector('.hidden');
    if (firstHidden) {
      firstHidden.classList.remove('hidden');
    }
    updateRelatedVisibility();
  }

  // Further careers management
  const furtherContainer = document.getElementById('further-container');
  const addFurtherBtn = document.getElementById('add-further-btn');

  function updateFurtherVisibility() {
    const visibleFurther = Array.from(furtherContainer.children).filter(div => !div.classList.contains('hidden'));
    if (visibleFurther.length >= MAX_CAREERS) {
      addFurtherBtn.classList.add('hidden');
    } else {
      addFurtherBtn.classList.remove('hidden');
    }
  }

  function removeFurther(id) {
    const section = document.getElementById(id);
    const inputs = section.querySelectorAll("input, textarea");
    inputs.forEach(field => {
      field.value = "";
    });
    section.classList.add("hidden");
    updateFurtherVisibility();
  }

  function addFurther() {
    const firstHidden = furtherContainer.querySelector('.hidden');
    if (firstHidden) {
      firstHidden.classList.remove('hidden');
    }
    updateFurtherVisibility();
  }

  // Event listeners
  addStatBtn.addEventListener('click', addStat);
  addRelatedBtn.addEventListener('click', addRelated);
  addFurtherBtn.addEventListener('click', addFurther);

  // Make functions globally available for onclick handlers
  window.removeStat = removeStat;
  window.removeRelated = removeRelated;
  window.removeFurther = removeFurther;

  // Initialize visibility
  updateStatsVisibility();
  updateRelatedVisibility();
  updateFurtherVisibility();
});
</script>
