<!DOCTYPE html>
<html lang="<%= I18n.locale.presence || 'en' %>">
<head>
  <title><%= page_title %></title>
  <% if content_for?(:hreflang_tags) %>
    <%= yield(:hreflang_tags) %>
  <% end %>
  <%= csrf_meta_tags %>
  <%= csp_meta_tag %>

  <%= javascript_importmap_tags %>
  <%= stylesheet_link_tag 'application' %>
  <%= javascript_include_tag 'application', type: "module" %>
  <%= javascript_include_tag "application", "data-turbo-track": "reload" %>
  <%= stylesheet_link_tag "tailwind", "inter-font" %>

  <script defer src="https://kit.fontawesome.com/7fae4ae66d.js" crossorigin="anonymous"></script>
  <script defer src="https://cdn.jsdelivr.net/npm/htmx.org@1.9.6"></script>
  <script src="https://analytics.ahrefs.com/analytics.js" data-key="bJUnpJ5re7xSm/pN4cVtqQ" async></script>
  
  <!-- Google Analytics -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-99JG1WS1Z7"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() {
      dataLayer.push(arguments);
    }
    gtag('js', new Date());
    gtag('config', 'G-99JG1WS1Z7');
  </script>
  <%= render './layouts/common_header' %>
</head>

<body hx-headers='{"X-Csrf-Token": "<%= form_authenticity_token %>"}' class="bg-dark-blue">
  <%= render 'layouts/anon_header/header' %>
  <div class="bg-dark-blue overflow-x-auto">
    <%= render 'shared/flash_banner' %>
    <div id="preview-video-loader" style="display: none;"></div>
    <%= yield %>

    <%= render 'shared/flag_media_dialog' %>
    <%= render '/htmx_components/footer' %>
  </div>
  
  <div id="toast-container"></div>
  <div hx-trigger="load" hx-get="/dev_toolbar" hx-swap="outerHtml" hx-target="this"></div>
  <% unless @current_user %>
    <div id="universal-signup-popup" class="hidden fixed bottom-0 sm:bottom-6 right-0 sm:right-4 w-full sm:w-96 rounded-t-2xl sm:rounded-2xl shadow-2xl z-50 p-6 animate-fade-in font-sans bg-gradient-to-r from-purple-500 to-blue-500">

  <button onclick="dismissPopup()" class="absolute top-2 right-3 text-white hover:text-gray-300 text-xl leading-none">×</button>

  <div class="hidden sm:block">
    <%= image_tag 'planet_with_rocket_svg_school_banner.svg', alt: 'Planet with Rocket SVG School Banner', class: 'mb-4 w-full h-40 object-cover rounded-lg' %>
  </div>

  <h2 class="text-2xl font-bold text-white mb-2 nova">Start Your Free Trial</h2>
  <p class="text text-white mb-4 text-base">Unlock expert-designed lessons, resources, and assessments tailored for educators. No credit card required.</p>

  <%= link_to "Claim Your Free Trial →", marketing_tracking_link_path("static_popup", accounts_new_path), class: "btn btn-base btn-white w-full text-center" %>
</div>

<script>
  function showPopup() {
    console.log ("showPopup called");
    const path = window.location.pathname;

    // Don't show on any page starting with /accounts
    if (path.startsWith("/accounts")) {
      console.log("Popup not shown on accounts page.");
      return;
    }

    if (!localStorage.getItem("signupPopupDismissed")) {
      document.getElementById("universal-signup-popup").classList.remove("hidden");
    } else {
      console.log("Popup dismissed, not showing again.");
    }
  }

  function dismissPopup() {
    document.getElementById("universal-signup-popup").classList.add("hidden");
    localStorage.setItem("signupPopupDismissed", "true");
  }

  setTimeout(showPopup, 2000);
</script>

<style>
  @keyframes fade-in {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
  }
  .animate-fade-in {
    animation: fade-in 0.4s ease-out forwards;
  }
</style>
  <% end %>

  <script defer src="https://cdn.jsdelivr.net/npm/@cd2/fileboy-browser@0.12.1-alpha.7"></script>
</body>
</html>
