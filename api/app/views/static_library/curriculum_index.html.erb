<% content_for :title, "Unit and Lesson Library" %>
<% content_for :meta_description, "Browse the Developing Experts Unit & Lessons Library and explore our available science and geography lessons." %>
<% set_seo_tags(unit_library_url) %>
<div class="py-12 px-4">
  <div class="max-w-screen-xl mx-auto space-y-8">
    <% if @current_user&.beta_feature_enabled?(:aug_4) %>
      <%= render TeacherStandardTitleAreaComponent.new(
        title: "Unit & Lessons Library", 
        subtitle: 'Browse our library of lessons. You can use the tiles below to filter our library by curriculum, unit and year. You can also search for lessons by keyword or filter by subject by viewing "All Lessons".', 
        icon: "fa-solid fa-book-open",
        help_button: {
          title: "Unit & Lessons Library",
          body: 'Browse our library of lessons. You can use the tiles below to filter our library by curriculum, unit and year. You can also search for lessons by keyword or filter by subject by viewing "All Lessons".',
          id: 5340
        },
        layout: :float
      ) %>
    <% else %>
      <div class="text-white">
        <div class="flex flex-wrap gap-2 items-start justify-between">
          <h1 class="text-4xl">Unit & Lessons Library</h1>
          <%= render HelpButtonComponent.new(
            title: "Unit & Lessons Library",
            body: 'Browse our library of lessons. You can use the tiles below to filter our library by curriculum, unit and year. You can also search for lessons by keyword or filter by subject by viewing "All Lessons".',
            id: 5340
          ) %>
        </div>
        <p class="text-lg">The library contains all of your available lessons. Use the tiles below to select your
          curriculum</p>
      </div>
    <% end %>
    <div class="grid grid-cols-fill-16 gap-4 mx-auto">
      <%- @curricula.each do |curriculum| %>
        <%- subjects = curriculum.subjects.pluck(:id, :name) %>
        <%= render CardComponent.new(title: curriculum.name, image_background_fileboy_id: curriculum.fileboy_image_id ) do |card| %>
          <%= card.with_footer do %>
            <%- multiple_subjects_present = subjects.count > 1 %>
            <div class="flex gap-2 flex-wrap">
              <% subjects.each do |subject_id, subject_name| %>
                <%= render ButtonComponent::TextView.new(
                  text: multiple_subjects_present ? subject_name : "View",
                  url: "/unit-library/curriculum/#{curriculum.id}?subject_id=#{subject_id}",
                ) %>
              <% end %>
            </div>
          <% end %>
        <% end %>
      <% end %>
      <%- if @current_user&.teacher? %>
        <%= render CardComponent.new(
          title: "School lesson library",
          image_background_fileboy_id: "3d77f012-00dc-42b1-ba2b-bd45c31218ab",
          path: "/s/library/community")
        %>
      <% end %>
    </div>
    <div class="bg-mid-blue text-white p-8 rounded-xl">
      <h2 class="text-2xl">All lessons</h2>
      <p class="mb-4">Want to see the whole library? Click here to search for individual lessons.</p>
      <a href="/library">
        <button class="btn btn-cyan btn-base">
          View all lessons
        </button>
      </a>
    </div>
  </div>
</div>