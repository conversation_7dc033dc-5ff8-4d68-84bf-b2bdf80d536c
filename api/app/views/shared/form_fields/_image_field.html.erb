<%# app/views/shared/form_fields/_image_field.html.erb %>
<%# 
  Partial for image upload and selection with Pexels integration
  
  Parameters:
  - form: The form object
  - field_name: The name of the field (default: :fileboy_image_id)
  - current_image_id: The current fileboy image ID (if any)
  - label: The field label (default: "Image")
  - preview_size: Size for the image preview (default: "200x200")
  - required: Whether the field is required (default: false)
%>
<% 
  field_name_param = local_assigns.fetch(:field_name, :fileboy_image_id)
  current_image_id = local_assigns.fetch(:current_image_id, form.object.try(field_name))
  image_field_name = local_assigns.fetch(:image_field, nil)
  image_url = local_assigns.fetch(:image_url, nil)
  label = local_assigns.fetch(:label, "Image")
  preview_size = local_assigns.fetch(:preview_size, "200x_")
  required = local_assigns.fetch(:required, false)
  uniq_id_param = local_assigns.fetch(:uniq_id)
  image_style = local_assigns.fetch(:image_style, 'cover')
  reportable = local_assigns.fetch(:reportable, nil)
  
  field_id = "#{form.object_name}_#{field_name}_#{uniq_id}"
  modal_id = "pexels-modal-#{field_id}"

  # Sanitize field_name when constructing field_id to remove characters problematic for CSS selectors
  # Replace non-alphanumeric characters (except underscore) with an underscore
  sanitized_field_name_part = field_name_param.to_s.gsub(/[^a-zA-Z0-9_]/, '_')

  # Construct unique and sanitized IDs for elements within this partial instance
  # Using 'internal' to denote these are specific to the scope of this partial's ERB rendering
  field_id_internal = local_assigns.fetch(:field_id_internal, "#{form.object_name}_#{sanitized_field_name_part}_#{uniq_id_param}".gsub(/__+/, '_'))  # Avoid double underscores
  modal_id_internal = local_assigns.fetch(:modal_id_internal, "pexels-modal-#{field_id_internal}")
%>
<div
  class="image-field-container mb-4 max-w-[440px] border rounded-lg p-4"
  name="image-field-container"
  data-reportable-config="<%= reportable.to_json if reportable.present? %>"
  <%= 'data-required=true' if required %>
>
  <% if reportable.present? %>
    <template name="reportable-image-template">
      <%= render ReportMediaButtonComponent.new(
        type: reportable[:type] || 'image', 
        source: reportable[:source], 
        id: reportable[:id], 
        reference: '__IMAGE_REFERENCE_TO_REPLACE__',
        class_name: "flex items-center justify-center aspect-1 w-7 absolute top-2 right-10 bg-black/40 cursor-pointer text-white rounded-full shadow hover:bg-black/80"
      ) do %>
        <i class="fa fa-flag text-sm"></i>
      <% end %>
    </template>
  <% end %>
  <div class="flex justify-between items-center mb-2">
    <%= form.label field_name, "#{label}", class: "block text-sm font-medium text-gray-700", required: required %>
    <div class="flex space-x-2">
      <button type="button" id="upload-btn-<%= field_id_internal %>" class="inline-flex items-center px-2.5 py-1.5 border border-blue-300 text-sm font-medium rounded text-blue-700 bg-blue-50 hover:bg-blue-100 cursor-pointer">
        <i class="fa-solid fa-upload mr-1"></i>Upload
      </button>
      <button type="button" id="search-pexels-<%= field_id_internal %>" class="inline-flex items-center px-2.5 py-1.5 border border-purple-300 text-sm font-medium rounded text-purple-700 bg-purple-50 hover:bg-purple-100">
        <i class="fa-solid fa-search mr-1"></i>Search
      </button>
    </div>
  </div>
  <%# Hidden field to store the fileboy_image_id %>
  <%= form.text_field field_name, id: field_id_internal, value: current_image_id, style: "display: none" %>
  <% reportable_image_path = current_image_id.present? ? "https://www.developingexperts.com/file-cdn/images/get/#{current_image_id}" : image_url.present? ? image_url : nil %>
  <% if image_url.present? && image_field_name.present? %>
    <%= form.hidden_field image_field_name, id: "#{field_id_internal}_image_url", value: image_url %>
  <% end %>
  <%# Image required warning %>
  <% if required %>
    <div id="image-required-warning-<%= field_id_internal %>" class="<%= "hidden" if current_image_id.present? %> mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-yellow-700 text-sm">
      <i class="fa-solid fa-circle-exclamation mr-1"></i>
      <span id="image-required-warning-text-<%= field_id_internal %>">You must select an image.</span>
    </div>
  <% end %>
  <%# Image preview area %>
  <div id="image-preview-<%= field_id_internal %>" class="image-preview mt-4 hover:bg-slate-200">
    <div class="relative inline-block w-full">
      <% if current_image_id.present? %>
        <img 
          src="<%= reportable_image_path %>?transform=resize:<%= preview_size %>" 
          alt="<%= label %>" 
          class="<%= image_style == 'cover' ? 'object-cover' : 'object-contain' %> rounded border border-gray-200 w-full"
          data-reportable="<%= reportable_image_path %>"
        >
      <% elsif image_url.present? %>
        <img 
          src="<%= reportable_image_path %>" 
          alt="<%= label %>" 
          class="<%= image_style == 'cover' ? 'object-cover' : 'object-contain' %> rounded border border-gray-200 w-full"
          data-reportable="<%= reportable_image_path %>"
        >
      <% else %>
        <div class="h-[150px] w-[200px] bg-gray-100 flex items-center justify-center rounded border border-gray-200">
          <i class="fa-regular fa-image text-gray-400 text-3xl"></i>
        </div>
      <% end %>
      <%# Remove button %>
      <% has_image = current_image_id.present? || image_url.present? %>
      <button 
        type="button" 
        id="remove-image-<%= field_id_internal %>"
        class="flex items-center justify-center aspect-1 w-7 absolute top-2 right-2 bg-red-600 text-white rounded-full shadow hover:bg-red-700 <%= has_image ? '' : 'hidden' %>"
        title="Remove image"
        data-reportable="<%= reportable_image_path %>"
      >
        <i class="fa-solid fa-times"></i>
      </button>
      <% if reportable.present? %>
        <%= render ReportMediaButtonComponent.new(
          type: reportable[:type] || 'image', 
          source: reportable[:source], 
          id: reportable[:id], 
          reference: reportable_image_path,
          class_name: "flex items-center justify-center aspect-1 w-7 absolute top-2 right-10 bg-black/40 cursor-pointer text-white rounded-full shadow hover:bg-black/80 #{has_image ? '' : 'hidden'}"
        ) do %>
          <i class="fa fa-flag text-sm"></i>
        <% end %>
      <% end %>
    </div>
    <% if current_image_id.present? %>
      <div class="mt-2 text-sm text-gray-500">
        <span id="image-id-display-<%= field_id_internal %>">
          Image ID: <%= current_image_id %>
        </span>
      </div>
    <% elsif image_url.present? %>
      <div class="mt-2 text-sm text-gray-500" data-reportable="<%= reportable_image_path %>">
        <span id="image-id-display-<%= field_id_internal %>">
          Image URL: <%= image_url %>
        </span>
      </div>
    <% end %>
  </div>
</div>
<%# Inline Modal for Image Search %>
<div id="<%= modal_id_internal %>" class="fixed inset-0 bg-gray-500 bg-opacity-75 z-50 hidden flex items-center justify-center">
  <div class="bg-white rounded-lg p-6 max-w-3xl w-full mx-4 shadow-xl max-h-[90vh] flex flex-col">
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-lg font-medium">Search Images</h3>
      <button type="button" class="text-gray-400 hover:text-gray-500" id="close-modal-<%= field_id_internal %>">
        <span class="sr-only">Close</span>
        <i class="fa-solid fa-times"></i>
      </button>
    </div>
    <div class="mb-4 flex items-center gap-2">
      <input 
        type="text" 
        id="search-input-<%= field_id_internal %>" 
        placeholder="Search for images..."
        class="flex-1 p-2 border border-gray-300 rounded-l"
      >
      <button 
        type="button" 
        id="search-button-<%= field_id_internal %>" 
        class="btn btn-base btn-cyan"
      >
        <i class="fa-solid fa-search"></i>
      </button>
    </div>
    <!-- Tab Navigation -->
    <div class="border-b border-gray-200 mb-4">
      <nav class="-mb-px flex space-x-8" aria-label="Image Sources">
        <button 
          type="button"
          id="tab-internal-<%= field_id_internal %>"
          class="tab-button whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm border-blue-500 text-blue-600"
        >
          Library Images
        </button>
        <button 
          type="button"
          id="tab-pexels-<%= field_id_internal %>"
          class="tab-button whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
        >
          Pexels
        </button>
      </nav>
    </div>
    <!-- Results Container -->
    <div class="flex-1 overflow-scroll">
      <div id="internal-results-<%= field_id_internal %>" class="image-search-tab-content grid grid-cols-2 gap-4 overflow-y-auto p-2 bg-gray-50 rounded">
        <div class="col-span-4 flex justify-center items-center h-32 text-gray-500">
          <div class="text-center">
            <i class="fa-regular fa-image text-3xl mb-2"></i>
            <p>Click search to load images</p>
          </div>
        </div>
      </div>
      <div id="pexels-results-<%= field_id_internal %>" class="image-search-tab-content hidden grid grid-cols-2 gap-4 overflow-y-auto p-2 bg-gray-50 rounded">
        <div class="col-span-4 flex justify-center items-center h-32 text-gray-500">
          <div class="text-center">
            <i class="fa-regular fa-image text-3xl mb-2"></i>
            <p>Search Pexels for images</p>
          </div>
        </div>
      </div>
    </div>
    <div class="mt-4 text-center text-xs text-gray-500">
      <span id="attribution-internal-<%= field_id_internal %>">
        Search and reuse images from your platform
      </span>
      <span id="attribution-pexels-<%= field_id_internal %>" class="hidden">
        <a href="https://www.pexels.com" target="_blank" class="underline">Photos provided by Pexels</a>
      </span>
    </div>
    <div class="mt-4 flex justify-end">
      <button type="button" class="px-4 py-2 border rounded text-gray-700" id="cancel-modal-<%= field_id_internal %>">
        Cancel
      </button>
    </div>
  </div>
</div>
<script>
  function escapeSelector(id) {
    // Escape special characters in ID for CSS selectors
    return id.replace(/([ #;&,.+*~':"!^$[\]()=>|\/@])/g, '\\$1');
  } 
  document.addEventListener('DOMContentLoaded', function() {
    initImageField('<%= field_id_internal %>', '<%= modal_id_internal %>');
  });

  // Initialize when drawer opens (if used within a drawer)
  document.addEventListener('drawer:opened', function(event) {
    // Try to find the specific container within the opened drawer
    const drawThatOpened = event.detail.target
    const thisNode = document.querySelector("#<%= field_id_internal %>")
    // skip callback if we opened a draw that isn't THIS draw
    if(!event.detail.target.contains(thisNode)) {
      return
    }
    console.log("Image field draw opened", "#<%= field_id_internal %>")
    setTimeout(function() {
      initImageField('<%= field_id_internal %>', '<%= modal_id_internal %>');
    }, 100);
  });

  // We need to track if this field has been initialized to avoid duplicate event handlers
  window.initializedImageFields = window.initializedImageFields || {};

  function initImageField(fieldId, modalId) {
    // Check if this field has already been initialized
    if (window.initializedImageFields[fieldId]) {
      return;
    }

    console.log(fieldId, modalId)

    // Get references to elements
    const hiddenField = document.getElementById(fieldId);
    const imageUrlField = document.getElementById(fieldId + '_image_url');
    const previewContainer = document.getElementById('image-preview-' + fieldId);
    const removeButton = document.getElementById('remove-image-' + fieldId);
    const uploadButton = document.getElementById('upload-btn-' + fieldId);
    const searchButton = document.getElementById('search-pexels-' + fieldId);
    const modal = document.getElementById(modalId);
    const closeModalButton = document.getElementById('close-modal-' + fieldId);
    const cancelModalButton = document.getElementById('cancel-modal-' + fieldId);
    const searchInput = document.getElementById('search-input-' + fieldId);
    const searchButtonNew = document.getElementById('search-button-' + fieldId);
    const warningField = document.getElementById('image-required-warning-' + fieldId)

    // Tab elements
    const tabInternal = document.getElementById('tab-internal-' + fieldId);
    const tabPexels = document.getElementById('tab-pexels-' + fieldId);
    const internalResults = document.getElementById('internal-results-' + fieldId);
    const pexelsResults = document.getElementById('pexels-results-' + fieldId);
    const attributionInternal = document.getElementById('attribution-internal-' + fieldId);
    const attributionPexels = document.getElementById('attribution-pexels-' + fieldId);

    let activeTab = 'internal';

    // Store template values as variables to avoid ERB interpolation issues in functions
    const previewSize = '<%= preview_size %>';
    const imageStyle = '<%= image_style %>';
    let reportableConfig = previewContainer.closest(".image-field-container").dataset.reportableConfig || null;
    const reportableTemplate = previewContainer.closest('[name="image-field-container"]').querySelector('[name="reportable-image-template"]')
    if(reportableConfig) {
      try {
        reportableConfig = JSON.parse(reportableConfig);
      } catch (e) {
        console.error('[Image Field] Invalid reportable config JSON:', reportableConfig, e);
        reportableConfig = null;
      }
    }

    if (!hiddenField || !previewContainer) {
      console.error('[Image Field] Required elements not found for image field', fieldId,
                   'Hidden field:', hiddenField, 'Preview container:', previewContainer);

      // Try again in 100ms - might help with timing issues in drawers
      setTimeout(function() {
        const hiddenFieldRetry = document.getElementById(fieldId);
        const previewContainerRetry = document.getElementById('image-preview-' + fieldId);


        if (hiddenFieldRetry && previewContainerRetry) {
          initImageField(fieldId, modalId);
        }
      }, 100);

      return;
    }

    <% if @current_user&.beta_feature_enabled?(:aug_4) %>
      const dragOverStyle = ['ring', 'ring-blue-400', 'bg-blue-50'];
      function addDragStyle() {
        const dropIconId = 'drop-icon-' + fieldId;
        let dropIcon = document.getElementById(dropIconId);
        if (!dropIcon) {
          dropIcon = document.createElement('div');
          dropIcon.id = dropIconId;
          dropIcon.className = 'absolute inset-0 flex items-center justify-center pointer-events-none z-20';
          dropIcon.style.background = 'rgba(255,255,255,0.7)';
          dropIcon.innerHTML = `
            <div class="flex flex-col items-center w-full">
              <i class="fa-solid fa-cloud-arrow-up text-blue-500 text-4xl mb-2"></i>
              <span class="text-blue-700 text-sm font-medium">Drop image here</span>
            </div>
          `;
          dropIcon.style.top = 0;
          dropIcon.style.left = 0;
          dropIcon.style.width = '100%';
          dropIcon.style.height = '100%';
          dropIcon.style.position = 'absolute';
          previewContainer.appendChild(dropIcon);
        }
        dropIcon.style.display = 'flex';
        previewContainer.classList.add('relative')
      }
      function removeDragStyle() {
        const dropIcon = document.getElementById('drop-icon-' + fieldId);
        if (dropIcon) {
          dropIcon.remove();
        }
        previewContainer.classList.remove('relative');
      }

      previewContainer.addEventListener('dragleave', function(ev) {
        removeDragStyle()
      });
      previewContainer.addEventListener('drop', function(ev) {
        removeDragStyle()
        console.log("File(s) dropped");

        // Prevent default behavior (Prevent file from being opened)
        ev.preventDefault();

        if (!ev.dataTransfer.items) {
          return
        }
        const item = ev.dataTransfer.items
        if(!item) {
          return
        }

        // Only handle files
        if (ev.dataTransfer && ev.dataTransfer.files && ev.dataTransfer.files.length > 0) {
          const file = ev.dataTransfer.files[0];
          if (file && file.type.startsWith('image/')) {
            uploadFile(file);
          } else {
            showError('Please drop a valid image file.');
          }
        } else {
          showError('No file detected. Please drop an image file.');
        }
      });
      previewContainer.addEventListener("dragover", function(ev) {
        console.log("File(s) in drop zone");

        // Prevent default behavior (Prevent file from being opened)
        ev.preventDefault();

        addDragStyle()
      });
    <% end %>


    // Mark this field as initialized
    window.initializedImageFields[fieldId] = true;

    // Ensure the initial tab is properly set
    switchTab('internal');

    // Tab switching functionality
    if (tabInternal && tabPexels) {
      tabInternal.addEventListener('click', function() {
        switchTab('internal');
      });

      tabPexels.addEventListener('click', function() {
        switchTab('pexels');
      });
    }

    function switchTab(tab) {
      activeTab = tab;

      // Update tab buttons
      const allTabButtons = document.querySelectorAll('#' + escapeSelector(modalId) + ' .tab-button');

      allTabButtons.forEach(btn => {
        btn.classList.remove('border-blue-500', 'text-blue-600');
        btn.classList.add('border-transparent', 'text-gray-500');
      });

      const activeTabButton = document.getElementById('tab-' + tab + '-' + fieldId);
      if (activeTabButton) {
        activeTabButton.classList.remove('border-transparent', 'text-gray-500');
        activeTabButton.classList.add('border-blue-500', 'text-blue-600');
      }

      // Update content - hide all first
      const allTabContent = document.querySelectorAll('#' + escapeSelector(modalId) + ' .image-search-tab-content');
      allTabContent.forEach(content => {
        content.classList.add('hidden');
      });

      // Show active content
      const activeContent = document.getElementById(tab + '-results-' + fieldId);
      if (activeContent) {
        activeContent.classList.remove('hidden');
      }

      // Update attribution
      if (attributionInternal && attributionPexels) {
        if (tab === 'internal') {
          attributionInternal.classList.remove('hidden');
          attributionPexels.classList.add('hidden');
        } else {
          attributionInternal.classList.add('hidden');
          attributionPexels.classList.remove('hidden');
        }
      }
    }

    // Set up upload button handler - Create file input only once
    if (uploadButton) {
      uploadButton.addEventListener('click', function() {
        // Create a temporary file input if it doesn't exist yet
        let fileInput = document.getElementById('file-input-' + fieldId);

        if (!fileInput) {
          fileInput = document.createElement('input');
          fileInput.type = 'file';
          fileInput.accept = 'image/*';
          fileInput.id = 'file-input-' + fieldId;
          fileInput.style.display = 'none';
          document.body.appendChild(fileInput);

          // Set up the file input change handler
          fileInput.addEventListener('change', function(e) {
            if (this.files && this.files[0]) {
              const file = this.files[0];
              uploadFile(file);
            }
          });
        }

        // Reset the file input to ensure the change event fires even if the same file is selected
        fileInput.value = '';

        // Trigger file selection dialog
        fileInput.click();
      });
    }

    // Set up remove button handler
    if (removeButton) {
      removeButton.addEventListener('click', function() {
        clearImage();
      });
    }

    // Set up search button handler - opens modal and loads initial internal images
    if (searchButton && modal) {
      searchButton.addEventListener('click', function() {
        modal.classList.remove('hidden');
        document.body.classList.add('overflow-hidden');

        // Load initial internal images if not already loaded
        if (internalResults && internalResults.children.length === 0) {
          searchInternalImages('');
        }
      });
    }

    // Set up close/cancel modal handlers
    if (closeModalButton && modal) {
      closeModalButton.addEventListener('click', function() {
        modal.classList.add('hidden');
        document.body.classList.remove('overflow-hidden');
      });
    }

    if (cancelModalButton && modal) {
      cancelModalButton.addEventListener('click', function() {
        modal.classList.add('hidden');
        document.body.classList.remove('overflow-hidden');
      });
    }

    // Close modal when clicking outside the content
    if (modal) {
      modal.addEventListener('click', function(e) {
        if (e.target === modal) {
          modal.classList.add('hidden');
          document.body.classList.remove('overflow-hidden');
        }
      });
    }

    // Set up search functionality
    if (searchButtonNew && searchInput) {
      console.log("SEARCH BTN NEW", searchButtonNew, searchInput)
      searchButtonNew.addEventListener('click', function() {
        const query = searchInput.value;
        if (activeTab === 'internal') {
          searchInternalImages(query);
        } else {
          searchPexels(query);
        }
      });

      searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          e.preventDefault();
          const query = searchInput.value;
          if (activeTab === 'internal') {
            searchInternalImages(query);
          } else {
            searchPexels(query);
          }
        }
      });
    }

    // Function to search internal images
    function searchInternalImages(query) {
      const resultsContainer = document.getElementById('internal-results-' + fieldId);

      if (!resultsContainer) {
        console.error('[Image Field] Internal results container not found:', 'internal-results-' + fieldId);
        return;
      }

      resultsContainer.innerHTML = '<div class="col-span-4 flex justify-center items-center h-32"><div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div></div>';

      const searchUrl = query ?
        '/images/search?query=' + encodeURIComponent(query) :
        '/images/search';

      fetch(searchUrl, {
        headers: {
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
        }
      }).then(response => response.json()).then(data => {
        renderInternalResults(data);
      }).catch(error => {
        console.error('[Image Field] Error fetching internal images:', error);
        resultsContainer.innerHTML = '<div class="col-span-4 text-center text-red-500">Error fetching images. Please try again.</div>';
      });
    }

    // Function to render internal image results
    // Pagination state for internal images
    let internalImagesData = null;
    let internalImagesCurrentPage = 1;
    const internalImagesPerPage = 10;

    function renderInternalResults(data) {
      const resultsContainer = document.getElementById('internal-results-' + fieldId);

      if (!resultsContainer) {
      console.error('[Image Field] Results container not found when rendering');
      return;
      }

      // Save data for pagination
      internalImagesData = data;
      internalImagesCurrentPage = 1;
      renderInternalResultsPage();
    }

    function renderInternalResultsPage() {
      const resultsContainer = document.getElementById('internal-results-' + fieldId);

      if (!resultsContainer) {
        console.error('[Image Field] Results container not found when rendering');
        return;
      }

      const data = internalImagesData;
      if (!data || !data.success || !data.images || data.images.length === 0) {
        resultsContainer.innerHTML = '<div class="col-span-4 text-center">No images found. Try a different search term.</div>';
        return;
      }

      const totalImages = data.images.length;
      const totalPages = Math.ceil(totalImages / internalImagesPerPage);
      const startIdx = (internalImagesCurrentPage - 1) * internalImagesPerPage;
      const endIdx = Math.min(startIdx + internalImagesPerPage, totalImages);
      const imagesToShow = data.images.slice(startIdx, endIdx);

      let html = '';
      imagesToShow.forEach((image, index) => {
        html += '<div class="internal-result relative cursor-pointer hover:opacity-90 transition-opacity border-2 border-transparent hover:border-blue-300 rounded" data-fileboy-id="' + image.fileboy_image_id + '">';
        html += '<img src="' + image.thumbnail_url + '" alt="' + (image.title || 'Internal image') + '" class="w-full object-cover rounded aspect-teaser">';
        html += '<div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1">';
        html += '<div class="truncate font-medium">' + (image.title || 'Untitled') + '</div>';
        html += '<div class="truncate text-xs opacity-75">' + (image.keywords || '') + '</div>';
        html += '</div>';
        html += '</div>';
      });

      // Pagination controls
      if (totalPages > 1) {
        html += '<div class="col-span-2">';
        html +=   '<div class="flex justify-center items-center mt-4 gap-2">';
        html +=     '<button type="button" class="internal-pagination-btn px-2 py-1 rounded border text-sm" data-page="' + (internalImagesCurrentPage - 1) + '" ' + (internalImagesCurrentPage === 1 ? 'disabled' : '') + '>&laquo; Prev</button>';
        html +=     '<span class="mx-2 text-xs text-gray-600">Page ' + internalImagesCurrentPage + ' of ' + totalPages + '</span>';
        html +=     '<button type="button" class="internal-pagination-btn px-2 py-1 rounded border text-sm" data-page="' + (internalImagesCurrentPage + 1) + '" ' + (internalImagesCurrentPage === totalPages ? 'disabled' : '') + '>Next &raquo;</button>';
        html +=   '</div>';
        html += '</div>';
      }

      resultsContainer.innerHTML = html;

      // Add click handlers to results
      const resultElements = document.querySelectorAll('#internal-results-' + escapeSelector(fieldId) + ' .internal-result');
      resultElements.forEach(result => {
        result.addEventListener('click', function() {
          const fileboyId = this.getAttribute('data-fileboy-id');
          if (fileboyId) {
            updateImage(fileboyId);
            modal.classList.add('hidden');
            document.body.classList.remove('overflow-hidden');
          }
        });
      });

      // Add click handlers to pagination buttons
      const paginationBtns = resultsContainer.querySelectorAll('.internal-pagination-btn');
      paginationBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
          const page = parseInt(this.getAttribute('data-page'), 10);
          if (!isNaN(page) && page >= 1 && page <= Math.ceil(internalImagesData.images.length / internalImagesPerPage)) {
            internalImagesCurrentPage = page;
            renderInternalResultsPage();
          }
        });
      });
    }

    // Function to search Pexels
    function searchPexels(query) {
      const resultsContainer = document.getElementById('pexels-results-' + fieldId);

      if (!resultsContainer) {
        console.error('[Image Field] Pexels results container not found');
        return;
      }

      resultsContainer.innerHTML = '<div class="col-span-4 flex justify-center items-center h-32"><div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div></div>';

      fetch('https://api.pexels.com/v1/search?query=' + encodeURIComponent(query) + '&per_page=100', {
        headers: {
          Authorization: "6kLBhp5E4vuUbfywzFJ3AYnYQX9bfb4VzejNZon1EeFPQtMdngl8S4kc"
        }
      }).then(response => response.json()).then(data => {
        renderPexelsResults(data);
      }).catch(error => {
        console.error('[Image Field] Error fetching Pexels images:', error);
        resultsContainer.innerHTML = '<div class="col-span-4 text-center text-red-500">Error fetching images. Please try again.</div>';
      });
    }

    // Function to render Pexels search results
    // Pagination state for Pexels images
    let pexelsImagesData = null;
    let pexelsImagesCurrentPage = 1;
    const pexelsImagesPerPage = 10;

  function renderPexelsResults(data) {
    // Fetch flagged images from the backend (provided as a JSON array of image URLs)
    const flaggedImages = <%= FlaggedImage.all.pluck(:image_id).to_json.html_safe %>;
    console.log("Flagged images:", flaggedImages);
    
    const resultsContainer = document.getElementById('pexels-results-' + fieldId);

    if (!resultsContainer) {
      console.error('[Image Field] Pexels results container not found when rendering');
      return;
    }

    if (!data.photos || data.photos.length === 0) {
      resultsContainer.innerHTML = '<div class="col-span-4 text-center">No images found. Try a different search term.</div>';
      return;
    }

    console.log("Pexels search results before filtering:", data);

    // Filter out flagged images
    const filteredPhotos = data.photos.filter(photo => {
      // Check all possible URL formats in the photo's src object
      const photoUrls = [
        photo.src.original,
        photo.src.large2x,
        photo.src.large,
        photo.src.medium,
        photo.src.small,
        photo.src.portrait,
        photo.src.landscape,
        photo.src.tiny
      ];
      
      // Check if any of the photo URLs match any flagged image URL
      const isFlagged = photoUrls.some(url => flaggedImages.includes(url));
      
      if (isFlagged) {
        console.log(`Filtered out flagged image with ID: ${photo.id}`);
      }
      
      return !isFlagged;
    });

    // Update the data object with filtered photos
    const filteredData = {
      ...data,
      photos: filteredPhotos,
      total_results: data.total_results - (data.photos.length - filteredPhotos.length)
    };

    console.log(`Filtered ${data.photos.length - filteredPhotos.length} flagged images from results`);
    console.log("Pexels search results after filtering:", filteredData);

    // Save filtered data for pagination
    pexelsImagesData = filteredData;
    pexelsImagesCurrentPage = 1;
    renderPexelsResultsPage();
  }

  // Alternative helper function if you want to filter by image ID instead of URL
  function renderPexelsResultsById(data) {
    // If your flagged images are stored by Pexels photo ID instead of URL
    const flaggedImageIds = <%= FlaggedImage.all.pluck(:image_id).to_json.html_safe %>;
    console.log("Flagged image IDs:", flaggedImageIds);
    
    const resultsContainer = document.getElementById('pexels-results-' + fieldId);

    if (!resultsContainer) {
      console.error('[Image Field] Pexels results container not found when rendering');
      return;
    }

    if (!data.photos || data.photos.length === 0) {
      resultsContainer.innerHTML = '<div class="col-span-4 text-center">No images found. Try a different search term.</div>';
      return;
    }

    console.log("Pexels search results before filtering:", data);

    // Filter out flagged images by ID
    const filteredPhotos = data.photos.filter(photo => {
      const isFlagged = flaggedImageIds.includes(photo.id.toString()) || flaggedImageIds.includes(photo.id);
      
      if (isFlagged) {
        console.log(`Filtered out flagged image with ID: ${photo.id}`);
      }
      
      return !isFlagged;
    });

    // Update the data object with filtered photos
    const filteredData = {
      ...data,
      photos: filteredPhotos,
      total_results: data.total_results - (data.photos.length - filteredPhotos.length)
    };

    console.log(`Filtered ${data.photos.length - filteredPhotos.length} flagged images from results`);
    console.log("Pexels search results after filtering:", filteredData);

    // Save filtered data for pagination
    pexelsImagesData = filteredData;
    pexelsImagesCurrentPage = 1;
    renderPexelsResultsPage();
  }

    function renderPexelsResultsPage() {
      const resultsContainer = document.getElementById('pexels-results-' + fieldId);

      if (!resultsContainer) {
        console.error('[Image Field] Pexels results container not found when rendering');
        return;
      }

      const data = pexelsImagesData;
      if (!data || !data.photos || data.photos.length === 0) {
        resultsContainer.innerHTML = '<div class="col-span-4 text-center">No images found. Try a different search term.</div>';
        return;
      }

      const totalImages = data.photos.length;
      const totalPages = Math.ceil(totalImages / pexelsImagesPerPage);
      const startIdx = (pexelsImagesCurrentPage - 1) * pexelsImagesPerPage;
      const endIdx = Math.min(startIdx + pexelsImagesPerPage, totalImages);
      const imagesToShow = data.photos.slice(startIdx, endIdx);

      let html = '';
      imagesToShow.forEach(photo => {
        html += '<div class="pexels-result relative cursor-pointer hover:opacity-90 transition-opacity border-2 border-transparent hover:border-purple-300 rounded" data-url="' + photo.src.original + '" data-photographer="' + photo.photographer + '">';
        html += '<img src="' + photo.src.medium + '" alt="' + (photo.alt || 'Pexels image') + '" class="w-full h-32 object-cover rounded">';
        html += '<div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1 truncate">';
        html += 'by ' + photo.photographer;
        html += '</div>';
        html += '</div>';
      });

      // Pagination controls
      if (totalPages > 1) {
        html += '<div class="col-span-2">';
        html +=   '<div class="flex justify-center items-center mt-4 gap-2">';
        html +=     '<button type="button" class="pexels-pagination-btn px-2 py-1 rounded border text-sm" data-page="' + (pexelsImagesCurrentPage - 1) + '" ' + (pexelsImagesCurrentPage === 1 ? 'disabled' : '') + '>&laquo; Prev</button>';
        html +=     '<span class="mx-2 text-xs text-gray-600">Page ' + pexelsImagesCurrentPage + ' of ' + totalPages + '</span>';
        html +=     '<button type="button" class="pexels-pagination-btn px-2 py-1 rounded border text-sm" data-page="' + (pexelsImagesCurrentPage + 1) + '" ' + (pexelsImagesCurrentPage === totalPages ? 'disabled' : '') + '>Next &raquo;</button>';
        html +=   '</div>';
        html += '</div>';
      }

      resultsContainer.innerHTML = html;

      // Add click handlers to results
      document.querySelectorAll('#pexels-results-' + escapeSelector(fieldId) + ' .pexels-result').forEach(result => {
        result.addEventListener('click', function() {
          const imageUrl = this.getAttribute('data-url');
          if (imageUrl) {
            uploadPexelsImage(imageUrl);
            modal.classList.add('hidden');
            document.body.classList.remove('overflow-hidden');
          }
        });
      });

      // Add click handlers to pagination buttons
      const paginationBtns = resultsContainer.querySelectorAll('.pexels-pagination-btn');
      paginationBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
          const page = parseInt(this.getAttribute('data-page'), 10);
          if (!isNaN(page) && page >= 1 && page <= Math.ceil(pexelsImagesData.photos.length / pexelsImagesPerPage)) {
            pexelsImagesCurrentPage = page;
            renderPexelsResultsPage();
          }
        });
      });
    }

    // Function to upload a file
    function uploadFile(file) {
      showLoading('Uploading image...');

      const formData = new FormData();
      formData.append('file', file);

      fetch('/images/upload_fileboy_image', {
        method: 'POST',
        body: formData,
        headers: {
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
        }
      })
      .then(response => response.json())
      .then(data => {
        if (data.success && data.fileboy_id) {
          updateImage(data.fileboy_id);
        } else {
          showError(data.error || 'Failed to upload image');
        }
      })
      .catch(error => {
        console.error('[Image Field] Error uploading image:', error);
        showError('Failed to upload image. Please try again.');
      });
    }

    // Function to upload a Pexels image
    function uploadPexelsImage(url) {
      showLoading('Downloading and processing image...');

      fetch('/images/process_pexels_image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({ url: url })
      })
      .then(response => response.json())
      .then(data => {
        if (data.success && data.fileboy_id) {
          updateImage(data.fileboy_id);
        } else {
          showError(data.error || 'Failed to process image');
        }
      })
      .catch(error => {
        console.error('[Image Field] Error processing Pexels image:', error);
        showError('Failed to process image. Please try again.');
      });
    }

    // Function to update image preview with a fileboy ID
    function updateImage(fileboyId) {
      hiddenField.value = fileboyId;

      previewContainer.classList.remove('hidden');

      const objectClass = (imageStyle === 'cover') ? 'object-cover' : 'object-contain';
      
      // Build report button HTML if reportable is configured
      let reportButtonHTML = '';
      if (reportableConfig && reportableTemplate) {
        reportButtonHTML = reportableTemplate.innerHTML
          .replace('__IMAGE_REFERENCE_TO_REPLACE__', 'https://www.developingexperts.com/file-cdn/images/get/' + fileboyId);
      }

      previewContainer.innerHTML =
        '<div class="relative inline-block w-full" data-reportable="https://www.developingexperts.com/file-cdn/images/get/' + fileboyId + '">' +
          '<img src="https://www.developingexperts.com/file-cdn/images/get/' + fileboyId + '?transform=resize:' + previewSize + '" alt="Image" class="' + objectClass + ' rounded border border-gray-200 w-full">' +
          '<button type="button" id="remove-image-' + fieldId + '" class="flex items-center justify-center aspect-1 w-7 absolute top-2 right-2 bg-red-600 text-white rounded-full shadow hover:bg-red-700" title="Remove image">' +
            '<i class="fa-solid fa-times"></i>' +
          '</button>' +
          reportButtonHTML +
        '</div>' +
        '<div class="mt-2 text-sm text-gray-500" data-reportable="https://www.developingexperts.com/file-cdn/images/get/' + fileboyId + '">' +
          '<span id="image-id-display-' + fieldId + '">Image ID: ' + fileboyId + '</span>' +
        '</div>';

      // Re-attach remove button handler
      const newRemoveButton = document.getElementById('remove-image-' + fieldId);
      if (newRemoveButton) {
        newRemoveButton.addEventListener('click', function() {
          clearImage();
        });
      }

      if (warningField) {
        warningField.classList.add('hidden');
      }
    }

    // Function to clear the image
    function clearImage() {
      hiddenField.value = '';
      previewContainer.innerHTML = `
        <div class="relative inline-block w-full">
          <div class="h-[150px] w-[200px] bg-gray-100 flex items-center justify-center rounded border border-gray-200">
            <i class="fa-regular fa-image text-gray-400 text-3xl"></i>
          </div>
        </div>
      `
      if(imageUrlField) {
        imageUrlField.value = '';
      }
      previewContainer.classList.add('hidden');

      if (warningField) {
        warningField.classList.remove('hidden');
      }
    }

    // Function to show loading state
    function showLoading(message) {
      previewContainer.classList.remove('hidden');
      previewContainer.innerHTML =
        '<div class="flex items-center justify-center p-4 bg-blue-50 rounded">' +
          '<div class="animate-spin rounded-full h-5 w-5 border-b-2 border-t-2 border-blue-500 mr-3"></div>' +
          '<p>' + (message || 'Loading...') + '</p>' +
        '</div>';
    }

    // Function to show error
    function showError(error) {
      previewContainer.classList.remove('hidden');
      previewContainer.innerHTML =
        '<div class="p-4 bg-red-50 text-red-700 rounded">' +
          '<p>' + error + '</p>' +
          '<p class="text-sm mt-2">Please try again or choose a different image.</p>' +
        '</div>';
    }

    // Update image preview when setting image id directly
    document.addEventListener("image:update", function(event) {
      const { targetId, fileboyId } = event.detail || {};

      if (!targetId || !fileboyId) return;

      if (typeof fieldId !== "undefined" && fieldId === targetId) {
        console.log('[Image Field] Input changed, updating image preview');
        clearImage();
        updateImage(fileboyId);
      }
    });
  }
</script>
