<div>
  <div class="pt-20 pb-8 px-4 md:px-8 bg-sky-200">
    <div class="container mx-auto">
      <div class="rounded-full p-4 bg-blue-950 inline-block mb-4"></div>
      <div class="flex flex-wrap gap-8">
        <div class="flex-1 min-w-[280px]">
          <h3 class="text-4xl mb-4">Stay connected</h3>
          <h4 class="text-md mb-4">Join our newsletter to stay up to date on features and releases</h4>
          <form hx-post="/footer_subscribe" class="flex gap-2 mb-6">
            <input id="email" name="email" type="text" placeholder="<EMAIL>" class="border rounded p-2 w-full flex-1">
            <button id="subscribe-button" class="bg-teal-500 text-white rounded p-2 flex-0" type="submit">Subscribe</button>
          </form>
          <div class="flex flex-wrap gap-4">
            <p class="flex-1 min-w-[200px]">
              <b>Address</b> <br>
              Developing Experts Limited<br/>Exchange Street Buildings<br/>35-37 Exchange Street<br/>Norwich<br/>NR2 1DP<br/>UK
            </p>
            <div class="flex-1 min-w-[200px] grid gap-4">
              <p><b>Phone</b><br>01603 273515</p>
              <p><b>Email</b><br><EMAIL></p>
            </div>
          </div>
        </div>
        <div class="flex-1  min-w-[300px]">
          <div class="flex mb-6 flex-wrap gap-8">
            <div class="flex-1  min-w-[150px]">
              <div class="grid gap-2">
                <h4>Developing Experts</h4>
                <%= [
                      link_to('Lessons', '/product'),
                      link_to('About', '/about'),
                      link_to('Research', '/research'),
                      link_to('Reporting', '/reporting'),
                      link_to('Plans & Pricing', '/plans')
                    ].join().html_safe %>
              </div>
            </div>
            <div class="flex-1 min-w-[150px]">
              <div class="grid gap-2 min-w-[150px]">
                <h4><%= link_to 'Legal', "/legal" %></h4>
                <%= [
                      link_to('GDPR', '/gdpr'),
                      link_to('Privacy Policy', '/privacy'),
                      link_to('Terms and Conditions', '/terms'),
                      link_to('Cookie Policy', '/cookies'),
                    ].join().html_safe %>
              </div>
            </div>
            <div class="flex-1 min-w-[150px]">
              <div class="grid gap-2 min-w-[150px]">
                <h4>Contact Us</h4>
                <%= [
                      link_to('Get in touch', '/enquiry'),
                      link_to('Request Subscription', lesson_subscription_path),
                      link_to("Children's Code", '/childrens-code'),
                      link_to('About Us', '/about'),

                      link_to('Careers', '/careers')
                    ].join().html_safe %>
              </div>
            </div>
          </div>
          <div class="flex justify-end items-end margin-left: auto gap-4">
            <img src="https://www.developingexperts.com/file-cdn/images/get/056a402e-6d51-4bdd-8d96-5cac52be10c7?transform=resize:_x80"/>
            <svg id="prefix__Layer_1" x="0" y="0" viewBox="0 0 205.8 66.7" xml:space="preserve" style="width: 100%; display: block; max-width: 250px;">
              <path class="prefix__st0" d="M53.7 54.8c0 .7-.4 1.1-1.1 1.1H36.1c-.4 0-.8-.1-.9-.2-.2-.1-.3-.5-.3-1V38.1c-1.1.7-2.3 1.3-3.7 1.6-1.9.5-3.9.8-6.2.8h-8.5c-.4 0-.8-.1-1-.2-.2-.2-.3-.5-.3-1V11c0-.5.1-.8.3-1 .2-.2.6-.3 1.1-.3H23c2.6 0 4.8.2 6.6.5 1.8.3 3.5.9 5 1.8 2 1.2 3.6 2.8 4.7 5s1.7 4.9 1.7 8.1h11.7c.4 0 .*******.*******.2.6v.3l-.3 2.7c-.1.4-.2.7-.4.8-.2.1-.6.2-1.1.2H40.6V38h10.3c.4 0 .7.1.9.2.2.2.2.4.2.7v2.5c0 .7-.4 1.1-1.1 1.1H40.6v8.9h11.9c.7 0 1.1.3 1.1 1v2.4zM54.6 2H15S4 2 4 13v39.6s0 11 11 11h39.6s11 0 11-11V13c.1 0 .1-11-11-11" fill="#130e3c"></path>
              <path class="prefix__st0" d="M31.3 16.4c-.9-.7-2-1.2-3.1-1.4-1.2-.3-2.5-.4-3.9-.4H21v21.2h3.4c1.5 0 3-.2 4.2-.5 1.3-.3 2.4-.9 3.3-1.9.9-.9 1.6-2 2.1-3.3.5-1.3.7-2.9.7-4.8 0-2-.3-3.8-.9-5.3-.5-1.6-1.3-2.8-2.5-3.6m118.9 37.3c-.1-.2-.2-.3-.4-.3 0 0-.1 0-.3.1-.3.1-.5.2-.8.2-.3.1-.6.1-1 .1-.3 0-.6 0-.8-.2-.2-.1-.4-.2-.5-.4-.1-.2-.2-.5-.3-.8 0-.3-.1-.7-.1-1.1V45h3.1c.4 0 .6-.2.6-.5v-1.3c0-.3-.2-.5-.5-.5h-3.1v-3.3c0-.4-.2-.6-.5-.6h-.1l-2 .2c-.2 0-.4.1-.5.2-.1.1-.2.2-.2.5v3h-1.4c-.3 0-.5.2-.5.6v1c0 .3 0 .5.1.6.1.1.2.1.5.1h1.3v7.5c0 .4 0 .8.1 1.1.1.3.1.6.3.9.1.3.3.5.5.8.3.4.8.7 1.4.9.6.2 1.2.3 1.9.3.6 0 1.1-.1 1.6-.2s1-.3 1.4-.5c.3-.1.4-.3.4-.5v-.2l-.2-1.4zm-28.7-6.9c.2-.4.4-.7.6-1 .3-.3.6-.5.9-.7.4-.2.8-.2 1.2-.2.4 0 .8.1 *******.2.6.4.8.6.2.2.4.5.5.9.1.3.2.7.2 1v.3h-5.7c.1-.4.3-.7.4-1.1m3 9.6c.7 0 1.5-.1 2.3-.2.8-.1 1.6-.4 2.3-.7.3-.1.4-.3.4-.5 0 0 0-.1-.1-.3l-.4-1.4c-.1-.2-.2-.3-.4-.3h-.2s-.1 0-.2.1c-.5.2-1.1.4-1.6.5-.6.1-1.2.2-1.8.2-1.1 0-2-.3-2.7-1-.7-.7-1.1-1.5-1.2-2.6h8.1c.3 0 .6 0 .7-.2.1-.1.2-.3.2-.6V49c0-.9-.1-1.8-.3-2.7-.2-.8-.7-1.6-1.3-2.3-.5-.5-1.1-.9-1.7-1.2-.7-.3-1.4-.4-2.3-.4-1 0-2 .2-2.8.5-.8.3-1.6.8-2.2 1.4-.6.6-1.1 1.4-1.4 2.2-.3.9-.5 1.8-.5 2.8 0 1.1.2 2.1.5 2.9.3.9.8 1.6 1.4 2.2.6.6 1.3 1.1 2.2 1.4 1 .4 1.9.6 3 .6m-28.3-7.6l4.2-5.5c.1-.1.1-.2.1-.3 0-.2-.1-.3-.4-.3h-2.5c-.3 0-.6.2-.8.5l-2.3 3.4-2.2-3.5c-.2-.3-.5-.4-.9-.4h-2.8c-.3 0-.4.1-.4.4 0 .1 0 .2.1.2l4.1 5.8-4.7 6.2c-.1.1-.1.2-.1.4s.1.3.3.3h2.4c.3 0 .5 0 .6-.1.2-.1.3-.2.4-.4l2.6-4.2 2.6 4.2c.1.2.2.3.3.4.1.1.3.1.6.1h2.8c.3 0 .5-.1.5-.4 0-.1 0-.2-.1-.4l-4.4-6.4zm15.9 2.4c-.2.6-.4 1.1-.8 1.5-.2.3-.5.5-.9.7-.4.2-.8.3-1.3.3s-1.1-.1-1.6-.4c-.5-.3-.9-.6-1.2-1v-6c.3-.3.7-.7 1.2-.9.6-.3 1.1-.4 1.6-.4 1 0 1.7.4 2.3 1.1.3.4.5.9.6 1.4.1.6.2 1.1.2 1.8.1.7 0 1.4-.1 1.9m1.9 3.3c.6-.7 1-1.5 1.3-2.4.3-.9.5-1.9.5-2.9s-.1-1.9-.4-2.8c-.3-.9-.7-1.7-1.3-2.3-.5-.5-1-.9-1.7-1.2-.6-.3-1.4-.5-2.3-.5-.8 0-1.5.2-2.2.5-.7.3-1.3.7-1.7 1.1v-.7c0-.2-.1-.3-.2-.4-.1-.1-.3-.1-.5-.1h-1.6c-.4 0-.7.2-.7.6v18c0 .5.2.7.7.7h1.9c.2 0 .4-.1.5-.2.1-.1.2-.3.2-.6v-6.2c.4.4.9.7 1.5 1 .6.2 1.2.3 1.9.3.9 0 1.7-.2 2.4-.5.5-.3 1.1-.8 1.7-1.4m22 .9v-8.8c.3-.4.8-.7 1.2-1 .5-.2.9-.3 1.4-.3.3 0 .5 0 .7.1h.1c.2 0 .4-.1.4-.4l.3-2v-.2c0-.2-.1-.3-.3-.4-.1 0-.3-.1-.4-.1h-.4c-.8 0-1.4.2-1.9.5s-1 .7-1.4 1.3v-.9c0-.3-.2-.5-.7-.5h-1.8c-.2 0-.4 0-.5.1s-.2.3-.2.6v12c0 .5.2.7.7.7h2c.5 0 .8-.3.8-.7m21.8-1.9c-.4.3-.9.4-1.5.4-.5 0-1-.1-1.6-.2l-1.8-.6c-.1 0-.2-.1-.3-.1-.2 0-.3.1-.4.4l-.4 1.4c0 .1-.1.2-.1.2 0 .2.1.3.3.4.6.3 1.3.5 2.1.7.7.2 1.5.3 2.2.3.7 0 1.4-.1 2-.3.6-.2 1.2-.4 1.7-.8.5-.3.9-.8 1.2-1.3.3-.5.4-1.2.4-1.9 0-.9-.3-1.6-.8-2.2-.5-.6-1.1-1-1.9-1.3l-2.5-1c-.5-.2-.9-.4-1.1-.6-.2-.2-.3-.5-.3-.8 0-.3.1-.6.4-.9.3-.3.7-.4 1.3-.4.4 0 .9.1 1.5.2.5.1 1 .3 1.5.5.1 0 .2.1.3.1.2 0 .3-.1.4-.3l.5-1.5c0-.1.1-.2.1-.3 0-.1-.1-.3-.3-.4-.7-.3-1.4-.5-2-.6-.7-.1-1.3-.2-2-.2-.6 0-1.2.1-1.8.2-.6.2-1.1.4-1.6.7-.5.3-.9.7-1.1 1.2-.3.5-.4 1.1-.4 1.8 0 .9.3 1.7.8 2.3.6.6 1.2 1.1 2 1.4l2.2.8c.6.2 1 .5 1.3.7.3.2.4.6.4.9-.2.4-.4.8-.7 1.1m-82.2 2.6h10.2c.4 0 .7-.2.7-.7v-1.6c0-.4-.2-.6-.7-.6h-7.4v-5.5h6.4c.4 0 .7-.2.7-.7v-1.6c0-.2-.1-.4-.2-.5-.1-.1-.3-.2-.5-.2h-6.4v-5h6.9c.3 0 .5 0 .7-.1.1-.1.2-.2.3-.5l.2-1.7v-.2c0-.1 0-.3-.1-.3-.1.1-.3.1-.5.1H75.5c-.4 0-.6.2-.6.7v17.6c0 .3.1.5.2.6 0 .1.2.2.5.2m88-16.5h.4v-1.4l.6 1.4c0 .1.1.1.2.1h.3l.1-.1.5-1.4v1.3c0 .1.1.2.2.2h.3c.1 0 .1 0 .1-.1l-.1-2.4c0-.1-.1-.1-.1-.1h-.3c-.1 0-.1 0-.2.1l-.7 1.7-.8-1.7s0-.1-.1-.1h-.4c-.1 0-.1 0-.1.1l-.1 2.4h.2" fill="#130e3c"></path>
              <path class="prefix__st0" d="M161.5 37.5h.6v2c0 .1 0 .1.1.1h.4c.1 0 .1 0 .1-.1v-2h.7l.1-.1v-.2c0-.1 0-.1-.1-.1h-2c-.1 0-.1 0-.1.1v.2s.1.1.2.1m-12.6-13.1c-.1.5-.3 1-.6 1.4-.3.4-.6.7-1 1-.4.2-.9.4-1.5.4s-1.1-.1-1.5-.4c-.4-.3-.7-.6-1-1-.2-.4-.4-.9-.5-1.4-.1-.5-.2-1.1-.2-1.7 0-.6.1-1.1.2-1.6s.3-1 .5-1.4c.2-.4.6-.7 1-1 .4-.2.9-.4 1.5-.4s1.1.1 1.5.4.7.6 1 1c.2.4.4.9.5 1.4.1.5.2 1.1.2 1.6.1.6 0 1.1-.1 1.7m3.2-4.5c-.3-.9-.7-1.6-1.2-2.2-.5-.6-1.2-1.1-2-1.5-.8-.3-1.8-.5-2.9-.5-1.1 0-2 .2-2.9.5-.8.4-1.5.9-2.1 1.5-.6.6-1 1.4-1.3 2.2-.3.9-.4 1.8-.4 2.8 0 1 .1 1.9.4 2.8s.7 1.6 1.2 2.2c.5.6 1.2 1.1 2 1.5.8.4 1.8.5 2.9.5 1.1 0 2-.2 2.9-.5.8-.4 1.5-.9 2.1-1.5.6-.6 1-1.4 1.3-2.2.3-.9.4-1.8.4-2.8 0-1-.1-1.9-.4-2.8m20.8-3.8H171c-.5 0-.7.2-.7.6v12c0 .5.2.7.7.7h1.9c.5 0 .7-.2.7-.7v-12c0-.3-.1-.4-.2-.5-.2-.1-.3-.1-.5-.1m-86.6 6.8c-.3.8-.7 1.5-1.3 2.1-.6.6-1.3 1-2.1 1.2-.8.2-1.6.3-2.6.3h-2.1V13.4h2c.9 0 1.7.1 2.4.2.7.2 1.4.5 1.9.9.7.5 1.2 1.3 1.6 2.2.4.9.6 2 .6 3.3.1 1.1-.1 2.1-.4 2.9m.4-11.1c-.9-.5-1.9-.9-3.1-1.1-1.1-.2-2.5-.3-4.1-.3h-4c-.3 0-.6.1-.7.2-.1.1-.2.3-.2.6v17.5c0 .3.1.5.2.6.1.1.3.2.6.2h5.3c1.4 0 2.6-.2 3.8-.5 1.2-.3 2.2-.9 3-1.6.9-.8 1.7-1.8 2.2-3 .5-1.2.8-2.7.8-4.3 0-2.1-.3-3.8-1-5.1-.6-1.5-1.6-2.5-2.8-3.2m9.9 8.4c.2-.4.4-.7.6-1 .3-.3.6-.5.9-.7.4-.2.8-.2 1.2-.2.4 0 .8.1 *******.2.6.4.8.6.2.2.4.5.5.9.1.3.2.7.2 1v.3h-5.7c.1-.4.2-.8.4-1.1m7.6 6.5c-.1-.2-.2-.3-.4-.3h-.2s-.1 0-.2.1c-.5.2-1.1.4-1.6.5-.6.1-1.2.2-1.8.2-1.1 0-2-.3-2.7-1-.7-.7-1.1-1.5-1.2-2.6h8.1c.3 0 .6-.1.7-.2.1-.1.2-.3.2-.6v-.4c0-.9-.1-1.8-.3-2.7-.2-.8-.7-1.6-1.3-2.3-.5-.5-1.1-.9-1.7-1.2-.7-.3-1.4-.4-2.3-.4-1 0-2 .2-2.8.5-.8.3-1.6.8-2.2 1.4-.6.6-1.1 1.4-1.4 2.2-.3.9-.5 1.8-.5 2.8 0 1.1.2 2.1.5 2.9.3.9.8 1.6 1.4 2.2.6.6 1.3 1.1 2.2 1.4.9.3 1.8.5 2.9.5.7 0 1.5-.1 2.3-.2.8-.1 1.6-.4 2.3-.7.3-.1.4-.3.4-.5 0 0 0-.1-.1-.3l-.3-1.3zm32.6 2V10.3c0-.2-.1-.4-.2-.5-.1-.1-.3-.1-.4-.1h-.2l-2 .3c-.2 0-.3.1-.4.2-.1.1-.1.2-.1.5v18.1c0 .4.2.7.6.7h2.1c.4-.1.6-.3.6-.8m35.1-17.9c-.5 0-.9.2-1.3.5s-.6.7-.6 1.3c0 .6.2 1 .6 1.3.4.3.8.4 1.3.4s.9-.2 1.3-.5.6-.7.6-1.3c0-.6-.2-1-.6-1.3-.4-.2-.9-.4-1.3-.4m-49.3 9.4c.2-.4.4-.7.6-1 .3-.3.6-.5.9-.7.4-.2.8-.2 1.2-.2.4 0 .8.1 *******.2.6.4.8.6.2.2.4.5.5.9.1.3.2.7.2 1v.3h-5.7c.2-.4.3-.8.4-1.1m7.7 8.7c.3-.1.4-.3.4-.5 0 0 0-.1-.1-.3l-.4-1.4c-.1-.2-.2-.3-.4-.3h-.2s-.1 0-.2.1c-.5.2-1.1.4-1.6.5-.6.1-1.2.2-1.8.2-1.1 0-2-.3-2.7-1-.7-.7-1.1-1.5-1.2-2.6h8.1c.3 0 .6 0 .7-.2.1-.1.2-.3.2-.6v-.4c0-.9-.1-1.8-.3-2.7-.2-.8-.7-1.6-1.3-2.3-.5-.5-1.1-.9-1.7-1.2-.7-.3-1.4-.4-2.3-.4-1 0-2 .2-2.8.5-.8.3-1.6.8-2.2 1.4-.6.6-1.1 1.4-1.4 2.2-.3.9-.5 1.8-.5 2.8 0 1.1.2 2.1.5 2.9.3.9.8 1.6 1.4 2.2.6.6 1.3 1.1 2.2 1.4.9.3 1.8.5 2.9.5.7 0 1.5-.1 2.3-.2.9-.1 1.7-.3 2.4-.6m54.2-13.2c-.8 0-1.6.1-2.4.4-.8.3-1.5.7-2.1 1.2v-.7c0-.2-.1-.3-.2-.4-.1-.1-.3-.1-.5-.1h-1.8c-.5 0-.7.2-.7.7v12c0 .4.2.7.7.7h2c.4 0 .7-.2.7-.7V20c.4-.4.9-.7 1.6-.9.6-.3 1.2-.4 1.7-.4.8 0 1.4.2 1.7.7.3.5.4 1.1.4 1.9v7.5c0 .2 0 .4.1.5.1.1.3.2.5.2h2c.3 0 .4-.1.5-.2.1-.1.1-.3.1-.5v-8.1c0-1.5-.3-2.7-1-3.6-.6-1-1.7-1.4-3.3-1.4m-73.9 13.2c.1.2.2.3.3.4.1.1.3.1.5.1h1.4c.2 0 .4 0 .5-.1.1-.1.2-.2.3-.4l4.8-12.2c0-.1.1-.2.1-.3 0-.3-.2-.4-.6-.4h-2c-.3 0-.4 0-.6.1-.1.1-.2.2-.3.5l-2.2 6.5c-.1.4-.3.8-.4 1.2-.1.4-.2.8-.3 1-.1-.3-.2-.7-.3-1.1-.1-.4-.2-.8-.4-1.3l-2.1-6.4c-.1-.4-.4-.6-.8-.6h-2.4c-.3 0-.5.1-.5.3 0 .1 0 .1.1.2l4.9 12.5zm47.6-9.1c.3-.3.7-.7 1.2-.9.6-.3 1.1-.4 1.6-.4 1 0 1.7.4 2.3 1.1.3.4.5.9.6 1.4.1.6.2 1.1.2 1.8s-.1 1.3-.2 1.9-.4 1.1-.8 1.5c-.2.3-.5.5-.9.7-.4.2-.8.3-1.3.3s-1.1-.1-1.6-.4c-.5-.3-.9-.6-1.2-1v-6zm-2.6 15.6h1.9c.2 0 .4-.1.5-.2.1-.1.2-.3.2-.6v-6.2c.4.4.9.7 1.5 1 .6.2 1.2.3 1.9.3.9 0 1.7-.2 2.4-.5s1.3-.8 1.8-1.4c.6-.7 1-1.5 1.3-2.4.3-.9.5-1.9.5-2.9s-.1-1.9-.4-2.8c-.3-.9-.7-1.7-1.3-2.3-.5-.5-1-.9-1.7-1.2-.6-.3-1.4-.5-2.3-.5-.8 0-1.5.2-2.2.5-.7.3-1.3.7-1.7 1.1v-.7c0-.2-.1-.3-.2-.4-.1-.1-.3-.1-.5-.1h-1.6c-.4 0-.7.2-.7.6v18c-.1.4.1.7.6.7m45.1-10c-.3.3-.7.7-1.2.9-.5.3-1.1.4-1.6.4-.4 0-.7-.1-1.1-.2-.3-.1-.6-.3-.9-.6-.4-.4-.7-.9-.9-1.5-.2-.6-.2-1.2-.2-1.8 0-.6.1-1.3.3-1.9.2-.6.5-1.2.9-1.6l.9-.6c.3-.1.7-.2 1.1-.2.5 0 1 .1 1.5.4.5.3.9.6 1.1.9v5.8zm3.3-8.7c0-.2 0-.4-.1-.5-.1-.1-.3-.2-.5-.2h-1.8c-.4 0-.6.2-.6.5l-.1.7c-.2-.2-.4-.5-.6-.6-.3-.2-.5-.3-.8-.5-.7-.3-1.3-.4-2-.4-1 0-1.9.2-2.6.6-.8.4-1.4.9-1.9 1.6-.5.7-.9 1.4-1.2 2.3-.3.9-.4 1.8-.4 2.7 0 1 .1 1.9.4 2.8.3.9.7 1.6 1.3 2.2 1 1 2.3 1.6 4.1 1.6.3 0 .6 0 1-.1.3-.1.7-.2 1-.3.3-.1.6-.3.9-.4.3-.2.5-.4.7-.6v1.1c0 .6 0 1.1-.1 1.5-.1.5-.3.8-.5 1.2-.2.3-.6.6-1 .7-.4.2-1 .3-1.7.3-.5 0-1-.1-1.5-.2s-1.1-.3-1.6-.5c-.2-.1-.3-.1-.4-.1-.1 0-.3.2-.4.5l-.4 1.2c0 .1 0 .1-.1.2v.2c0 .2.1.4.4.6.7.3 1.3.5 2 .6.7.1 1.4.2 2.1.2 2.3 0 4-.6 5-1.8 1-1.2 1.5-2.9 1.5-5.2V16.7z" fill="#130e3c"></path>
            </svg>
          </div>
        </div>
      </div>

    </div>
  </div>
  <div class="bg-teal-500 p-4">
    <div class="container mx-auto text-sm">
      <p>Copyright <%= Date.current.year %> Developing Experts, All rights reserved.</p>
    </div>
  </div>
</div>
