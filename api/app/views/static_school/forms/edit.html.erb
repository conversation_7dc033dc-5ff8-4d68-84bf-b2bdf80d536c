<%= javascript_include_tag "nice-select", type: "module" %>
<%= javascript_include_tag 'scripts/field-select' %>
<% content_for :title, "Edit | Manage Class" %>
<div class="pt-12">
  <%= render 'edit_header', form: @form, active_tab: :edit, title: "Edit Class #{@form.name}" %>
  <div class="md:px-8" data-controller="modal">
    <div id="edit-container" class="bg-white md:rounded-xl p-8 text-black">
      <div id="details-container" class="mb-12 max-w-2xl">
        <h2 class="text-2xl mb-4">Details</h2>
        <%= form_with model: @form, url: "/school/classes/#{@form.id}", method: :patch, local: true do |form| %>
          <% if @form.errors.any? %>
            <div class="form-errors">
              <ul>
                <% @form.errors.full_messages.each do |message| %>
                  <li><%= message %></li>
                <% end %>
              </ul>
            </div>
          <% end %>
          <div class="mb-4">
            <%= form.label :name, class: "font-semibold" %>
            <%= form.text_field :name %>
          </div>
          <div class="mb-4">
            <%= form.label :lesson_weekdays, "Teaching days", class: "font-semibold" %>
            <p class="text-sm -mt-1 mb-1">When assigning new lessons to this class, they will automatically be scheduled on these days of the week.</p>
            <%= form.select :lesson_weekdays, options_for_select(@days.map { |d| [d[0], d[1]] }, @form.lesson_weekdays.select { |k, v| v == 1 }.keys), {}, { multiple: true, class: "field field-select", id: "lesson-weekday-select" } %>
          </div>
          <%= form.submit "Save", class: "btn btn-base btn-purple" %>
        <% end %>
      </div>
      <div id="teachers-container">
        <h2 class="text-2xl mb-4">Teachers</h2>
        <div class="flex flex-col gap-4 w-fit mb-4">
          <% @teachers.each do |teacher| %>
            <div class="flex items-center justify-between gap-x-4">
              <%= render AvatarComponent.new(text: teacher.name, fileboy_image_id: teacher.fileboy_image_id, include_text: true) %>
              <%= link_to remove_teacher_school_form_path(@form, teacher_id: teacher.id), method: :patch, data: { confirm: 'Are you sure you want to remove this teacher?' }, class: "hover:text-red-800" do %>
                <i class="fa fa-trash"></i>
              <% end %>
            </div>
          <% end %>
        </div>
        <button class="btn btn-sm btn-purple" data-action="click->modal#open" type="button">Add Teacher...</button>
      </div>
      <dialog data-modal-target="dialog" class="p-6 overflow-visible w-full max-w-2xl max-h-80 h-full">
        <div class="flex flex-col h-full justify-between">
          <%= form_with url: "/school/classes/#{@form.id}/add_teacher", method: :patch, local: true do |form| %>
            <div>
              <h3 class="flex-shrink-0 text-2xl p-2 mb-2">Add Teacher</h4>
              <label>Teacher</label>
              <%= form.select :teacher_id,
                  options_for_select((@all_teachers - @teachers).pluck(:name, :id)),
                  { include_blank: "Select Teacher" },
                  { required: true, data: { nice_select: true } }
              %>
            </div>
            <div class="flex flex-shrink-0 mt-2 pt-4 gap-2">
              <button class="btn btn-flat-white btn-base" data-action="click->modal#close" type="button">Cancel</button>
              <button class="btn btn-purple btn-base" type="submit">Add Teacher</button>
            </div>
          <% end %>
        </div>
      </dialog>
    </div>
    <%- if @current_user.is_school_admin?%>
      <div class="mt-4">
        <%= link_to 'Delete', "/school/classes/#{@form.id}", method: :delete, data: { confirm: 'Are you sure?' }, class: 'btn btn-flat-red btn-base' %>
      </div>
    <% end %>
  </div>
</div>
