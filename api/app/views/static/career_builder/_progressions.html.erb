<div id="career-progressions">
  <div>
    <h2 class="text-2xl mb-4">Career Progressions</h2>
    <% if @occupational_progressions.nil? %>
      <div>No career progressions found for <%= @career_name %></div>
    <% else %>
      <div class="grid gap-y-4 mb-8">
        <p>
          This page showcases various career options and the pathways to reach them. Each career listed here shares
          transferable skills and knowledge, making it easier for individuals to transition between them.
        </p>
        <p>
          Your current career is highlighted to help you see how it fits into the broader landscape of potential
          career choices. By clicking on any career, you can learn more about it, including the training and education
          required to pursue it.
        </p>
        <p>
          Remember, progressing in your career often involves further learning and training. This page provides
          insights into future career options as well as those that can lead up to your current one.
        </p>
        <p>
          These career progression decisions are informed by comparing the skills and knowledge needed for different
          occupations, along with data on how people move between them. Explore the possibilities and discover the
          exciting journey ahead in your career!
        </p>
      </div>

      <div id="occProgression" class="bg-black/25 p-4 rounded-lg">
        <div class="flex mb-8">
          <div class="mx-auto">
            <h2>Current Career</h2>
            <%= link_to career_builder_path(params[:id]) do %>
              <div class="border-2 border-yellow-500 rounded-xl max-w-sm mt-2 hover:shadow-xl hover:-translate-y-1 transition duration-300 ease-in-out">
                <% current_career = @occupational_progressions[:selected] %>
                <%= render partial: "teaser_card", locals: { title: current_career["name"], body: current_career["overview"], interactive: false } %>
              </div>
            <% end %>
          </div>
        </div>
        <div class="grid grid-cols-1 gap-8 mx-auto">
          <% if @occupational_progressions[:before].any? %>
            <div class="mx-auto">
              <h2>Starting Careers</h2>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 mt-2">
                <% @occupational_progressions[:before].each do |career| %>
                  <div class="flex justify-start">
                    <a class="cursor-pointer" hx-post="/career-builder/generate_career_path" hx-vals='{"career": "<%= career["name"] %>", "age": "<%= @age %>"}' hx-target="body" hx-swap="outerHTML" hx-push-url="true"  onclick="window.scrollTo({top: 0, behavior: 'smooth'})">
                      <%= render partial: "teaser_card", locals: { title: career["name"], body: career["overview"] } %>
                    </a>
                  </div>
                <% end %>
              </div>
            </div>
          <% end %>
          <% if @occupational_progressions[:after].any? %>
            <div class="mx-auto">
              <h2>Advanced Careers</h2>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 mt-2">
                <% @occupational_progressions[:after].each do |career| %>
                  <div class="flex justify-start">
                    <a class="cursor-pointer" hx-post="/career-builder/generate_career_path" hx-vals='{"career": "<%= career["name"] %>", "age": "<%= @age %>"}' hx-target="body" hx-swap="outerHTML" hx-push-url="true"  onclick="window.scrollTo({top: 0, behavior: 'smooth'})">
                      <%= render partial: "teaser_card", locals: { title: career["name"], body: career["overview"] } %>
                    </a>
                  </div>
                <% end %>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    <% end %>
  </div>
</div>