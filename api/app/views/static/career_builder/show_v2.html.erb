<% content_for :title, "#{@career_path.career_name} | AI Career Search V2" %>

<%= javascript_include_tag 'scripts/display-loading-facts' %>

<%= render LoadingOverlayComponent.new(id: 'careerLoad', text: "Creating Career Path...") %>
<%= render LoadingOverlayComponent.new(id: 'furtherCareerLoad', text: "Finding Further Careers...") %>

<div class="min-h-screen bg-gray-900">
  <div id="career-content">
    <script type="module">
        let isGenerating = <%= @career_path.status == 'generating' %>;
        
        // V2 doesn't need auto-refresh since generation happens before redirect
        if (isGenerating) {
            // Show a simple message instead of skeleton
            console.log('Career path is still generating, this should not happen in V2');
        }
    </script>

    <% if @career_path.status == 'error' %>
      <!-- Error Banner -->
      <div class="relative overflow-hidden bg-gradient-to-r from-red-900/70 via-red-800/70 to-red-900/70 py-12">
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">
            <%= @career_path.career_name %>
            <span class="text-sm bg-red-600 text-white px-2 py-1 rounded ml-2">V2 ERROR</span>
          </h1>
        </div>
      </div>
      <!-- Error Content -->
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div id="careerInappropriate" class="bg-red-50 border border-red-200 rounded-xl p-6 flex gap-4">
          <div class="bg-red-500 text-white rounded-lg p-4 flex-shrink-0">
            <i class="fa-solid fa-triangle-exclamation text-2xl" aria-hidden="true"></i>
          </div>
          <div class="flex-1">
            <h2 class="text-red-800 text-xl font-bold mb-2">Something has gone wrong with V2 AI</h2>
            <p class="text-red-700 mb-4">It seems something has gone wrong with the new AI service. The admins have been notified and will try to fix this issue. Please try again later.</p>
            <button onclick="regenerateCareerPathV2(<%= @career_path.id %>)" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors">
              Retry Generation
            </button>
          </div>
        </div>
      </div>

    <% elsif @career_path.status == 'flagged' %>
      <!-- Flagged Banner -->
      <div class="relative overflow-hidden bg-gradient-to-r from-yellow-900/70 via-orange-900/70 to-red-900/70 py-12">
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">
            <%= @career_path.career_name %>
            <span class="text-sm bg-yellow-600 text-white px-2 py-1 rounded ml-2">V2 FLAGGED</span>
          </h1>
        </div>
      </div>
      <!-- Flagged Content -->
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div id="careerInappropriate" class="bg-red-50 border border-red-200 rounded-xl p-6 flex gap-4">
          <div class="bg-red-500 text-white rounded-lg p-4 flex-shrink-0">
            <i class="fa-solid fa-triangle-exclamation text-2xl" aria-hidden="true"></i>
          </div>
          <div class="flex-1">
            <h2 class="text-red-800 text-xl font-bold mb-2">Inappropriate Career</h2>
            <p class="text-red-700"><%= @career_path.flagged_reason %></p>
          </div>
        </div>
      </div>

    <% else %>
      <!-- Success State - Main Career Content -->
      
      <!-- Banner Section -->
      <div class="relative overflow-hidden bg-gradient-to-r from-purple-900/70 via-blue-900/70 to-cyan-900/70 py-12">
        <!-- Subtle Background Elements -->
        <div class="absolute inset-0">
          <!-- Grid Pattern -->
          <div class="absolute inset-0 opacity-3">
            <div class="grid grid-cols-12 gap-4 h-full">
              <% (1..12).each do |i| %>
                <div class="border-r border-white/10"></div>
              <% end %>
            </div>
          </div>
          
          <!-- Minimal Floating Icons -->
          <div class="absolute top-8 right-1/4 text-cyan-300/20 text-xl">
            <i class="fas fa-user-tie"></i>
          </div>
          <div class="absolute bottom-8 left-1/3 text-purple-300/20 text-lg">
            <i class="fas fa-rocket"></i>
          </div>
        </div>
        
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
            <!-- Left Content -->
            <div class="lg:col-span-2">
              <!-- Breadcrumbs -->
              <div class="mb-4">
                <%= render_breadcrumbs(dark_text: false) %>
              </div>
              
              <!-- Job Family Badge -->
              <% if @career_path&.job_family.present? %>
                <div class="mb-4">
                  <%= link_to job_family_path(@career_path.job_family), 
                      class: "inline-flex items-center px-3 py-1 bg-white/10 backdrop-blur-sm border border-white/20 text-cyan-300 text-sm font-semibold rounded-full hover:bg-white/20 transition-all duration-300" do %>
                    <i class="fas fa-briefcase mr-2 text-xs"></i>
                    <%= @career_path.job_family.name %>
                  <% end %>
                </div>
              <% end %>
              
              <h1 class="text-4xl md:text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-300 to-purple-300 mb-4">
                <%= @career_path.career_name %>
              </h1>
              
              <!-- Education Level Switcher -->
              <% if @related_career_paths.any? %>
                <div class="mb-6">
                  <p class="text-gray-300 text-sm mb-3">View this career for different education levels:</p>
                  <div class="flex flex-wrap gap-2">
                    <% @related_career_paths.each do |related_cp| %>
                      <% if related_cp.education_level == @current_education_level %>
                        <span class="px-4 py-2 bg-gradient-to-r from-blue-600 to-cyan-500 text-white rounded-lg font-semibold shadow-lg">
                          <%= related_cp.education_level_short %>
                        </span>
                      <% else %>
                        <%= link_to show_v2_career_builder_path(related_cp), 
                            class: "px-4 py-2 bg-white/10 hover:bg-gradient-to-r hover:from-blue-600 hover:to-cyan-500 text-white rounded-lg font-semibold transition-all duration-300 hover:shadow-lg hover:scale-105" do %>
                          <%= related_cp.education_level_short %>
                        <% end %>
                      <% end %>
                    <% end %>
                  </div>
                </div>
              <% end %>
              
              <!-- Description -->
              <% if @career_path&.career&.description.present? %>
                <p class="text-lg text-gray-200 leading-relaxed mb-4"><%= @career_path.career.description %></p>
              <% end %>
              <% if @info["about"].present? %>
                <p class="text-lg text-gray-200 leading-relaxed"><%= @info["about"] %></p>
              <% end %>
            </div>
            
            <!-- Right Content - Career Image -->
            <div class="relative">
              <% if @result.career_path.present? && @result.career_path["image"].present? %>
                <div class="relative group">
                  <div class="relative overflow-hidden rounded-2xl transform group-hover:scale-105 transition-transform duration-500">
                    <div class="relative">
                      <div class="w-full h-64">
                        <%= image_tag(@result.career_path["image"], 
                            alt: @career_path.career_name,
                            class: "object-cover w-full h-64",
                            data: { reportable: @result.career_path['image'] }
                        ) %>
                      </div>
                      
                      <!-- Gradient Overlay -->
                      <div class="absolute inset-0 bg-gradient-to-tr from-purple-600/20 via-transparent to-cyan-400/20"></div>
                      
                      <!-- Report Button -->
                      <%= render ReportMediaButtonComponent.new(
                        type: 'image', 
                        source: 'CareerPath', 
                        id: @career_path.id, 
                        reference: @result.career_path['image'],
                        class_name: "absolute bottom-2 left-2 text-white bg-black/50 hover:bg-black/70 text-sm p-2 rounded-lg cursor-pointer transition-colors"
                      ) do %>
                        <i class="fa fa-flag"></i>
                      <% end %>
                      
                      <!-- Favourite Button -->
                      <% if @user_career_path.present? %>
                        <div class="absolute top-2 right-2" onclick='toggleFavouriteCareerPath(<%= @user_career_path.id %>);'>
                          <i id="favourite-icon" class="bg-black/50 hover:bg-black/70 p-2 rounded-lg text-white text-lg fa-<%= @user_career_path.is_favourite ? 'solid' : 'regular' %> fa-star cursor-pointer transition-colors"></i>
                        </div>
                      <% end %>
                    </div>
                  </div>
                  
                  <!-- Decorative Elements -->
                  <div class="absolute -top-2 -right-2 w-6 h-6 bg-cyan-400 rounded-full animate-pulse"></div>
                  <div class="absolute -bottom-2 -left-2 w-4 h-4 bg-purple-400 rounded-full animate-pulse delay-500"></div>
                </div>
              <% else %>
                <!-- Fallback when no image -->
                <div class="bg-gradient-to-br from-gray-700 to-gray-900 rounded-2xl h-64 flex flex-col items-center justify-center border border-gray-600 relative">
                  <i class="fas fa-user-tie text-6xl text-gray-400 mb-4"></i>
                  <span class="text-gray-400 text-lg font-medium">Career Path</span>

                  <!-- Favourite Button -->
                  <% if @user_career_path.present? %>
                    <div class="absolute top-2 right-2" onclick='toggleFavouriteCareerPath(<%= @user_career_path.id %>);'>
                      <i id="favourite-icon" class="bg-black/50 hover:bg-black/70 p-2 rounded-lg text-white text-lg fa-<%= @user_career_path.is_favourite ? 'solid' : 'regular' %> fa-star cursor-pointer transition-colors"></i>
                    </div>
                  <% end %>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="flex flex-col lg:flex-row max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-12 space-y-8 lg:space-y-0 lg:space-x-10 pb-16">

        <!-- Sidebar Navigation -->
        <nav class="hidden lg:block sticky top-8 h-max w-60 bg-gray-800 border border-gray-700 rounded-xl p-4 text-white">
          <h2 class="text-xl font-bold mb-4 text-transparent bg-clip-text bg-gradient-to-r from-cyan-300 to-purple-300">
            Explore <span class="text-xs bg-blue-600 px-1 py-0.5 rounded">V2</span>
          </h2>
          <ul class="space-y-2 text-sm">
            <li>
              <a href="#" onclick="window.scrollTo({ top: 0, behavior: 'smooth'})" class="hover:text-cyan-400 block py-2 px-3 rounded-lg hover:bg-gray-700/50 transition-all duration-300">
                🔝 To Top
              </a>
            </li>
            <% if @info["stats"].present? %>
              <li>
                <a href="#stats" onclick="scrollToElement('stats')" class="hover:text-cyan-400 block py-2 px-3 rounded-lg hover:bg-gray-700/50 transition-all duration-300">
                  📊 Statistics
                </a>
              </li>
            <% end %>
            <% if @info["further_careers"].present? %>
              <li>
                <a href="#further" onclick="scrollToElement('further')" class="hover:text-cyan-400 block py-2 px-3 rounded-lg hover:bg-gray-700/50 transition-all duration-300">
                  🚀 Further Careers
                </a>
              </li>
            <% end %>
            <% if @result.career_path.present? && @result.career_path["careerPath"].present? %>
              <% @result.career_path["careerPath"].each_with_index do |res, i| %>
                <li>
                  <a href="#stage_<%= i %>" onclick="scrollToElement('stage_<%= i %>')" class="hover:text-cyan-400 block py-2 px-3 rounded-lg hover:bg-gray-700/50 transition-all duration-300">
                    <%= res["emoji"] %> <%= res["title"] %>
                  </a>
                </li>
              <% end %>
            <% end %>
            <% if @info["relatedCareers"].present? %>
              <li>
                <a href="#related" onclick="scrollToElement('related')" class="hover:text-cyan-400 block py-2 px-3 rounded-lg hover:bg-gray-700/50 transition-all duration-300">
                  🔗 Related
                </a>
              </li>
            <% end %>
            <% if @suggested_lessons.present? %>
              <li>
                <a href="#suggested_lessons" onclick="scrollToElement('suggested_lessons')" class="hover:text-cyan-400 block py-2 px-3 rounded-lg hover:bg-gray-700/50 transition-all duration-300">
                  📚 Suggested Lessons
                </a>
              </li>
            <% end %>
            <% if @result.videos.present? %>
              <li>
                <a href="#suggested_videos" onclick="scrollToElement('suggested_videos')" class="hover:text-cyan-400 block py-2 px-3 rounded-lg hover:bg-gray-700/50 transition-all duration-300">
                  🎬 Suggested Videos
                </a>
              </li>
            <% end %>
            <li>
              <a href="#career-apprenticeships" class="hover:text-cyan-400 block py-2 px-3 rounded-lg hover:bg-gray-700/50 transition-all duration-300">
                🎓 View Apprenticeships
              </a>
            </li>
            <% if @suggested_tours.present? %>
              <li>
                <a href="#suggested_tours" onclick="scrollToElement('suggested_tours')" class="hover:text-cyan-400 block py-2 px-3 rounded-lg hover:bg-gray-700/50 transition-all duration-300">
                  🏭 Suggested Tours
                </a>
              </li>
            <% end %>
            <li>
              <a href="#career-progressions" class="hover:text-cyan-400 block py-2 px-3 rounded-lg hover:bg-gray-700/50 transition-all duration-300">
                📈 View Career Progressions
              </a>
            </li>
            <li>
              <a href="#career-qualifications" class="hover:text-cyan-400 block py-2 px-3 rounded-lg hover:bg-gray-700/50 transition-all duration-300">
                🎯 View Qualifications
              </a>
            </li>
          </ul>
        </nav>

        <!-- Main Content -->
        <div class="flex-1 space-y-12 text-white">

          <!-- Statistics -->
          <% if @info["stats"].present? %>
            <section id="stats">
              <h2 class="text-3xl font-bold mb-8 text-transparent bg-clip-text bg-gradient-to-r from-cyan-300 to-purple-300">
                📊 Statistics
              </h2>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <% @info["stats"].each do |stat| %>
                  <div class="bg-gray-800 border border-gray-700 rounded-xl p-6 hover:border-cyan-400/50 hover:scale-105 transition-all duration-300">
                    <h3 class="font-semibold text-xl mb-2 text-white"><%= stat["title"] %></h3>
                    <p class="text-lg text-gray-300"><%= stat["text"] %></p>
                  </div>
                <% end %>
              </div>
            </section>
          <% end %>

          <!-- Further Careers -->
          <% further_careers = @user_further_careers.nil? || @user_further_careers.empty? ? @info["further_careers"] : @user_further_careers %>
          <% if further_careers.present? %>
            <section id="further">
              <h2 class="text-3xl font-bold mb-8 text-transparent bg-clip-text bg-gradient-to-r from-cyan-300 to-purple-300">
                🚀 Careers in this path
              </h2>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <% further_careers.each do |career| %>
                  <%= link_to careers_generate_path(name: career['name'], education_level: @current_education_level), class: "cursor-pointer group" do %>
                    <div class="bg-gray-800 border border-gray-700 rounded-xl p-6 flex flex-col justify-between h-full group-hover:border-cyan-400 group-hover:scale-105 transition-all duration-300 hover:shadow-2xl">
                      <% if career["image"].present? %>
                        <div class="mb-4 relative" data-reportable="<%= career['image'] %>">
                          <%= image_tag(career["image"], class: "w-full h-48 object-cover rounded-lg") %>
                          <%= render ReportMediaButtonComponent.new(
                            type: 'image', 
                            source: 'CareerPath', 
                            id: @career_path.id, 
                            reference: career["image"],
                            class_name: "absolute bottom-2 left-2 text-white bg-black/50 hover:bg-black/70 text-sm p-2 rounded-lg cursor-pointer transition-colors"
                          ) do %>
                            <i class="fa fa-flag"></i>
                          <% end %>
                        </div>
                      <% end %>
                      <div>
                        <h3 class="text-xl font-bold mb-2 group-hover:text-cyan-300 transition-colors"><%= career["name"] %></h3>
                        <p class="text-lg text-gray-300"><%= career["description"] %></p>
                      </div>
                    </div>
                  <% end %>
                <% end %>
              </div>
            </section>
          <% end %>

          <!-- Career Path Stages -->
          <% if @result.career_path.present? && @result.career_path["careerPath"].present? %>
            <% @result.career_path["careerPath"].each_with_index do |res, i| %>
              <section id="stage_<%= i %>">
                <div class="flex flex-col md:flex-row items-start md:items-center gap-4 mb-8">
                  <h2 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-300 to-purple-300">
                    <%= res["title"] %>
                  </h2>
                  <% if @audios.audios.any? { |audio| audio.name == res["keyword"] } %>
                    <%= render AudioComponent.new(url: get_audio_url(@audios, res["keyword"]), collapsed: true) %>
                  <% else %>
                    <span id="audio-<%= res['keyword'] %>" class="hidden">
                      <%= render AudioComponent.new(url: get_audio_url(@audios, res["keyword"]), collapsed: true, add_src_later: true) %>
                    </span>
                    <button id="btn-<%= res['keyword'] %>" type="button"
                      class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-cyan-400 to-purple-400 text-white font-semibold rounded-lg shadow-md hover:from-cyan-600 hover:to-purple-500 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-cyan-400"
                      onclick="generateAudioForSection(<%= @career_path.id %>, <%= json_escape(res.to_json) %>)">
                        <i class="fa-solid fa-volume-up"></i>
                    </button>
                  <% end %>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <% res["steps"].each do |s| %>
                    <div class="bg-gray-800 border border-gray-700 rounded-xl p-6 flex flex-col justify-between h-full hover:border-purple-400/50 hover:scale-105 transition-all duration-300">
                      <% if s["image"].present? %>
                        <div class="mb-4 relative" data-reportable="<%= s['image'] %>">
                          <%= image_tag(s["image"], class: "w-full h-48 object-cover rounded-lg") %>
                          <%= render ReportMediaButtonComponent.new(
                            type: 'image', 
                            source: 'CareerPath', 
                            id: @career_path.id, 
                            reference: s["image"],
                            class_name: "absolute bottom-2 left-2 text-white bg-black/50 hover:bg-black/70 text-sm p-2 rounded-lg cursor-pointer transition-colors"
                          ) do %>
                            <i class="fa fa-flag"></i>
                          <% end %>
                        </div>
                      <% end %>
                      <div>
                        <h3 class="text-xl font-bold mb-2 text-white"><%= s["title"] %></h3>
                        <p class="text-lg text-gray-300 mb-4"><%= s["description"] %></p>
                        <% if s["furtherInfo"].present? %>
                          <p class="text-base text-gray-400"><%= s["furtherInfo"] %></p>
                        <% end %>
                      </div>
                    </div>
                  <% end %>
                </div>
              </section>
            <% end %>
          <% end %>

          <!-- Related Careers -->
          <% if @info["relatedCareers"].present? %>
            <section id="related">
              <h2 class="text-3xl font-bold mb-8 text-transparent bg-clip-text bg-gradient-to-r from-cyan-300 to-purple-300">
                🔗 Related Careers
              </h2>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <% @info["relatedCareers"].each do |career| %>
                  <%= link_to careers_generate_path(name: career['name'], education_level: @current_education_level), class: "cursor-pointer group" do %>
                    <div class="bg-gray-800 border border-gray-700 rounded-xl p-6 flex flex-col justify-between h-full group-hover:border-cyan-400 group-hover:scale-105 transition-all duration-300 hover:shadow-2xl">
                      <% if career["image"].present? %>
                        <div class="mb-4 relative"  data-reportable="<%= career['image'] %>">
                          <%= image_tag(career["image"], class: "w-full h-48 object-cover rounded-lg") %>
                          <%= render ReportMediaButtonComponent.new(
                            type: 'image', 
                            source: 'CareerPath', 
                            id: @career_path.id, 
                            reference: career["image"],
                            class_name: "absolute bottom-2 left-2 text-white bg-black/50 hover:bg-black/70 text-sm p-2 rounded-lg cursor-pointer transition-colors"
                          ) do %>
                            <i class="fa fa-flag"></i>
                          <% end %>
                        </div>
                      <% end %>
                      <div>
                        <h3 class="text-xl font-bold mb-2 group-hover:text-cyan-300 transition-colors"><%= career["name"] %></h3>
                        <p class="text-lg text-gray-300"><%= career["description"] %></p>
                      </div>
                    </div>
                  <% end %>
                <% end %>
              </div>
            </section>
          <% end %>

          <!-- Suggested Lessons -->
          <% if @suggested_lessons.present? %>
            <section id="suggested_lessons">
              <h2 class="text-3xl font-bold mb-8 text-transparent bg-clip-text bg-gradient-to-r from-cyan-300 to-purple-300">
                📚 Suggested Lessons
              </h2>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <% @suggested_lessons.each do |l| %>
                  <div class="cursor-pointer group">
                    <%= link_to "/missions/#{l[:id]}" do %>
                      <% fileboy_image_url = "https://www.developingexperts.com/file-cdn/images/get/#{l[:fileboy_image_id]}?transform=resize:1000x_;format:webp;quality:75" %>
                      <div class="bg-gray-800 border border-gray-700 rounded-xl p-6 flex flex-col justify-between h-full group-hover:border-cyan-400 group-hover:scale-105 transition-all duration-300">
                        <% if fileboy_image_url.present? %>
                          <div class="mb-4 relative" data-reportable="<%= fileboy_image_url %>">
                            <%= image_tag(fileboy_image_url, class: "w-full h-48 object-cover rounded-lg") %>
                            <%= render ReportMediaButtonComponent.new(
                              type: 'image', 
                              source: 'CareerPath', 
                              id: @career_path.id, 
                              reference: fileboy_image_url,
                              class_name: "absolute bottom-2 left-2 text-white bg-black/50 hover:bg-black/70 text-sm p-2 rounded-lg cursor-pointer transition-colors"
                            ) do %>
                              <i class="fa fa-flag"></i>
                            <% end %>
                          </div>
                        <% end %>
                        <div>
                          <h3 class="text-xl font-bold mb-2 group-hover:text-cyan-300 transition-colors"><%= l[:name] %></h3>
                        </div>
                      </div>
                    <% end %>
                  </div>
                <% end %>
              </div>
            </section>
          <% end %>

          <!-- Suggested Videos -->
          <% if @result.videos.present? %>
            <section id="suggested_videos">
              <h2 class="text-3xl font-bold mb-8 text-transparent bg-clip-text bg-gradient-to-r from-cyan-300 to-purple-300">
                🎬 Useful Videos
              </h2>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <% @result.videos.each do |video| %>
                  <%= render 'static/videos/video_card', video: video %>
                <% end %>
              </div>
            </section>
          <% end %>

          <!-- Suggested Videos (Alternative) -->
          <% if @suggested_videos.present? %>
            <section id="suggested_videos">
              <h2 class="text-3xl font-bold mb-8 text-transparent bg-clip-text bg-gradient-to-r from-cyan-300 to-purple-300">
                🎬 Suggested Videos
              </h2>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <% @suggested_videos.each do |video| %>
                  <%= render 'static/videos/video_card', video: video %>
                <% end %>
              </div>
            </section>
          <% end %>

          <!-- Suggested Tours -->
          <% if @suggested_tours.present? %>
            <section id="suggested_tours">
              <h2 class="text-3xl font-bold mb-8 text-transparent bg-clip-text bg-gradient-to-r from-cyan-300 to-purple-300">
                🏭 Suggested Tours
              </h2>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <% @suggested_tours.each do |t| %>
                  <%= link_to "/tours/#{t[:id]}", class: "group" do %>
                    <% fileboy_image_url = "https://www.developingexperts.com/file-cdn/images/get/#{t[:fileboy_image_id]}?transform=resize:1000x_;format:webp;quality:75" %>
                    <div class="bg-gray-800 border border-gray-700 rounded-xl p-6 flex flex-col justify-between h-full group-hover:border-cyan-400 group-hover:scale-105 transition-all duration-300">
                      <% if fileboy_image_url.present? %>
                        <div class="mb-4">
                          <%= image_tag(fileboy_image_url, class: "w-full h-48 object-cover rounded-lg") %>
                        </div>
                      <% end %>
                      <div>
                        <h3 class="text-xl font-bold mb-2 group-hover:text-cyan-300 transition-colors"><%= t[:name] %></h3>
                      </div>
                    </div>
                  <% end %>
                <% end %>
              </div>
            </section>
          <% end %>

          <!-- Dynamic Content Sections -->
          <%= render "static/career_builder/apprenticeships" %>
          <%= render "static/career_builder/progressions" %>
          <%= render "static/career_builder/qualifications" %>

          <!-- Call to Action -->
          <div class="text-center py-12">
            <div class="bg-gradient-to-r from-gray-800 to-gray-700 rounded-2xl p-8 border border-gray-600">
              <h3 class="text-2xl font-bold text-white mb-4">Interested in More Career Paths?</h3>
              <p class="text-gray-300 mb-6">
                Explore other careers or use our AI to discover personalised paths based on your interests.
              </p>
              <div class="flex flex-wrap justify-center gap-4">
                <%= link_to "/careers", 
                    class: "inline-flex items-center px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white font-bold rounded-lg transition-all duration-300 hover:scale-105" do %>
                  <i class="fas fa-arrow-left mr-2"></i>
                  Browse All Careers
                <% end %>
                <%= link_to "/careers/generate", 
                    class: "inline-flex items-center px-6 py-3 bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 text-white font-bold rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-lg" do %>
                  <i class="fas fa-robot mr-2"></i>
                  Generate New Career Path
                <% end %>
              </div>
            </div>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</div>

<script>
    <% if @result.present? && @result["careerPath"].present? %>
      var stages = <%= @result["careerPath"].to_json.html_safe %>;
    <% end %>

    function scrollToElement(id) {
        const el = document.getElementById(id);
        if (el) {
            el.scrollIntoView({behavior: 'smooth'});
        }
    }

    var careerLoad = document.getElementById('careerLoad');
    var careerFacts = document.getElementById('careerLoad-text');
    var furtherCareerLoad = document.getElementById('furtherCareerLoad');
    <% if @age.present? %>
      const age = <%= @age.to_json.html_safe %>;
    <% end %>

    function generateAudioForSection(id, section) {
      const {keyword} = section;

      var btn = document.getElementById(`btn-${keyword}`);
      var audioSpan = document.getElementById(`audio-${keyword}`);

      if (!btn || !audioSpan) {
        console.warn("Button or audio span not found for section:", keyword);
        return;
      }

      var audioEl = audioSpan.querySelector("audio");
      var audioSrc = audioEl ? audioEl.querySelector("source") : null;

      btn.disabled = true;
      // Update button styling for new design
      btn.classList.add('opacity-60', 'cursor-not-allowed');
      btn.classList.remove('hover:from-cyan-600', 'hover:to-purple-500');
      btn.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';

      fetch(`/career-builder/${id}/generate_audio_for_section`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({section})
      }).then(response => response.json())
        .then(data => {
          if (audioSrc) {
            audioSrc.src = "https://www.developingexperts.com/file-cdn/files/get/" + data.result.fileboy_audio_id;
            audioEl.load();
          }

          btn.style.display = 'none';
          audioSpan.classList.remove('hidden');
        })
        .catch(e => {
          console.error(e);
          btn.disabled = false;
          btn.classList.remove('opacity-60', 'cursor-not-allowed');
          btn.classList.add('hover:from-cyan-600', 'hover:to-purple-500');
          btn.innerHTML = '<i class="fa-solid fa-volume-up"></i>';
        })
    };

    function regenerateCareerPathV2(careerPathId, withSiblings = false) {
        const logUUID = crypto.randomUUID();
        
        fetch(`/career-builder/${careerPathId}/regenerate_career_path_v2`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({logUUID, with_siblings: withSiblings})
        }).then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Reload the page to show generating state
                    window.location.reload();
                } else {
                    alert('Failed to start regeneration. Please try again.');
                }
            })
            .catch(e => {
                console.error(e);
                alert('An error occurred. Please try again.');
            });
    }

    <% if @user_career_path.present? %>
      const favIcon = document.getElementById("favourite-icon");
      let isFavourite = <%= @user_career_path&.is_favourite || false %>;

      function toggleFavouriteCareerPath(id) {
          fetch(`/career-builder/${id}/toggle_favourite_career_path`, {
              method: 'POST',
              headers: {
                  'Content-Type': 'application/json',
              },
          }).then(response => response.json())
              .then(data => {
                  isFavourite = !isFavourite;
                  if (isFavourite) {
                      favIcon.classList.remove("fa-regular");
                      favIcon.classList.add("fa-solid");
                      if (window.showToast) showToast('Career Path added to favourites');
                  } else {
                      favIcon.classList.remove("fa-solid");
                      favIcon.classList.add("fa-regular");
                      if (window.showToast) showToast('Career Path removed from favourites');
                  }
              }).catch(e => {
              console.error(e);
          })
      }
    <% end %>

    const textInput = document.getElementById("changes");
    if (textInput) {
        textInput.addEventListener("keypress", function (e) {
            if (e.key === "Enter") {
                e.preventDefault();
                const submitBtn = document.getElementById("submitChanges");
                if (submitBtn) submitBtn.click();
            }
        });
    }
</script>
