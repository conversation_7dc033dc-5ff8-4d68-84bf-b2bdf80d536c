<% content_for :title, "AI Career Search V2" %>

<div class="min-h-screen bg-gray-900">
  <!-- Banner Section -->
  <div class="relative overflow-hidden bg-gradient-to-r from-cyan-900/80 via-blue-900/80 to-purple-900/80 py-12">
    <!-- Subtle Background Elements -->
    <div class="absolute inset-0">
      <!-- Grid <PERSON> -->
      <div class="absolute inset-0 opacity-3">
        <div class="grid grid-cols-12 gap-4 h-full">
          <% (1..12).each do |i| %>
            <div class="border-r border-white/10"></div>
          <% end %>
        </div>
      </div>
      
      <!-- Minimal Floating Icons -->
      <div class="absolute top-8 right-1/4 text-cyan-300/20 text-xl">
        <i class="fas fa-robot"></i>
      </div>
      <div class="absolute bottom-8 left-1/3 text-blue-300/20 text-lg">
        <i class="fas fa-magic"></i>
      </div>
    </div>
    
    <div class="relative max-w-screen-xl mx-auto px-4">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
        <!-- Left Content -->
        <div class="lg:col-span-2">
          <!-- Breadcrumbs -->
          <div class="mb-4">
            <%= render_breadcrumbs(dark_text: false) %>
          </div>
          
          <h1 class="text-4xl md:text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-300 to-purple-300 mb-4">
            Find a career path to 
            <span class="text-transparent bg-clip-text bg-gradient-to-r from-yellow-300 to-orange-400">explore today</span>
          </h1>
          
          <p class="text-lg text-gray-200 mb-6 leading-relaxed">
            Discover a personalised career journey with our cutting-edge AI tool. Get tailored resources, 
            insights, and step-by-step guides to help you succeed.
          </p>
        </div>
        
        <!-- Right Content - AI Icon -->
        <div class="relative">
          <div class="bg-gradient-to-br from-white/5 to-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/10">
            <div class="text-center">
              <div class="text-6xl text-cyan-300/60 mb-4">
                <i class="fas fa-robot"></i>
              </div>
              <h3 class="text-xl font-bold text-white mb-2">AI Career Generator</h3>
              <p class="text-gray-300 text-sm">Powered by next-gen AI technology</p>
            </div>
          </div>
          
          <!-- Decorative dots -->
          <div class="absolute -top-1 -right-1 w-3 h-3 bg-cyan-400 rounded-full animate-pulse"></div>
          <div class="absolute -bottom-1 -left-1 w-2 h-2 bg-purple-400 rounded-full animate-pulse delay-300"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="max-w-screen-xl mx-auto py-12 px-4">
    <div class="max-w-screen-xl mx-auto">
      
      <!-- Two Column Layout: AI Generator and Interactive Advisor -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        
        <!-- AI Career Generator - 2/3 width -->
        <div class="lg:col-span-2">
          <div class="de-ai-assistant-container is-static is-expanded">
            <div class="de-ai-assistant-content-wrapper p-4">
              <div class="de-ai-expandable-content" style="visibility: visible; opacity: 1; max-height: none;">
                <div class="mb-6">
                  <h3 class="text-white text-xl nova mb-4">Select education level</h3>
                  <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                    <button 
                      type="button" 
                      class="education-level-btn p-4 rounded-xl border-2 border-gray-600 text-white hover:border-blue-500 transition-all"
                      data-level="primary"
                      data-age="8"
                    >
                      <div class="font-semibold nova">Primary</div>
                      <div class="text-sm text-slate-400">Ages 5-11</div>
                    </button>
                    <button 
                      type="button" 
                      class="education-level-btn p-4 rounded-xl border-2 border-gray-600 text-white hover:border-blue-500 transition-all"
                      data-level="secondary"
                      data-age="13"
                    >
                      <div class="font-semibold nova">Secondary</div>
                      <div class="text-sm text-slate-400">Ages 11-16</div>
                    </button>
                    <button 
                      type="button" 
                      class="education-level-btn p-4 rounded-xl border-2 border-gray-600 text-white hover:border-blue-500 transition-all"
                      data-level="post-16"
                      data-age="21"
                    >
                      <div class="font-semibold nova">Post-16</div>
                      <div class="text-sm text-slate-400">Ages 16+</div>
                    </button>
                  </div>
                  <p id="educationLevelError" class="text-red-500 text-sm mt-2" style="display: none;">Education level must be selected.</p>
                </div>

                <div class="mb-6">
                  <h3 class="text-white text-xl nova mb-4">Enter career to explore</h3>
                  <div class="de-ai-input-wrapper">
                    <input 
                      name="career" 
                      id="career" 
                      placeholder="e.g. Software Engineer, Teacher, Doctor..." 
                      value="<%= params[:name] %>"
                      class="de-ai-textarea"
                      style="min-height: 50px; padding-right: 50px;"
                      required
                    >
                    <button 
                      type="submit" 
                      id="submitCareer" 
                      class="de-ai-submit-btn" 
                      onclick="generateCareerPathV2()"
                    >
                      <i class="fa-light fa-paper-plane text-sm"></i>
                    </button>
                  </div>
                  <p id="careerError" class="text-red-500 text-sm mt-2" style="display: none;">Career name is required.</p>
                </div>

                <!-- Error Display -->
                <div id="careerInappropriate" class="p-4 border border-red-500 rounded-xl bg-red-500/10" style="display: none;">
                  <div class="flex items-start gap-3">
                    <div class="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <i class="fa-solid fa-triangle-exclamation text-white text-xs"></i>
                    </div>
                    <div>
                      <h4 class="text-red-400 font-semibold mb-1">Inappropriate Career</h4>
                      <p id="inappropriateExplain" class="text-red-300 text-sm"></p>
                    </div>
                  </div>
                </div>

                <!-- AI Generation Progress -->
                <div id="generationContainer" class="mt-6 p-6 border border-gray-600 rounded-xl bg-gray-800/50" style="display: none;">
                  <div class="flex items-center gap-3 mb-4">
                    <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-cyan-500 rounded-full flex items-center justify-center">
                      <i class="fa fa-robot text-white text-sm"></i>
                    </div>
                    <div>
                      <h4 class="text-white font-semibold nova">AI Career Assistant</h4>
                      <p id="statusMessage" class="text-slate-400 text-sm">Starting generation...</p>
                    </div>
                  </div>
                  
                  <div class="w-full bg-gray-700 rounded-full h-2 mb-4">
                    <div id="progressBar" class="progress-bar-animated h-2 rounded-full transition-all duration-500" style="width: 0%"></div>
                  </div>
                  
                  <div id="factContainer" class="text-white">
                    <p class="text-slate-300 mb-2 font-semibold">💡 Did you know?</p>
                    <p id="currentFact" class="text-lg fact-display">Preparing interesting facts about your career choice...</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Interactive Career Advisor - 1/3 width -->
        <div class="lg:col-span-1">
          <div class="bg-gradient-to-br from-purple-900/30 to-pink-900/30 backdrop-blur-sm rounded-2xl p-6 border border-purple-500/20 h-full">
            <div class="text-center">
              <!-- Icon -->
              <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-user-graduate text-white text-2xl"></i>
              </div>
              
              <!-- Title -->
              <h3 class="text-xl font-bold text-white mb-3">Interactive Career Advisor</h3>
              
              <!-- Description -->
              <p class="text-gray-300 text-sm mb-6 leading-relaxed">
                Get personalised career guidance through our interactive questionnaire and AI-powered recommendations.
              </p>
              
              <!-- CTA Button -->
              <% if @current_user.present? %>
                <%= link_to careers_interactive_path, id: "load-lead-link", class: "btn-gradient btn btn-purple w-full px-4 py-3 rounded-xl font-semibold hover:scale-105 transition-transform" do %>
                  <i class="fas fa-arrow-right mr-2"></i>
                  Start Interactive Advisor
                <% end %>
              <% else %>
                <div class="text-center">
                  <p class="text-slate-300 mb-4 text-sm">
                    Sign in to access our interactive career advisor
                  </p>
                  <a href="/accounts/new" class="btn-gradient btn btn-purple w-full px-4 py-3 rounded-xl font-semibold inline-block hover:scale-105 transition-transform">
                    <i class="fas fa-user-plus mr-2"></i>
                    Start Free Trial
                  </a>
                </div>
              <% end %>
              
              <!-- Features list -->
              <div class="mt-6 pt-6 border-t border-purple-500/20">
                <ul class="text-xs text-gray-400 space-y-2">
                  <li class="flex items-center">
                    <i class="fas fa-check text-purple-400 mr-2"></i>
                    Personalised questionnaire
                  </li>
                  <li class="flex items-center">
                    <i class="fas fa-check text-purple-400 mr-2"></i>
                    AI-powered matching
                  </li>
                  <li class="flex items-center">
                    <i class="fas fa-check text-purple-400 mr-2"></i>
                    Career pathway guidance
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Favourite Careers Section -->
      <% if @last_favourite_user_careers.present? %>
        <div class="mb-8">
          <div class="flex flex-col md:flex-row gap-4 md:items-center justify-between mb-6">
            <h2 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-300 to-purple-300">
              Favourite Career Paths
            </h2>
            <%= link_to careers_favourites_path, class: 'inline-flex items-center px-6 py-3 bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white font-bold rounded-lg transition-all duration-300 hover:scale-105' do %>
              <i class="fas fa-heart mr-2"></i>
              View All Saved Careers
            <% end %>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6">
            <% @last_favourite_user_careers.each do |user_career_path| %>
              <% career = user_career_path.career_path %>
              <%= render 'static/career_builder/career_card', career: career, is_favourite: true %>
            <% end %>
          </div>
        </div>
      <% else %>
        <div class="bg-gradient-to-r from-gray-800 to-gray-700 rounded-2xl p-8 border border-gray-600">
          <h3 class="text-2xl font-bold text-white mb-4">Explore Your Ideal Career Path</h3>
          <p class="text-gray-300 mb-6">
            Whether you're exploring new opportunities or advancing your career, our AI-driven career advisor 
            will assist you in making informed decisions and connecting you with the right information. 
            Start your career exploration today!
          </p>
          
          <!-- Quick Links -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <%= link_to careers_search_path, class: "flex items-center p-4 bg-white/5 rounded-lg border border-white/10 hover:bg-white/10 transition-colors group" do %>
              <div class="text-2xl text-purple-400 mr-4 group-hover:scale-110 transition-transform">
                <i class="fas fa-search"></i>
              </div>
              <div>
                <h4 class="text-white font-semibold">Search Careers</h4>
                <p class="text-gray-400 text-sm">Browse our database of careers</p>
              </div>
            <% end %>
            
            <%= link_to job_families_path, class: "flex items-center p-4 bg-white/5 rounded-lg border border-white/10 hover:bg-white/10 transition-colors group" do %>
              <div class="text-2xl text-cyan-400 mr-4 group-hover:scale-110 transition-transform">
                <i class="fas fa-briefcase"></i>
              </div>
              <div>
                <h4 class="text-white font-semibold">Job Families</h4>
                <p class="text-gray-400 text-sm">Explore career categories</p>
              </div>
            <% end %>
            
            <%= link_to careers_index_path, class: "flex items-center p-4 bg-white/5 rounded-lg border border-white/10 hover:bg-white/10 transition-colors group" do %>
              <div class="text-2xl text-blue-400 mr-4 group-hover:scale-110 transition-transform">
                <i class="fas fa-home"></i>
              </div>
              <div>
                <h4 class="text-white font-semibold">Career Hub</h4>
                <p class="text-gray-400 text-sm">Return to careers home</p>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>

<%= render ModalComponent.new(
  title: "Subscription Required",
  id: "subscriptionDialog",
  external_control: true,
  disable_close: true,
) do |modal| %>
  <div class="mb-4">
    <div class="w-12 h-12 mx-auto bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
      <i class="fas fa-lock text-white text-2xl"></i>
    </div>
  </div>
  <% if @current_user.pupil? %>
    <p class="text-gray-700 mb-6">
      Your school needs to subscribe to access our AI-powered career services. 
    </p>
  <% else %>
    <p class="text-gray-700 mb-6">
      You need to subscribe to access our AI-powered career services. Unlock personalised career guidance and advanced features by upgrading your account.
    </p>
    <% modal.with_footer do %>
      <%= link_to new_subscription_path, class: "btn-gradient btn btn-purple w-full px-4 py-3 rounded-xl font-semibold inline-block hover:scale-105 transition-transform mb-2" do %>
        <i class="fas fa-arrow-up-right-from-square mr-2"></i>
        Upgrade Now
      <% end %>
    <% end %>
  <% end %>

<% end %>
<% if @requires_ai_access %>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Check if the subscription dialog is already open
    var dialog = document.getElementById('subscriptionDialog');
    if (dialog) {
      // If so, show it immediately
      dialog.showModal();
    }
  });
  // You can call showSubscriptionDialog() when access is restricted

  (function(){
    document.addEventListener('DOMContentLoaded', function() {
      function triggerModal() {
        if (document.body && document.body.style) {
          document.body.style.overflow = 'hidden';
          document.documentElement.style.overflow = 'hidden';
        }
        var d = document.getElementById('subscriptionDialog');
        if (d && !d.hasCancelListener) {
          d.addEventListener('cancel', function(e) {
            e.preventDefault();
          });
          d.hasCancelListener = true;
        }
        if(!(d && d.parentNode)){
          location.reload();
          clearInterval(x);
        }
        if(d && !d.open) {
          window.location.reload()
        }
      }
      triggerModal()
      var x = setInterval(function(){
        triggerModal()
      }, 987);
    });
  })();
</script>
<% end %>

<script>
    var careerLoad = document.getElementById("generateCareersLoad");
    var careerFacts = document.getElementById("generateCareersLoad-text");
    var leadLoad = document.getElementById("leadLoad");
    var educationLevelError = document.getElementById("educationLevelError");
    var careerError = document.getElementById("careerError");
    var generationContainer = document.getElementById("generationContainer");
    var progressBar = document.getElementById("progressBar");
    var statusMessage = document.getElementById("statusMessage");
    var currentFact = document.getElementById("currentFact");

    var selectedEducationLevel = null;
    var selectedAge = null;

    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded, setting up education level buttons');
        
        // Handle education level button selection
        document.querySelectorAll('.education-level-btn').forEach(button => {
            button.addEventListener('click', function() {
                console.log('Education level button clicked:', this.dataset.level);
                
                // Remove selected state from all buttons
                document.querySelectorAll('.education-level-btn').forEach(btn => {
                    btn.classList.remove('border-blue-500', 'bg-blue-600');
                    btn.classList.add('border-slate-600', 'bg-slate-800');
                });
                
                // Add selected state to clicked button
                this.classList.remove('border-slate-600', 'bg-slate-800');
                this.classList.add('border-blue-500', 'bg-blue-600');
                
                selectedEducationLevel = this.dataset.level;
                selectedAge = parseInt(this.dataset.age);
                
                console.log('Selected:', { educationLevel: selectedEducationLevel, age: selectedAge });
                
                educationLevelError.style.display = 'none';
            });
        });

        // Set default selection based on URL params
        const urlEducationLevel = '<%= params[:education_level] %>';
        if (urlEducationLevel) {
            const button = document.querySelector(`[data-level="${urlEducationLevel}"]`);
            if (button) {
                button.click();
            }
        }
        
        // Setup career advisor button
        const loadLeadBtn = document.getElementById('load-lead');
        if (loadLeadBtn) {
            loadLeadBtn.addEventListener('click', function (e) {
                educationLevelError.style.display = 'none';
                careerError.style.display = 'none';

                if (!selectedEducationLevel) {
                    e.preventDefault();
                    educationLevelError.style.display = 'block';
                } else {
                    var loadLeadLink = document.getElementById('load-lead-link');
                    var logUUID = crypto.randomUUID();
                    loadLeadLink.href = '<%= lead_career_builder_index_path %>?age=' + encodeURIComponent(selectedAge) + '&uuid=' + encodeURIComponent(logUUID);
                    leadLoad.style.display = 'block';
                }
            });
        }

        // Setup career input enter key handler
        const careerInput = document.getElementById("career");
        if (careerInput) {
            careerInput.addEventListener("keypress", function (e) {
                if (e.key === "Enter") {
                    e.preventDefault();
                    document.getElementById("submitCareer").click();
                }
            });
        } else {
            console.error('Career input field not found!');
        }
    });

    // Helper functions
    function updateProgressBar(progress) {
        if (progressBar) {
            progressBar.style.width = `${progress}%`;
        }
    }

    function updateStatusMessage(message) {
        if (statusMessage) {
            statusMessage.textContent = message;
        }
    }

    function showErrorMessage(error, header) {
        const careerInappropriate = document.getElementById('careerInappropriate');
        const inappropriateExplain = document.getElementById('inappropriateExplain');
        const headerNode = inappropriateExplain ? inappropriateExplain.previousElementSibling : null;
        
        if (careerInappropriate && inappropriateExplain) {
            inappropriateExplain.textContent = error;
            careerInappropriate.style.display = 'flex';
        }

        if(header && headerNode) {
          headerNode.textContent = header;
        } else if (headerNode) {
          // the original / fallback
          headerNode.textContent = "Inappropriate Career";
        } else {
          console.warn('Header node not found for error message');
        }
        
        hideGenerationContainer();
    }

    function hideGenerationContainer() {
        generationContainer.style.display = 'none';
    }

    function showGenerationContainer() {
        generationContainer.style.display = 'block';
    }

    function displayFactsV2(career, age) {
        currentFact.textContent = `Loading interesting facts about ${career}...`;
        
        console.log('Requesting facts for:', career, 'age:', age);
        
        return new Promise((resolve, reject) => {
            fetch('/career-builder/generate_facts_for_career_v2', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({career, age, logUUID: ""})
            }).then(response => {
                console.log('Facts response status:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            }).then(data => {
                console.log('Facts response data:', data);
                
                if (data.success && data.result && data.result.facts && data.result.facts.length > 0) {
                    let i = 0;
                    const facts = data.result.facts;
                    
                    console.log('Got facts:', facts);

                    function displayNextFact() {
                        currentFact.textContent = facts[i];
                        i = (i + 1) % facts.length;
                    }

                    // Show first fact immediately
                    displayNextFact();
                    // Then rotate through them
                    const factInterval = setInterval(displayNextFact, 8000);
                    
                    resolve();
                } else {
                    console.log('No facts in response or request failed:', data);
                    currentFact.textContent = `Generating career insights for ${career}...`;
                    resolve();
                }
            }).catch(e => {
                console.error('Facts request failed:', e);
                currentFact.textContent = `Error loading facts: ${e.message}`;
                reject(e); // Reject to expose the actual error
            });
        });
    }

    // Career Generation Status Poller for V2
    class CareerGenerationPoller {
        constructor(options = {}) {
            this.careerPathId = options.careerPathId;
            this.userId = options.userId;
            this.pollInterval = options.pollInterval || 2000; // 2 seconds
            this.maxAttempts = options.maxAttempts || 150; // 5 minutes max
            this.onStatusUpdate = options.onStatusUpdate || (() => {});
            this.onComplete = options.onComplete || (() => {});
            this.onError = options.onError || (() => {});
            
            this.attempts = 0;
            this.polling = false;
            this.timeoutId = null;
        }

        start() {
            if (this.polling) return;
            
            this.polling = true;
            this.attempts = 0;
            this.poll();
        }

        stop() {
            this.polling = false;
            if (this.timeoutId) {
                clearTimeout(this.timeoutId);
                this.timeoutId = null;
            }
        }

        async poll() {
            if (!this.polling || this.attempts >= this.maxAttempts) {
                this.stop();
                if (this.attempts >= this.maxAttempts) {
                    this.onError('Polling timeout - generation taking too long');
                }
                return;
            }

            this.attempts++;

            try {
                const response = await fetch(`/career-builder/career_path_status_v2?career_path_id=${this.careerPathId}&user_id=${this.userId}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                const data = await response.json();

                if (data.success) {
                    this.onStatusUpdate(data);

                    if (data.status === 'completed') {
                        this.stop();
                        this.onComplete(data);
                        return;
                    } else if (data.status === 'error' || data.status === 'flagged') {
                        this.stop();
                        this.onError(data.message || 'Generation failed');
                        return;
                    }
                } else {
                    console.warn('Status check failed:', data.message);
                }

            } catch (error) {
                console.error('Error polling status:', error);
            }

            // Schedule next poll
            if (this.polling) {
                this.timeoutId = setTimeout(() => this.poll(), this.pollInterval);
            }
        }
    }

    function generateCareerPathV2() {
        var logUUID = crypto.randomUUID();

        console.log('generateCareerPathV2 called');

        const careerInput = document.getElementById('career');
        if (!careerInput) {
            console.error('Career input field not found!');
            alert('Error: Career input field not found. Please refresh the page.');
            return;
        }

        const career = careerInput.value.trim();

        const careerInappropriate = document.getElementById('careerInappropriate');
        const careerInappropriateText = document.getElementById('inappropriateExplain');

        educationLevelError.style.display = 'none';
        careerError.style.display = 'none';
        careerInappropriate.style.display = 'none';

        var isError = false;

        if (!selectedEducationLevel) {
            educationLevelError.style.display = 'block';
            isError = true;
        }

        if (career === "") {
            careerError.style.display = 'block';
            isError = true;
        }

        if (isError) {
            console.error('Validation failed:', { selectedEducationLevel, career });
            return;
        }

        console.log("STARTING V2 GENERATION", { career, educationLevel: selectedEducationLevel, age: selectedAge });

        showGenerationContainer();
        updateStatusMessage("Loading interesting facts...");
        updateProgressBar(10);

        // First load facts, then start generation
        displayFactsV2(career, selectedAge).then(() => {
            updateStatusMessage("Starting career generation...");
            updateProgressBar(25);
            
            return fetch('/career-builder/generate_career_path_v2', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    career, 
                    age: selectedAge, 
                    education_level: selectedEducationLevel,
                    logUUID
                })
            });
        }).then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStatusMessage("Generation in progress...");
                updateProgressBar(50);
                
                // Start polling for status updates
                const poller = new CareerGenerationPoller({
                    careerPathId: data.career_path_id,
                    userId: <%= @user&.id || 'null' %>,
                    onStatusUpdate: (statusData) => {
                        console.log('Status update:', statusData);
                        updateProgressBar(Math.max(50, statusData.progress || 60));
                        updateStatusMessage(statusData.message || 'Generating your career path...');
                    },
                    onComplete: (statusData) => {
                        console.log('Generation complete!');
                        updateProgressBar(100);
                        updateStatusMessage('Complete! Redirecting...');
                        setTimeout(() => {
                            window.location.href = `/career-builder/${statusData.career_path_id}/show_v2`;
                        }, 1000);
                    },
                    onError: (error) => {
                        console.error('Generation failed:', error);
                        showErrorMessage(error, "Error Generating Career Path");
                    }
                });
                
                poller.start();
            } else {
                if (data.result && data.result.flagged) {
                    showErrorMessage(`This career request was flagged as inappropriate. ${data.result.explanation}`);
                } else {
                    showErrorMessage('Failed to start generation. Please try again.');
                }
            }
        })
        .catch(e => {
            console.error('Generation error:', e);
            showErrorMessage('An error occurred. Please try again.');
        });
    }
</script>
