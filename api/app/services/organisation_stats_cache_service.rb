class OrganisationStatsCacheService
  def self.get_lesson_kpi_stats(campaign_ids, data, filters)
    query = <<-SQL
      WITH
        [#{campaign_ids}] AS campaign_ids,
        user_interactions AS (
          SELECT
            JSONExtractString(properties, 'lesson_template_id') AS lesson_template_id,
            formatDateTime(timestamp, '%Y-%m') AS month,
            JSONExtractString(properties, 'user_id') AS user_id,
            any(JSONExtractString(properties, 'school_id')) AS school_id,
            any(JSONExtractString(properties, 'school_name')) AS school_name,
            any(JSONExtractString(properties, 'school_postcode')) AS school_postcode,
            any(JSONExtractString(properties, 'user_dob')) AS dob,
            any(JSONExtractString(properties, 'user_gender')) AS gender,
            any(JSONExtractString(properties, 'user_ethnicity')) AS ethnicity,
            any(JSONExtractString(properties, 'school_region')) AS region,
            COUNT(*) AS interactions
          FROM events
          WHERE
            event = 'lesson template viewed'
            AND timestamp >= now() - INTERVAL 2 YEAR
            AND (
              length(campaign_ids) = 0
              OR length(arrayIntersect(arrayMap(x -> toString(x), ifNull(JSONExtractArrayRaw(properties, 'lesson_template_campaign_ids'), [])), campaign_ids)) > 0
            )
            AND match(JSONExtractString(properties, 'school_id'), '^[0-9]+$') = 1
            AND match(JSONExtractString(properties, 'lesson_template_id'), '^[0-9]+$') = 1
            AND (
              (#{filters.key?(:gender) ? 1 : 0} = 0 OR JSONExtractString(properties, 'user_gender') = '#{data[:gender]}')
              AND (#{filters.key?(:age) ? 1 : 0} = 0 OR (
                JSONExtractString(properties, 'user_dob') IS NOT NULL
                AND toDateTime(JSONExtractString(properties, 'user_dob')) >= toDateTime('#{data[:dob_start]}')
                AND toDateTime(JSONExtractString(properties, 'user_dob')) <= toDateTime('#{data[:dob_end]}')
              ))
              AND (#{filters.key?(:ethnicity) ? 1 : 0} = 0 OR JSONExtractString(properties, 'user_ethnicity') = '#{data[:ethnicity]}')
              AND (#{filters.key?(:region) ? 1 : 0} = 0 OR JSONExtractString(properties, 'school_region') = '#{data[:region]}')
            )
          GROUP BY
            lesson_template_id, month, user_id
        )
      SELECT
        lesson_template_id,
        month,
        groupArray(tuple(user_id, school_id, school_name, school_postcode, dob, gender, ethnicity, region, interactions)) AS results
      FROM user_interactions
      GROUP BY
        lesson_template_id, month
      ORDER BY
        lesson_template_id, month
      LIMIT 1000
    SQL

    data = TrackingService.query(query)
    raise "HogQL query failed: #{data['error']}" if data['error']

    # Transform and return mapped results directly
    flattened_results = []
    data['results'].each do |row|
      lesson_template_id = row[0]
      month = row[1]
      user_data_array = row[2] || []

      user_data_array.each do |user_tuple|
        flattened_results << {
          'school_id' => user_tuple[1],
          'lesson_template_id' => lesson_template_id,
          'month' => month,
          'school_name' => user_tuple[2],
          'school_postcode' => user_tuple[3],
          'dob' => user_tuple[4],
          'gender' => user_tuple[5],
          'ethnicity' => user_tuple[6],
          'region' => user_tuple[7],
          'user_id' => user_tuple[0],
          'engagement' => user_tuple[8]
        }
      end
    end

    flattened_results
  end

  def self.get_unit_kpi_stats(campaign_ids, data, filters)
    query = <<-SQL
      WITH
        [#{campaign_ids}] AS campaign_ids,
        user_interactions AS (
          SELECT
            JSONExtractString(properties, 'unit_id') AS unit_id,
            formatDateTime(timestamp, '%Y-%m') AS month,
            JSONExtractString(properties, 'user_id') AS user_id,
            any(JSONExtractString(properties, 'school_id')) AS school_id,
            any(JSONExtractString(properties, 'school_name')) AS school_name,
            any(JSONExtractString(properties, 'school_postcode')) AS school_postcode,
            any(JSONExtractString(properties, 'user_dob')) AS dob,
            any(JSONExtractString(properties, 'user_gender')) AS gender,
            any(JSONExtractString(properties, 'user_ethnicity')) AS ethnicity,
            any(JSONExtractString(properties, 'school_region')) AS region,
            COUNT(*) AS interactions
          FROM events
          WHERE
            event = 'unit viewed'
            AND timestamp >= now() - INTERVAL 2 YEAR
            AND (
              length(campaign_ids) = 0
              OR length(arrayIntersect(arrayMap(x -> toString(x), ifNull(JSONExtractArrayRaw(properties, 'unit_campaign_ids'), [])), campaign_ids)) > 0
            )
            AND match(JSONExtractString(properties, 'school_id'), '^[0-9]+$') = 1
            AND match(JSONExtractString(properties, 'unit_id'), '^[0-9]+$') = 1
            AND (
              (#{filters.key?(:gender) ? 1 : 0} = 0 OR JSONExtractString(properties, 'user_gender') = '#{data[:gender]}')
              AND (#{filters.key?(:age) ? 1 : 0} = 0 OR (
                JSONExtractString(properties, 'user_dob') IS NOT NULL
                AND toDateTime(JSONExtractString(properties, 'user_dob')) >= toDateTime('#{data[:dob_start]}')
                AND toDateTime(JSONExtractString(properties, 'user_dob')) <= toDateTime('#{data[:dob_end]}')
              ))
              AND (#{filters.key?(:ethnicity) ? 1 : 0} = 0 OR JSONExtractString(properties, 'user_ethnicity') = '#{data[:ethnicity]}')
              AND (#{filters.key?(:region) ? 1 : 0} = 0 OR JSONExtractString(properties, 'school_region') = '#{data[:region]}')
            )
          GROUP BY
            unit_id, month, user_id
        )
      SELECT
        unit_id,
        month,
        groupArray(tuple(user_id, school_id, school_name, school_postcode, dob, gender, ethnicity, region, interactions)) AS results
      FROM user_interactions
      GROUP BY
        unit_id, month
      ORDER BY
        unit_id, month
      LIMIT 1000
    SQL

    data = TrackingService.query(query)
    raise "HogQL query failed: #{data['error']}" if data['error']

    # Transform and return mapped results directly
    flattened_results = []
    data['results'].each do |row|
      unit_id = row[0]
      month = row[1]
      user_data_array = row[2] || []

      user_data_array.each do |user_tuple|
        flattened_results << {
          'school_id' => user_tuple[1],
          'unit_id' => unit_id,
          'month' => month,
          'school_name' => user_tuple[2],
          'school_postcode' => user_tuple[3],
          'dob' => user_tuple[4],
          'gender' => user_tuple[5],
          'ethnicity' => user_tuple[6],
          'region' => user_tuple[7],
          'user_id' => user_tuple[0],
          'engagement' => user_tuple[8]
        }
      end
    end

    flattened_results
  end

  def self.get_school_kpi_stats(campaign_ids, data, filters)
    query = <<-SQL
      WITH
        [#{campaign_ids}] AS campaign_ids
      SELECT
        JSONExtractString(properties, 'school_id') AS school_id,
        anyIf(
          JSONExtractString(properties, 'school_name'),
          JSONExtractString(properties, 'school_name') IS NOT NULL AND JSONExtractString(properties, 'school_name') != ''
        ) AS school_name,
        anyIf(
          JSONExtractString(properties, 'school_postcode'),
          JSONExtractString(properties, 'school_postcode') IS NOT NULL AND JSONExtractString(properties, 'school_postcode') != ''
        ) AS school_postcode,
        any(JSONExtractString(properties, 'school_region')) AS region,
        SUM(
          IF(
            (
              (#{filters.key?(:gender) ? 1 : 0} = 0 OR JSONExtractString(properties, 'user_gender') = '#{data[:gender]}')
              AND (#{filters.key?(:age) ? 1 : 0} = 0 OR (
                JSONExtractString(properties, 'user_dob') IS NOT NULL
                AND toDateTime(JSONExtractString(properties, 'user_dob')) >= toDateTime('#{data[:dob_start]}')
                AND toDateTime(JSONExtractString(properties, 'user_dob')) <= toDateTime('#{data[:dob_end]}')
              ))
              AND (#{filters.key?(:ethnicity) ? 1 : 0} = 0 OR JSONExtractString(properties, 'user_ethnicity') = '#{data[:ethnicity]}')
              AND (#{filters.key?(:region) ? 1 : 0} = 0 OR JSONExtractString(properties, 'school_region') = '#{data[:region]}')
            ),
            1, 0
          )
        ) AS engagement
      FROM events
      WHERE
        event = 'lesson template viewed'
        AND (
          length(campaign_ids) = 0
          OR length(arrayIntersect(arrayMap(x -> toString(x), ifNull(JSONExtractArrayRaw(properties, 'lesson_template_campaign_ids'), [])), campaign_ids)) > 0
        )
        AND match(JSONExtractString(properties, 'school_id'), '^[0-9]+$') = 1
      GROUP BY
        school_id
      HAVING
        engagement > 0
      LIMIT 10000
    SQL

    data = TrackingService.query(query)
    raise "HogQL query failed: #{data['error']}" if data['error']

    # Transform and return mapped results directly
    results = []
    data['results'].each do |row|
      results << {
        'school_id' => row[0],
        'school_name' => row[1],
        'school_postcode' => row[2],
        'region' => row[3],
        'engagement' => row[4]
      }
    end

    results
  end

  def self.get_career_kpi_stats(data, filters)
    query = <<-SQL
      WITH
        user_interactions AS (
          SELECT
            JSONExtractString(properties, 'career_id') AS career_id,
            JSONExtractString(properties, 'career_type') AS career_type,
            formatDateTime(timestamp, '%Y-%m') AS month,
            JSONExtractString(properties, 'user_id') AS user_id,
            any(JSONExtractString(properties, 'school_id')) AS school_id,
            any(JSONExtractString(properties, 'school_name')) AS school_name,
            any(JSONExtractString(properties, 'school_postcode')) AS school_postcode,
            any(JSONExtractString(properties, 'career_name')) AS career_name,
            any(JSONExtractString(properties, 'career_job_family')) AS career_job_family,
            any(JSONExtractString(properties, 'user_dob')) AS dob,
            any(JSONExtractString(properties, 'user_gender')) AS gender,
            any(JSONExtractString(properties, 'user_ethnicity')) AS ethnicity,
            any(JSONExtractString(properties, 'school_region')) AS region,
            COUNT(*) AS interactions
          FROM events
          WHERE
            event = 'career view'
            AND timestamp >= now() - INTERVAL 2 YEAR
            AND match(JSONExtractString(properties, 'school_id'), '^[0-9]+$') = 1
            AND match(JSONExtractString(properties, 'career_id'), '^[0-9]+$') = 1
            AND (
              (#{filters.key?(:gender) ? 1 : 0} = 0 OR JSONExtractString(properties, 'user_gender') = '#{data[:gender]}')
              AND (#{filters.key?(:age) ? 1 : 0} = 0 OR (
                JSONExtractString(properties, 'user_dob') IS NOT NULL
                AND toDateTime(JSONExtractString(properties, 'user_dob')) >= toDateTime('#{data[:dob_start]}')
                AND toDateTime(JSONExtractString(properties, 'user_dob')) <= toDateTime('#{data[:dob_end]}')
              ))
              AND (#{filters.key?(:ethnicity) ? 1 : 0} = 0 OR JSONExtractString(properties, 'user_ethnicity') = '#{data[:ethnicity]}')
              AND (#{filters.key?(:region) ? 1 : 0} = 0 OR JSONExtractString(properties, 'school_region') = '#{data[:region]}')
            )
          GROUP BY
            career_id, career_type, month, user_id
        )
      SELECT
        career_id,
        career_type,
        month,
        groupArray(tuple(user_id, school_id, school_name, school_postcode, career_name, career_job_family, dob, gender, ethnicity, region, interactions)) AS results
      FROM user_interactions
      GROUP BY
        career_id, career_type, month
      ORDER BY
        career_id, career_type, month
      LIMIT 1000
    SQL

    data = TrackingService.query(query)
    raise "HogQL query failed: #{data['error']}" if data['error']

    # Transform and return mapped results directly
    flattened_results = []
    data['results'].each do |row|
      career_id = row[0]
      career_type = row[1]
      month = row[2]
      user_data_array = row[3] || []

      user_data_array.each do |user_tuple|
        flattened_results << {
          'career_id' => career_id,
          'career_type' => career_type,
          'month' => month,
          'user_id' => user_tuple[0],
          'school_id' => user_tuple[1],
          'school_name' => user_tuple[2],
          'school_postcode' => user_tuple[3],
          'career_name' => user_tuple[4],
          'career_job_family' => user_tuple[5],
          'dob' => user_tuple[6],
          'gender' => user_tuple[7],
          'ethnicity' => user_tuple[8],
          'region' => user_tuple[9],
          'engagement' => user_tuple[10]
        }
      end
    end

    flattened_results
  end
end
