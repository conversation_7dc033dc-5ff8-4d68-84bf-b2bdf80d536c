# app/services/career_ai_generator_service.rb
class CareerAIGeneratorService
  include Rails.application.routes.url_helpers

  MAX_RETRIES = 3

  def initialize
    @ai_client = AiClient.new
  end

  def generate_career_path_by_id(id, regen = false, nothread = false)
    career_path = CareerPath.find(id)

    if regen
      career_path.update(status: "pending", generation_key: nil)
    end

    return if career_path.status == "flagged"
    
    if career_path.status == "completed"
      Rails.logger.info "Career path already completed for career path id: #{id}"
      return
    end

    handle_stuck_or_errored_generation(career_path)
    
    return if career_path.status == "generating"

    generation_key = generate_unique_key
    
    # Update status with generation key for concurrency control
    career_path.update(
      status: "generating", 
      generation_key: generation_key, 
      generation_started_at: Time.current
    )
    
    # Verify we got the lock
    career_path.reload
    if career_path.generation_key != generation_key
      Rails.logger.info "Generation key mismatch for career path id: #{id}"
      return
    end

    if nothread
      generate_career_path_inner(id)
    else
      Thread.new do
        begin
          generate_career_path_inner(id)
        rescue => e
          Sentry.capture_exception(e)
          Rails.logger.error("Thread error for career_path_id=#{id}: #{e.message}")
          Rails.logger.error(e.backtrace.join("\n"))
        end
      end
    end
  end

  def generate_career_path_inner(id)
    career_path = CareerPath.find(id)

    name = career_path.career_name

    # Step 1: Generate career path structure
    career_path_data = generate_career_path_structure(name, career_path.age)

    # Step 2: Generate career info
    career_info = generate_career_information(name, career_path.age)

    # Step 3: Generate further careers
    further_careers = generate_further_career_suggestions(name, career_path.age)

    # Step 4: Add images
    career_path_data['image'] = add_primary_image(id, name, career_path.job_family&.name)
    further_careers = add_images_to_career_suggestions(id, further_careers)
    related_careers = add_images_to_career_suggestions(id, { "careers" => career_info["relatedCareers"] })

    # Step 5: Get job family
    job_family = get_job_family_for_career_path(id, name, career_path.age)

    # Step 6: Process videos
    video_ids = process_career_videos(career_path_data, name)

    # Step 7: Combine all data
    career_info["further_careers"] = further_careers["careers"]
    career_info["relatedCareers"] = related_careers["careers"]

    qualifications = generate_qualifications(name)

    # Step 8: Save final result
    career_path.update(
      career_path: career_path_data,
      info: career_info,
      video_ids: video_ids,
      status: "completed",
      qualifications: qualifications,
      generation_key: nil
    )
  rescue => e
    handle_generation_error(career_path, e)
  end

  def sanitise_career_name(name)
    system_prompt = build_sanitisation_prompt
    user_prompt = name

    messages = @ai_client.build_messages(system_prompt, user_prompt)
    response = @ai_client.generate_response(
      messages: messages,
      response_type: :json,
      temperature: 0.3
    )

    if response['flagged']
      return { flagged: true, explanation: response['explanation'] }
    end

    { output: response['output'] }
  end

  def generate_qualifications(career_name)
    nc_service = NationalCareersService.new
    career_info = nc_service.get_career_info(career_name)
    info = career_info[:career] if career_info.present?
    qualitifications = career_info[:qualifications] if career_info.present?
    {
      career: info,
      qualifications: qualitifications,
    }
  rescue => e
    Rails.logger.error("Error fetching qualifications for #{career_name}: #{e.message}\n#{e.backtrace.join("\n")}")
    ErrorLog.create!(error: { errorData: e, backtrace: e.backtrace }.to_json)
    {} # Return empty hash if there's an error
  end

  def generate_career_path_structure(career_name, age)
    system_prompt = build_career_path_prompt(age)
    user_prompt = career_name
    
    messages = @ai_client.build_messages(system_prompt, user_prompt)
    @ai_client.generate_response(
      messages: messages,
      response_type: :json,
      temperature: 0.7,
      max_tokens: 2000
    )
  end

  def generate_career_information(career_name, age)
    system_prompt = build_career_info_prompt(age)
    user_prompt = career_name
    
    messages = @ai_client.build_messages(system_prompt, user_prompt)
    response = @ai_client.generate_response(
      messages: messages,
      response_type: :json,
      temperature: 0.7,
      max_tokens: 2000
    )
    
    response["info"]
  end

  def generate_further_career_suggestions(career_name, age)
    system_prompt = build_further_careers_prompt(age)
    user_prompt = career_name
    
    messages = @ai_client.build_messages(system_prompt, user_prompt)
    @ai_client.generate_response(
      messages: messages,
      response_type: :json,
      temperature: 0.7
    )
  end

  def generate_job_family_classification(career_name, age)
    families = JobFamily.distinct.pluck(:name).compact
    
    system_prompt = build_job_family_prompt(families)
    user_prompt = career_name
    
    messages = @ai_client.build_messages(system_prompt, user_prompt)
    response = @ai_client.generate_response(
      messages: messages,
      response_type: :json,
      temperature: 0.3
    )
    
    response["family"]
  end

  def process_career_videos(career_path_data, career_name)
    video_ids = []
    video_media_queries = career_path_data["videoMediaQueries"] || []
    video_media_queries << career_name
    video_media_queries.uniq!

    if video_media_queries.present?
      video_media_queries.each do |query|
        break if video_ids.size >= 3
        
        found = Video.where(is_career: true).search(query).limit(3).ids
        video_ids += found
        video_ids.uniq!
      end
    end

    video_ids.uniq.first(3)
  end

  def add_primary_image(id, sanitised_career, job_family_name = nil)
    # Generate AI-powered search query
    search_query = generate_image_search_query(sanitised_career, job_family_name)

    # Fallback to original method if AI fails
    if search_query.blank?
      search_query = sanitised_career.gsub(' ', '-')
    end
    
    image = PexelsImageService.new.search_for_career_path(id, search_query, 0)
    return image['medium'] if image.present?
    nil
  end

  def add_images_to_career_suggestions(career_path_id, careers)
    careers["careers"]&.each_with_index do |career, i|
      # Use AI-generated query for better results
      search_query = generate_image_search_query(career["name"])
      
      # Fallback to original method if AI fails
      if search_query.blank?
        search_query = career["name"].gsub(' ', '-')
      end
      
      image = PexelsImageService.new.search_for_career_path(career_path_id, search_query, i + 1)
      career["image"] = image["medium"] if image.present?
    end
    careers
  end

  def get_job_family_for_career_path(career_path_id, career_name, age)
    career_path = CareerPath.find_by(id: career_path_id)
    return unless career_path

    family_name = generate_job_family_classification(career_name, age)
    job_family = JobFamily.find_or_create_with_image(name: family_name)

    if career_path.update(job_family: job_family)
      Rails.logger.info("Updated career_path #{career_path_id} with job_family #{family_name}")
    else
      Rails.logger.error("Failed to update career_path #{career_path_id} with job_family #{family_name}")
    end

    job_family
  end

  def handle_stuck_or_errored_generation(career_path)
    has_errored = career_path.status == "error"
    is_stuck_generating = career_path.status == "generating" && 
                         career_path.generation_started_at < 10.minutes.ago

    if has_errored || is_stuck_generating
      career_path.update(status: "pending", generation_key: nil)
      
      # Increment retry attempts
      career_path.update(generation_attempts: (career_path.generation_attempts || 0) + 1)
      
      if career_path.generation_attempts > MAX_RETRIES
        Rails.logger.info "Max retries reached for career path id: #{career_path.id}"
        career_path.update(status: "error")
        return
      end
    end
  end

  def handle_generation_error(career_path, error)
    error_text = "Error generating career path: #{error.message}"
    error_text += "\n\n" + error.backtrace.join("\n")
    
    Rails.logger.error error_text
    career_path.update(status: "error")
    
    ErrorLog.create!(error: { errorData: error, backtrace: error.backtrace }.to_json)
  end

  def generate_unique_key
    (0...8).map { (65 + rand(26)).chr }.join + Time.current.to_i.to_s
  end

  # Prompt building methods
  def build_sanitisation_prompt
    <<~PROMPT
      The user is prompted to enter the name of a career they are interested in.

      You must sanitise the provided phrase. This includes standardising the text, removing any special characters, and ensuring that the text is appropriate.
      Convert the input to the most common form of the career name. For example, "Shoe repairer" should be converted to "Cobbler".
      
      The user may already input a standard and appropriate input like "software developer", in this case you just need to fix the casing: "Software Developer".

      If the provided phrase is inappropriate, return `{ "flagged": true, "explanation": "reason" }`. Things that are inappropriate would be things that aren't careers, 
      or are offensive, or are not appropriate for a school pupil. If the input is inappropriate, you must return a reason why it is inappropriate in the explanation field.

      Output format is JSON: `{ "flagged": false, "output": "standardised career name" }`.
    PROMPT
  end

  def build_career_path_prompt(age)
    <<~PROMPT
      You are a UK career advisor.

      You will be provided with a career name and your goal is to provide a multi stage plan to achieve that career.
      Each stage should be clearly labelled and have individual steps that are clear and easy to follow.

      Your response should be an array of JSON objects: 
      each stage should have a title and steps, and each step should have a title and description.
      For each career path, include at least 3 steps and ensure that each step is relevant to the career path.

      Ensure the text is appropriate for a student of age #{age}.

      Please ensure that your response has no special characters that may cause parsing errors.

      The format should be JSON: 
      {
        "career": "string",
        "description": "string",
        "jobFamily": "string", // one of: 'Education', 'Healthcare', 'Law', 'Retail', 'Engineering', 'Construction', 'Finance', 'Technology', 'Hospitality', 'Arts & Media', 'Manufacturing', 'Transportation', 'Sales & Marketing', 'Public Safety', 'Administration', 'Human Resources', 'Logistics', 'Social Services'
        "videoMediaQueries": ["string"], // a list of queries to find videos related to the career, they should all be one word and relevant to the career, no generic terms
        "careerPath": [
          {
            "title": "string",
            "keyword": "string",
            "emoji": "string",
            "steps": [
              {
                "title": "string",
                "description": "string",
                "furtherInfo": "string",
                "keyword": "string"
              }
            ]
          }
        ]
      }
      
      The keywords should be a word or short phrase that relates to the current step/stage.
      Each stage should have an emoji that relates to the title of the current stage.
      Ensure that the emoji is in UTF-8 format. The career description should be a short paragraph outlining what the career is about, while the step description should relate to the current step. The furtherInfo key within each step needs to be one or two paragraphs containing further and more specific details about the current step. Ensure that the title and emoji are separate keys. Ensure that your response is in the correct format shown above.
    PROMPT
  end

  def build_career_info_prompt(age)
    <<~PROMPT
      You are a UK career advisor. You will be provided with a career name and your goal is to provide some info about that career. Your information should be pertaining to the career in the United Kingdom. Your response should include statistics - such as salary range, number of positions, etc, about - a couple paragraphs explaining the career and what it entails, and related careers. 

      Ensure the text is appropriate for a student of age #{age}. Please ensure that your response has no special characters that may cause parsing errors. 

      You should aim to include at least 3 statistics and 3 related careers. The statistics should be relevant to the career in the UK, and the related careers should be similar or connected to the main career.

      The format must be JSON: 
      { 
        "career": "string", 
        "info": { 
          "tags": ["string"], 
          "stats": [
            { 
              "title": "string", 
              "text": "string" 
            }
          ], 
          "about": "string", 
          "relatedCareers": [
            { 
              "name": "string", 
              "description": "string" 
            }
          ] 
        } 
      }
      
      You must include tags: a list of unique words (or extremely short phrases if relevant) that describe the career. Usually less than 5. These are used to help students find lessons related to the career they are looking for. Ensure that your response is in the correct format shown above.
    PROMPT
  end

  def build_further_careers_prompt(age)
    <<~PROMPT
      You are a UK career advisor. You will be provided with a career name. Your goal is to provide some career suggestions that are related to the given career, but more specific. You should provide up to 3 suggestions. 
      
      Please format your response as JSON: 
      { 
        "careers": [
          { 
            "name": "string", 
            "description": "string" 
          }
        ] 
      }
      
      Ensure the suggestions do not include any special characters which may cause parsing errors. Please tailor the suggestions to be appropriate for a student of #{age} years of age.
    PROMPT
  end

  def build_job_family_prompt(families)
    <<~PROMPT
      Classify the given career into one of the following job families: #{families.join(', ')}.

      If the career does not fit any of these, create and return a new job family name.

      Respond **only** with JSON in this exact format:

      {
        "family": "string"  // one of the provided families, or a new name you generate
      }
    PROMPT
  end

  # Additional prompt methods for controller endpoints
  def build_facts_prompt(age)
    <<~PROMPT
      You are a UK career advisor. You will be provided with a career name and your goal is to provide some fun or interesting facts about that career for a school pupil. You must generate 5 facts. Ensure that the facts are tailored for a student #{age} years of age. Please ensure that your response has no special characters that may cause parsing errors.
      
      The format should be JSON: { "facts": ["fact1", "fact2", "fact3", "fact4", "fact5"] }
      
      Ensure that your response is in the correct format shown above.
    PROMPT
  end

  def generate_career_facts(career_name, age)
    # First sanitise the career name
    sanitised_career = sanitise_career_name_simple(career_name)
    return { flagged: true } if sanitised_career.nil?

    # Generate facts
    facts_prompt = build_facts_prompt(age)
    messages = @ai_client.build_messages(facts_prompt, sanitised_career)
    @ai_client.generate_response(
      messages: messages,
      response_type: :json,
      temperature: 0.7
    )
  end

  def generate_image_search_query(career_name, job_family = nil)
    system_prompt = build_image_search_prompt
    user_prompt = career_name
    user_prompt += " | #{job_family}" if job_family

    messages = @ai_client.build_messages(system_prompt, user_prompt)
    response = @ai_client.generate_response(
      messages: messages,
      response_type: :json,
      temperature: 0.5
    )
    
    response["query"]
  end

  def build_image_search_prompt
    <<~PROMPT
      You are an expert at generating search queries for stock photo websites like Pexels to find high-quality, relevant images for different careers.

      Your goal is to create a search query that will return professional, visually appealing images that clearly represent the given career. 

      Guidelines:
      - Avoid generic terms like "engineer", "manager", "specialist" that appear in many job titles
      - Focus on the specific tools, environment, or activities unique to that career
      - Use concrete, visual terms that photographers would use to tag their photos
      - Prefer action words or specific workplace settings over job titles
      - Consider what someone in this career would actually be photographed doing
      - Keep queries 1-3 words for best results
      - Avoid overly technical jargon that might not match photo tags

      Examples:
      - "Software Engineer" → "coding laptop"
      - "Civil Engineer" → "construction blueprints" 
      - "Marketing Manager" → "team presentation"
      - "Nurse" → "hospital care"
      - "Chef" → "cooking kitchen"
      - "Accountant" → "calculator spreadsheet"
      - "Graphic Designer" → "design tablet"
      - "Teacher" → "classroom teaching"
      - "Dentist" → "dental examination"
      - "Electrician" → "electrical wiring"
      - "Pilot" → "cockpit flying"
      - "Photographer" → "camera shooting"

      Respond with JSON in this format:
      {
        "query": "specific visual search terms"
      }
    PROMPT
  end

  private

  def sanitise_career_name_simple(career_name)
    system_prompt = build_sanitisation_prompt
    messages = @ai_client.build_messages(system_prompt, career_name)
    response = @ai_client.generate_response(
      messages: messages,
      response_type: :json,
      temperature: 0.3
    )

    return nil if response["flagged"]
    response["output"]
  end
end
