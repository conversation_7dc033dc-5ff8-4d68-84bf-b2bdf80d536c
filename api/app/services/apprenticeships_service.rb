require 'uri'
require 'httparty'

class ApprenticeshipsService
  include HTTParty
  base_uri 'https://api.apprenticeships.education.gov.uk/vacancies'

  def initialize
    @headers = {
      'Ocp-Apim-Subscription-Key' => "#{ENV["APPRENTICESHIPS_API_KEY"]}",
      'X-Version' => "1"
    }
  end

  def get_vacancies_by_routes routes
    return "" unless routes.present?

    route_array = []
    routes.each do |route|
      encoded_route = URI.encode_www_form_component(route)
      route_array.push("Routes=#{encoded_route}")
    end

    response = self.class.get("/vacancy?#{route_array.join('&')}", headers: @headers)
    body = JSON.parse(response.body)

    return "" if body["totalFiltered"] == 0

    return body["vacancies"]
  end

  def filter_vacancies_by_career career, vacancies, text_service
    return "" unless vacancies.present?

    mapped_vacancies = vacancies.map do |v|
      {
        ref: v["vacancyReference"],
        name: v["title"]
      }
    end

    related_vacancy_refs = text_service.find_related_vacancies(career, mapped_vacancies)["refs"]

    filtered_vacancies = vacancies.select { |v| related_vacancy_refs.include?(v["vacancyReference"]) }
  end

  def get_all_routes
    response = self.class.get("/referencedata/courses/routes", headers: @headers)
    body = JSON.parse(response.body)

    return body["routes"]
  end

  def get_matching_routes career, all_routes, text_service
    return '' unless career.present? && all_routes.present?
    routes = all_routes.join(' / ')
    closest_match = text_service.find_related_routes(career, routes)["routes"]
  end

  def get_vacancies career
    openai_text_service = OpenaiTextService.new

    all_routes = get_all_routes
    routes = get_matching_routes(career, all_routes, openai_text_service)
    vacancies = get_vacancies_by_routes(routes)
    related_vacancies = filter_vacancies_by_career(career, vacancies, openai_text_service)

    related_vacancies
  end

end
