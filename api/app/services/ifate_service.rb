require 'uri'
require 'httparty'

class IfateService
  include HTTParty
  base_uri 'https://occupational-maps-api.skillsengland.education.gov.uk/api/v1'
  

  def initialize
    @headers = {
      'X-API-KEY' => "#{ENV["IFATE_API_KEY"]}"
    }
  end

  def get_occupation_code(career)
    return '' unless career.present?
    return '' unless @headers['X-API-KEY'].present?

    begin
      encoded_career = URI.encode_www_form_component(career)

      response = self.class.get("/SearchOccupations?searchTerm=#{encoded_career}&expand=occupation.links", headers: @headers)
      body = JSON.parse(response.body)

      return "" if body.nil? || body["pagination"].nil? || body["pagination"]["total_returned"] == 0

      results = body["results"]
      filtered_results = results.select do |occ|
        occ["links"].any? { |link| link["rel"] == "occupationalProgressionURL" }
      end

      return "" if filtered_results.empty?
      return filtered_results[0]["stdCode"] if filtered_results[0]["name"].downcase.eql? career.downcase

      occupations = filtered_results.map do |occ|
        { stdCode: occ["stdCode"], name: occ["name"] }
      end

      career_names = occupations.map { |c| c[:name] }.join(' / ')

      openai_text_service = OpenaiTextService.new
      closest_match = openai_text_service.find_similar_career(career, career_names)

      return "" if closest_match.nil?
      occupation = occupations.find { |occ| occ[:name] == closest_match["name"] }
      return "" if occupation.nil?
      return occupation[:stdCode]
    rescue HTTParty::Error, JSON::ParserError => e
      # Log the error and return an empty string
      Rails.logger.error("Error fetching occupation code: #{e.message}")
      return ""
    end
  end

  def get_occupational_progression(occCode)
    return nil unless occCode.present?

    stdCode = URI.encode_www_form_component(occCode)

    response = self.class.get("/OccupationalProgression/#{stdCode}?expand=occupation.overview", headers: @headers)
    body = JSON.parse(response.body)

    return nil if body["status"] == 404

    map_occupational_progression(body)
  end

  def map_occupational_progression(input)
    occupation_progression = input
    occupations = occupation_progression["occupations"]
    progressions = occupation_progression["progressions"]

    current_code = occupation_progression["keyStdCode"]

    before = []
    after = []
    selected = occupations.find { |occ| occ["stdCode"] == current_code }

    progressions.each do |p|
      if p["stdCodeTo"] == current_code
        before << occupations.find { |occ| occ["stdCode"] == p["stdCodeFrom"] }
      elsif p["stdCodeFrom"] == current_code
        after << occupations.find { |occ| occ["stdCode"] == p["stdCodeTo"] }
      end
    end

    { before: before, after: after, selected: selected }
  end

end
