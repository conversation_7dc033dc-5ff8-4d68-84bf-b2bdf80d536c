# frozen_string_literal: true

# == Schema Information
#
# Table name: users
#
#  id                                    :bigint           not null, primary key
#  admin_permissions                     :jsonb
#  alias                                 :string
#  avatar_configuration                  :jsonb
#  beta_features                         :jsonb
#  blocked_at                            :datetime
#  can_access_career_builder             :boolean
#  can_access_lesson_ai                  :boolean
#  can_create_lessons                    :boolean          default(FALSE)
#  can_view_school_data                  :boolean          default(FALSE)
#  communication_preferences             :jsonb
#  deleted                               :boolean          default(FALSE), not null
#  dob                                   :datetime
#  education                             :string           default("")
#  email                                 :string           not null
#  ethnicity                             :integer
#  gender                                :string
#  geography_lead                        :boolean          default(FALSE)
#  glossary_list                         :string           default([]), is an Array
#  has_career_profile                    :boolean          default(FALSE), not null
#  hs_visitor_cache                      :jsonb
#  hubspot_marketing_email_name_received :string
#  identifier                            :string
#  import_data                           :jsonb
#  is_blocked                            :boolean          default(FALSE)
#  is_school_admin                       :boolean
#  job_hunter_interests                  :string
#  job_title                             :string
#  last_activity_at                      :datetime
#  last_sign_in_at                       :datetime
#  last_sign_out_at                      :datetime
#  last_sync_hubspot_activity_at         :datetime
#  lead_source                           :integer
#  linked_in_url                         :string           default("")
#  location                              :string
#  login_count                           :integer          default(0)
#  mailchimp_last_error                  :text
#  mailchimp_last_sync                   :datetime
#  mailchimp_last_sync_attempt           :datetime
#  mailchimp_list                        :string
#  mailchimp_sync_status                 :string           default("pending")
#  name                                  :string
#  onboarded                             :boolean
#  password_digest                       :string           not null
#  phone                                 :string
#  points                                :integer          default(0), not null
#  preferred_location                    :string
#  presentation_settings                 :jsonb
#  profile_color                         :jsonb
#  provider                              :string           not null
#  questionnaire_taken                   :boolean          default(FALSE)
#  recovery_requested_at                 :datetime
#  recovery_token                        :string
#  referral_actioned                     :boolean          default(FALSE)
#  referral_code                         :string
#  referral_share_name                   :boolean          default(TRUE), not null
#  require_password_reset                :boolean
#  science_lead                          :boolean          default(FALSE)
#  session_token                         :string
#  sign_in_token                         :string
#  specified_class                       :string
#  specified_year                        :string
#  tasks_completed_cache                 :jsonb
#  tokens                                :json
#  type                                  :string
#  uid                                   :string           default(""), not null
#  unique_wonde_identifier               :string
#  use_new_presentation_player           :boolean
#  work_experience                       :jsonb            is an Array
#  working_days                          :string
#  years_of_experience                   :string           default("")
#  created_at                            :datetime         not null
#  updated_at                            :datetime         not null
#  cv_fileboy_id                         :string
#  fileboy_image_id                      :string
#  hubspot_id                            :string
#  mailchimp_id                          :string
#  my_login_id                           :string
#  organisation_id                       :bigint
#  referred_from_user_id                 :integer
#  school_id                             :bigint
#  stripe_customer_id                    :string
#  white_label_organisation_id           :bigint
#  wonde_id                              :string
#
# Indexes
#
#  index_users_on_email                           (email) UNIQUE WHERE (NOT deleted)
#  index_users_on_identifier_and_school_id        (identifier,school_id) UNIQUE WHERE (NOT deleted)
#  index_users_on_organisation_id                 (organisation_id)
#  index_users_on_school_id                       (school_id)
#  index_users_on_sign_in_token                   (sign_in_token) UNIQUE
#  index_users_on_uid_and_provider                (uid,provider) UNIQUE WHERE (NOT deleted)
#  index_users_on_white_label_organisation_id     (white_label_organisation_id)
#  index_users_unique_wonde_id_where_not_deleted  (wonde_id) UNIQUE WHERE ((deleted = false) AND (wonde_id IS NOT NULL) AND ((wonde_id)::text <> ''::text))
#
# Foreign Keys
#
#  fk_rails_...  (referred_from_user_id => users.id)
#  fk_rails_...  (white_label_organisation_id => organisations.id)
#
require "keyword_extractor"

class User < ApplicationRecord
  audited
  include WardenUser
  include Subscribable
  include SubscriptionAccess

  JOB_TITLES = [
    # ['label', 'value'],
    ['Head', 'Head'],
    ['SLT/Governor', 'SLT/Governor'],
    ['Business/Finance', 'Business/Finance'],
    ['Science Lead', 'Subject Lead Science'],
    ['Teacher', 'Teacher'],
    ['Teaching Assistant', 'Teaching Assistant'],
  ].freeze

  enum lead_source: %i[website exhibition referral]
  enum ethnicity: %i[white mixed_or_multiple_ethnic_groups asian_or_asian_british black_african_caribbean_or_black_british other]

  scope :not_pupils, -> { where.not(type: 'Pupil') }

  belongs_to :school, optional: true
  belongs_to :organisation, optional: true
  belongs_to :white_label_organisation, class_name: "Organisation", foreign_key: :white_label_organisation_id, optional: true

  has_many :created_schools, class_name: 'School', foreign_key: :created_by_id

  has_one :author

  has_one :ai_subscription, -> { where(subscription_type: :ai) }, class_name: 'StripeSubscription'
  has_one :curriculum_subscription, -> { where(subscription_type: :curriculum) }, class_name: 'StripeSubscription'
  has_many :stripe_subscriptions, class_name: 'StripeSubscription'

  has_many :quiz_attempts, -> { current_school_year }, class_name: 'QuizOld::Attempt'
  has_many :all_quiz_attempts, dependent: :destroy, class_name: 'QuizOld::Attempt'
  has_many :xls_imports

  has_many :user_lesson_templates, class_name: "Lesson::Template", foreign_key: :user_id

  has_many :quip_quizzes, foreign_key: :user_id

  has_many :enrollments, -> { where(deleted: false) }, dependent: :destroy, inverse_of: :user
  has_many :forms, -> { current_school_year }, through: :enrollments
  has_many :all_forms, through: :enrollments, inverse_of: :users, source: :form
  has_many :lessons, through: :forms
  has_many :all_lessons, through: :all_forms, source: :lessons
  has_many :lesson_templates, through: :lessons, source: :template, class_name: 'Lesson::Template'
  has_many :lesson_templates, through: :all_lessons, source: :template, class_name: 'Lesson::Template'

  has_many :new_library_units, through: :lesson_templates
  has_many :years, through: :new_library_units

  has_many :pupils, through: :forms
  has_many :all_pupils, through: :all_forms, source: :pupils
  has_many :word_search_results, class_name: 'Lesson::WordSearchResult'
  has_many :presentation_progresses, dependent: :destroy
  has_many :pupil_quip_attempts, foreign_key: :pupil_id
  has_many :active_days, foreign_key: :user_id, dependent: :destroy
  has_many :user_achievements, dependent: :destroy
  has_many :tracking_presentation_views, dependent: :destroy, foreign_key: :user_id, class_name: 'TrackingPresentationView'
  has_many :user_tracking_lesson_template_vieweds, source: :tracking_lesson_template_vieweds, class_name: "TrackingLessonTemplateViewed"
  has_many :tracking_logins, dependent: :destroy, foreign_key: :user_id, class_name: 'TrackingLogin'
  has_many :questionnaire_answers, dependent: :destroy
  has_many :questionnaire_career_tags, through: :questionnaire_answers, source: :career_tags
  has_many :live_stream_messages
  has_many :questionnaire_users, dependent: :destroy
  has_many :device_logins, dependent: :destroy

  has_many :subscription_creators
  has_many :created_subscriptions, through: :subscription_creators, source: :subscriber

  has_one :custom_sign_up_url_use

  has_many :lesson_plan_views

  has_many :pupil_homeworks
  has_many :homework, through: :pupil_homeworks
  has_many :created_homeworks, class_name: "Homework", foreign_key: :created_by_id

  has_many :user_preferred_careers
  has_many :preferred_careers, through: :user_preferred_careers, source: :career

  has_many :referred_users, foreign_key: :referred_from_user_id, class_name: 'User'
  has_one :referred_from_user, foreign_key: :id, primary_key: :referred_from_user_id, class_name: 'User'

  validates :password, length: { minimum: 8 }, if: -> { password.present? }
  validates :name, presence: true
  validates :school, presence: true, unless: -> { admin? || individual_user? || user? || employer? }
  validates :email, uniqueness: { unless: :deleted?, conditions: -> { where(deleted: false) } }
  validate :reasonable_date_of_birth

  has_many :user_interest_tags, dependent: :destroy
  has_many :related_words, through: :user_interest_tags
  has_many :form_units, through: :forms

  has_many :campaign_lesson_views

  has_many :curriculum_document_groups
  has_many :curriculum_document_groupings

  has_one :subscriber, as: :subscriber, dependent: :destroy

  has_many :user_rank_events, dependent: :destroy

  has_many :career_suggestions, dependent: :destroy
  has_many :mailchimp_sync_logs, dependent: :destroy

  has_many :user_referrals, foreign_key: :referrer_id, dependent: :destroy
  has_many :received_referrals, class_name: 'UserReferral', foreign_key: :email, primary_key: :email

  has_many :user_career_paths, dependent: :destroy

  has_many :flows, dependent: :destroy
  has_many :flow_enrollments, dependent: :destroy
  has_many :enrolled_flows, through: :flow_enrollments, source: :flow
  has_many :flow_progresses, dependent: :destroy
 

  validate do
    if password.present?
      errors.add(:password, 'must be at least 8 characters.') unless (password.length > 7)
      errors.add(:password, 'must contain a number.') unless (password.match?(/[0-9]/))
      errors.add(:password, 'must contain a letter.') unless (password.match?(/[a-z]/i))
      errors.add(:password, 'must contain a special character.') unless (password.match?(/[^a-zA-Z0-9 ]/i))
    end
  end

  validate { errors.add(:email, 'must be lowercase') unless email == email.to_s.downcase }

  before_validation {
    self.name = name&.strip
    self.email = email.to_s.downcase
    self.type ||= 'IndividualUser'
  }

  # List of available beta features and their descriptions
  BETA_FEATURES = {
    "teacher_dashboard_tour" => {
      name: "Dashboard Tour",
      description: "Get a guided tour of the teacher dashboard, through a new 'Start Tour' button."
    },
    "teacher_page_tours" => {
      name: "School Page Tours",
      description: "Get a guided tour of the other teacher pages, through a new 'Start Tour' button."
    },
    "ai_lesson_editing_smart_combine" => {
      name: "AI Lesson Editing Smart Combine",
      description: "Use AI to merge lessons in the lesson editor when creating a new lesson.",
      secret: true
    },
    "ai_lesson_editing_assistant" => {
      name: "AI Lesson Editing Assistant",
      description: "Use AI to assist with lesson editing tasks.",
      secret: true
    },
    "ai_lesson_builder" => {
      name: "AI Lesson Builder",
      description: "Use AI to build a new lesson from scratch.",
      secret: true
    },
    "new_lesson_editor" => {
      name: "New Lesson Editor",
      description: "Use the new lesson editor to create and edit lessons."
    },
    "invites" => {
      name: "Invites",
      description: "Invite teachers to your school and manage invites.",
      secret: true
    },
    "referrals" => {
      name: "Referrals",
      description: "Refer friends to Developing Experts and earn rewards.",
      secret: true
    },
    "aug_4" => {
      name: "Aug 4th Features",
      description: "Explore the new features released on August 4th.",
      secret: true
    },
    "aug_18" => {
      name: "Aug 18th Features",
      description: "Explore the new features released on August 18th.",
      secret: true
    },
    "september_1" => {
      name: "September 1st Features",
      description: "Explore the new features released on September 1st.",
      secret: true
    },
    "september_15" => {
      name: "September 15th Features",
      description: "Explore the new features released on September 15th.",
      secret: true
    },
    "flows" => {
      name: "Flows access",
      description: "Get access to the new Flows/Courses feature.",
      secret: true
    },
  }.freeze

  # List of available communication preferences and their descriptions
  COMMUNICATION_PREFERENCES = {
    "exemplar_work" => {
      name: "Exemplar Work Notifications",
      description: "Get notified when you submit a new exemplar work or when one of your submissions is reviewed and approved."
    },
    "lesson_feedback" => {
      name: "Lesson Feedback Notifications",
      description: "Receive updates when you submit lesson feedback or when someone responds to your feedback with a comment."
    },
    "independent_learning" => {
      name: "Homework Submission Notifications",
      description: "Be notified when the first and last pupils submit homework assignments you have set for your class."
    },
    "activity" => {
      name: "Activity Notifications",
      description: "Receive helpful notifications when you use new features for the first time, including tips and support information."
    },
    "invite_accepted" => {
      name: "Invite Accepted Notifications",
      description: "Get notified when a teacher you invited accepts their invite to your school."
    },
    "referral_invite_accepted" => {
      name: "Referral Invite Accepted Notifications",
      description: "Receive notifications when someone accepts your referral invite to join Developing Experts."
    },
    "referral_code_used" => {
      name: "Referral Code Notifications",
      description: "Get notified when someone uses your referral code to sign up for Developing Experts."
    },
  }.freeze

  # List of available admin permissions and their descriptions
  ADMIN_PERMISSIONS = {
    "curriculum" => {
      name: "Curriculum",
      description: ""
    },
    "content" => {
      name: "Content",
      description: ""
    },
    "website" => {
      name: "Website",
      description: ""
    },
    "schools" => {
      name: "Schools",
      description: ""
    },
    "marketing" => {
      name: "Marketing",
      description: ""
    },
    "misc" => {
      name: "Misc",
      description: ""
    },
  }.freeze

  # Scope to find all users subscribed to a specific service
  # service can be :ai, :science, or :geography
  scope :subscribed_to_service, ->(service) {
    service = service.to_sym
    
    # Start with basic admin users who always have access
    admin_users = where(type: 'Admin')
    
    # Users whose schools are in trial
    trial_school_users = joins(:school).where(schools: { trial_end_date: Date.current.. })
    
    # Modern subscriber model users - both direct and through school
    # User subscribers
    user_subscribers = joins(:subscriber)
      .where("subscribers.active_products->>'products' LIKE ?", "%#{service}%")
      .where("subscribers.subscription_status IN (?)", ['active', 'active_until_period_end'])
    
    # School subscribers
    school_subscribers = joins(:school => :subscriber)
      .where("subscribers.active_products->>'products' LIKE ?", "%#{service}%")
      .where("subscribers.subscription_status IN (?)", ['active', 'active_until_period_end'])
    
    # Legacy subscription logic - Only consider these if no subscriber record exists
    case service
    when :ai
      # Users with schools that have Hubspot AI subscription but no subscriber record
      hubspot_users = joins(:school)
        .where(schools: { hubspot_ai_subscription_status: true })
        .where.not(id: user_subscribers.select(:id))
        .where.not(id: school_subscribers.select(:id))
      
      # Users with direct AI subscription but no subscriber record
      legacy_direct_subs = joins(:ai_subscription)
        .where("stripe_subscriptions.free_subscription = ? OR stripe_subscriptions.stripe_cache->>'status' = ?", true, 'active')
        .where.not(id: user_subscribers.select(:id))
        .where.not(id: school_subscribers.select(:id))
      
      union_all(admin_users, trial_school_users, user_subscribers, school_subscribers, hubspot_users, legacy_direct_subs)
      
    when :science
      # Users with schools that have Hubspot science subscription but no subscriber record
      hubspot_users = joins(:school)
        .where(schools: { hubspot_subscription_status: 'Subscribed' })
        .where.not(id: user_subscribers.select(:id))
        .where.not(id: school_subscribers.select(:id))
      
      # Users with direct curriculum subscription but no subscriber record
      legacy_direct_subs = joins(:curriculum_subscription)
        .where("stripe_subscriptions.free_subscription = ? OR stripe_subscriptions.stripe_cache->>'status' = ?", true, 'active')
        .where.not(id: user_subscribers.select(:id))
        .where.not(id: school_subscribers.select(:id))
      
      union_all(admin_users, trial_school_users, user_subscribers, school_subscribers, hubspot_users, legacy_direct_subs)
      
    when :geography
      # Users with schools that have Hubspot geography subscription but no subscriber record
      hubspot_users = joins(:school)
        .where(schools: { hubspot_geography_subscription: true })
        .where.not(id: user_subscribers.select(:id))
        .where.not(id: school_subscribers.select(:id))
      
      # Users with direct curriculum subscription but no subscriber record
      legacy_direct_subs = joins(:curriculum_subscription)
        .where("stripe_subscriptions.free_subscription = ? OR stripe_subscriptions.stripe_cache->>'status' = ?", true, 'active')
        .where.not(id: user_subscribers.select(:id))
        .where.not(id: school_subscribers.select(:id))
      
      union_all(admin_users, trial_school_users, user_subscribers, school_subscribers, hubspot_users, legacy_direct_subs)
      
    else
      raise ArgumentError, "Unknown service: #{service}"
    end
  }
  
  # Helper method to combine multiple ActiveRecord::Relation objects with UNION ALL
  # This ensures all queries select only the ID column to avoid column count mismatches
  def self.union_all(*relations)
    id_column = "#{table_name}.id"
    
    # Transform each relation to select only the ID column
    sql_queries = relations.map do |relation|
      # Make sure relation is not empty to avoid SQL errors
      next nil if relation.empty?
      
      # Force each relation to select only the user ID column
      relation.select(id_column).to_sql
    end.compact
    
    # If no valid relations were provided, return none
    return none if sql_queries.empty?
    
    # Combine all queries with UNION ALL
    union_sql = sql_queries.join(' UNION ALL ')
    
    # Return users whose IDs are in the combined result
    where("#{id_column} IN (SELECT id FROM (#{union_sql}) AS combined_ids)")
  end

  def account_restricted?
    is_blocked || (!subscribed_to_any_service? && (school&.restricted? || school&.trial_ended? || school&.trial_pending?))
  end

  # Multi service subscription checks
  # Possible subscription types: science, geography, ai
  # Check if Hubspot or Stripe managed subscriber:
    # - If Stripe then check on subscriber
    # - If Hubspot then
      # - Check on school
      # - Check on old style stripe
  # If in trial, then always true
  def subscribed_to_service?(service)
    return true if school&.in_trial?
    return true if admin?

    # Get the school and user Stripe subscriber records

    if subscription.nil?
      # Managed by Hubspot or old style Stripe
      case service
      when :ai
        return ai_subscription&.free_subscription || ai_subscription&.active? || school&.hubspot_ai_subscription_status
      when :science
        return curriculum_subscription&.free_subscription || curriculum_subscription&.active? || school&.subscribed?
      when :geography
        return curriculum_subscription&.free_subscription || curriculum_subscription&.active? || school&.hubspot_geography_subscription?
      else
        raise ArgumentError, "Unknown service: #{service}"
      end
    else
      subscription.has_access_to?(service)
    end
  end

  def curriculum_subscription?
    subscribed_to_service?(:science) || subscribed_to_service?(:geography)
  end

  def subscribed_to_any_service?
    # Check trial status once (short-circuit)
    return true if school&.in_trial?
    return true if admin?

    if subscription.nil?
      # Handle old-style subscriptions with single check for each condition
      return ai_subscription&.free_subscription ||
             ai_subscription&.active? ||
             school&.hubspot_ai_subscription_status ||
             curriculum_subscription&.free_subscription ||
             curriculum_subscription&.active? ||
             school&.subscribed? ||
             school&.hubspot_geography_subscription?
    else
      # the active_subscription only return a subscriber with products, so it's presence is enough
      # to know if the user has a subscription to any of the services
      return subscription.has_access_to?(:science) ||
              subscription.has_access_to?(:geography) ||
              subscription.has_access_to?(:ai)
    end
  end

  # Beta Features
  def beta_feature_enabled?(feature_name)
    beta_features&.include?(feature_name.to_s)
  end

  def enable_beta_feature!(feature_name)
    self.beta_features ||= []
    self.beta_features << feature_name.to_s unless beta_features.include?(feature_name.to_s)
    save!
  end

  def disable_beta_feature!(feature_name)
    self.beta_features&.delete(feature_name.to_s)
    save!
  end

  # Communication Preferences
  # Store as array of hashes: [{key: bool}, ...]
  def allows_notification?(type)
    prefs = communication_preferences || []
    # If no entry for key, default to true (enabled)
    entry = prefs.find { |h| h.is_a?(Hash) && h.key?(type.to_s) }
    entry.nil? ? true : entry[type.to_s]
  end

  def enable_communication_preference!(type)
    self.communication_preferences ||= []
    type_str = type.to_s
    prefs = self.communication_preferences.reject { |h| h.is_a?(Hash) && h.key?(type_str) }
    prefs << { type_str => true }
    self.communication_preferences = prefs
    save!
  end

  def disable_communication_preference!(type)
    self.communication_preferences ||= []
    type_str = type.to_s
    prefs = self.communication_preferences.reject { |h| h.is_a?(Hash) && h.key?(type_str) }
    prefs << { type_str => false }
    self.communication_preferences = prefs
    save!
  end

  # Admin Permissions
  # Store as array of hashes: [{key: bool}, ...]
  def allows_admin_section?(section)
    return false unless admin?
    permitted = admin_permissions || []
    # If no entry for key, default to true (enabled)
    entry = permitted.find { |h| h.is_a?(Hash) && h.key?(section.to_s) }
    entry.nil? ? true : entry[section.to_s]
  end

  def update_admin_permissions(enabled_sections)
    enabled_sections ||= []

    self.admin_permissions = ADMIN_PERMISSIONS.keys.map do |section|
      { section.to_s => enabled_sections.include?(section.to_s) }
    end
    save!
  end

  def has_school_feature_access?
    return true if admin?
    return true if school&.in_trial?

    return false if school.nil?
    return false if school.category == :home_school_category

    if school.subscriber.nil?
      return school&.subscribed? || school&.hubspot_geography_subscription?
    else
      return school.subscriber.has_access_to?(:science) || school.subscriber.has_access_to?(:geography)
    end
  end

  def stripe_subscriber?
    return true if subscription.present?
  end

  def has_full_access_to_template?(template)
    return true if template.anonymous || template.demo

    subject = template.subject

    if subject.blank?
      # For now, default to the first science subject
      subject = NewLibrary::Subject.where(name: 'Science').first
    end

    subject_name = subject.name.downcase.to_sym
    subscribed_to_service?(subject_name)
  end

  def questionnaire_taken?
    if !Questionnaire.find_by(is_onboarding_questionnaire: true, published: true).present?
      return true
    end
    questionnaire_users.left_joins(:questionnaire).where(questionnaires: { is_onboarding_questionnaire: true, published: true }).any?
  end

  def form_unit_questionnaires_to_take
    active_units = form_units.active.joins(new_library_unit: :questionnaire)
    active_units = active_units.where.not(questionnaires: { id: questionnaire_users.select(:questionnaire_id) })
  end

  def can_create_lessons?
    !!curriculum_subscription? && type != 'IndividualUser'
  end

  def can_view_curriculum_documents?
    return true if school&.subscribed? || school&.free_subscription
    if school&.in_trial? && CurriculumDocument.where(available_to_trial: true).any?
      return true
    end
    return false
  end

  def user_achievement_points
    Achievement.where(id: user_achievements.select(:achievement_id)).sum(:points)
  end

  def get_or_build_referral_code
    if self.referral_code.present?
      self.referral_code
    else
      get_new_referral_code
      self.referral_code
    end
  end

  def get_new_referral_code
    new_referral_code = generate_referral_code
    self.update!(referral_code: new_referral_code)
  end

  after_create :get_or_build_referral_code, if: -> { !referral_code.present? && !pupil? }

  # this returns generates a hexidecimal str of 8 length and checks if it collides with a user before recursively making one again
  # so i think thats 16^8 possible combinations so it's prolly safe
  def generate_referral_code(attempts = 0)
    # if attempts > something, do something?

    next_code = SecureRandom.hex(4).upcase

    if User.find_by_referral_code(next_code).present?
      return self.generate_referral_code(attempts + 1)
    else
      return next_code
    end
  end

  def build_referral_from_code code
    if code.blank?
      return
    end

    referred_from_user_id = User.find_by_referral_code(code)&.id

    if referred_from_user_id.present?
      self.update(referred_from_user_id: referred_from_user_id)
    end
  end

  def has_subscription?
    school&.free_subscription || school&.hubspot_subscription_status == "Subscribed" || school&.hubspot_subscription_status == "In Trial"
  end

  alias has_subscription has_subscription?

  def free_teacher?
    teacher? && !!school&.unregistered?
  end

  def after_sign_in
    # Update without callbacks
    update_columns(login_count: (login_count || 0) + 1)
    TrackingService.track_user_login(self)
  end

  def intercom_sync
    return
  end

  def get_stats
    {
      'Logins' => login_count,
      'User Type' => type,
      'Customer Type' => self.school&.category == :home_school_category ? "Customer" : "",
      'UTM Source' => lead_source,
      'Last Login Date' => last_sign_in_at,
    }
  end

  def best_word_search_result
    word_search_results.minimum(:time_taken)
  end

  %w[pupil teacher admin individual_user user employer].each do |user_type|
    define_method("#{user_type}?") { type == user_type.classify }
    define_singleton_method(user_type) { where(type: user_type.classify) }
  end

  def teacher_admin?
    teacher? && is_school_admin
  end

  def send_password_reset_email token:
    SchoolMailer.password_reset(self, token).deliver_now
  end

  def last_view resource
    View.where(user: self, resource: resource).ordered.limit(1).pluck(:created_at)[0]
  end

  def create_view resource
    View.create!(user: self, resource: resource)
  end

  def add_points amount
    update!(points: points + amount.to_i)
  end

  def history_templates
    Lesson::Template.where(id: presentation_progresses.select(:lesson_template_id))
  end

  def accessible_admins
    admin? ? Admin.all : Admin.none
  end

  def accessible_forms
    admin? ? Form.all : Form.none
  end

  def accessible_lesson_keywords
    admin? ? Lesson::Keyword.all : Lesson::Keyword.none
  end

  def accessible_lesson_lessons
    admin? ? Lesson::Lesson.all : Lesson::Lesson.none
  end

  def accessible_lesson_ratings
    admin? ? Lesson::Rating.all : Lesson::Rating.none
  end

  def accessible_lesson_results
    admin? ? Lesson::Result.all : Lesson::Result.none
  end

  def accessible_lesson_slides
    admin? ? Lesson::Slide.all : Lesson::Slide.none
  end

  def purchased_or_public_lesson_templates
    admin? ? Lesson::Document.all : Lesson::Document.none
  end

  def accessible_lesson_templates
    admin? ? Lesson::Template.all : Lesson::Template.none
  end

  def accessible_notifications
    notifications
  end

  def accessible_pupils
    admin? ? Pupil.all : Pupil.none
  end

  def accessible_quiz_attempts
    admin? ? QuizOld::Attempt.all : QuizOld::Attempt.none
  end

  def accessible_quiz_questions
    admin? ? QuizOld::Question.all : QuizOld::Question.none
  end

  def accessible_schools
    admin? ? School.all : School.none
  end

  def accessible_teachers
    admin? ? Teacher.all : Teacher.none
  end

  def accessible_lesson_documents
    Lesson::Document.all
  end

  def generate_sign_in_token
    update!(sign_in_token: SecureRandom.hex(12))
    sign_in_token
  end

  # Array<{
  #   id: number
  #   name: string
  #   year_id: number
  #   year: string
  #   unit_id: number
  #   unit: string
  #   quiz_avg: number | null
  #   lesson_avg: number | null
  # }>
  def self.report(type, school_id:, form_ids: nil, unit_ids: nil)
    pupils = type.to_s == "pupils"

    conditions = [
      true,
      form_ids && "forms.id IN (#{escape form_ids})",
      unit_ids && "units.id IN (#{escape unit_ids})",
    ]

    load_sql("user/report").compact.assign(
      school_id: school_id,
      select_sql: sql(pupils ? "users.id, users.name" : "forms.id, forms.name"),
      where_sql: sql(conditions.compact.join(" AND ")),
      group_sql: sql(pupils ? "users.id" : "forms.id"),
      order_sql: sql(pupils ? "users.name" : "forms.name"),
    )
  end

  def average_marks
    query = load_sql("form/average_marks").compact.assign(where_sql: sql("users.id = #{escape id}"))
    query.to_json[0]
  end

  def country
    (admin?) ? Country.find(1) : (school && Country.find(school.country_id))
  end

  def interest_tags
    user_interest_tags.pluck(:name)
  end

  # takes an array of strings and create careers tags for each, removing all existing tags
  def interest_tags=(tags)
    tags = tags.keep_if(&:present?).uniq

    transaction do
      user_interest_tags.destroy_all
      tags.each { |tag| user_interest_tags.new(name: tag) }
    end
  end

  def v1_as_json(options = nil)
    as_json(except: [
      :password_digest, :recovery_token, :recovery_requested_at, :provider, :tokens
    ])
      .merge({
               user_type: type,
               deleted: deleted,
             })
  end

  def v2_as_json(options = nil)
    as_json(except: [
      :password_digest, :recovery_token, :recovery_requested_at, :provider, :tokens
    ])
      .merge({
               user_type: type,
               deleted: deleted,
             })
  end

  def permanently_delete_user
    ActiveRecord::Base.transaction do
      PresentationProgress.unscoped.where(user: self).each { |e| e.delete }
      Enrollment.unscoped.where(user: self).each { |e| e.delete }
      View.unscoped.where(user: self).each { |e| e.delete }
      UserTemplate.unscoped.where(user: self).each { |e| e.delete }
      TrackingLogin.unscoped.where(user: self).each { |e| e.delete }
      TrackingPresentationView.unscoped.where(user: self).each { |e| e.delete }
      ActiveDay.unscoped.where(user: self).each { |e| e.delete }
      UserAchievement.unscoped.where(user: self).each { |e| e.delete }
      delete
    end
  end

  def career_feed
    CareerTagging
      .joins(career_tag: :related_words)
      .where(
        taggable_type: ["Career", "CareerCourse", "CareerVacancy", "Event"],
        related_words: { name: related_words.select(:name) },
      )
      .group(:taggable_id, :taggable_type)
      .order(Arel.sql("COUNT(related_words) DESC"))
      .select(
        :taggable_id,
        :taggable_type,
        "coalesce(JSON_AGG(DISTINCT related_words.name), '[]') AS common_words",
      )
  end

  def is_valid_email
    !!/#{DOMAIN_STR}/i.match(email)
  end

  def self.is_generic_email email
    domains = ["aol", "att", "comcast", "facebook", "gmail", "gmx", "googlemail", "google", "hotmail", "mac", "me", "mail", "msn", "live", "sbcglobal", "verizon", "yahoo", "email", "fastmail", "games", "hush", "hushmail", "icloud", "iname", "inbox", "lavabit", "love", "outlook", "pobox", "protonmail", "tutanota", "tutamail", "tuta", "keemail", "rocketmail", "safe-mail", "wow", "ygm", "ymail", "zoho", "yandex", "bellsouth", "charter", "cox", "earthlink", "juno", "btinternet", "virginmedia", "blueyonder", "freeserve", "ntlworld", "o2", "orange", "sky", "talktalk", "tiscali", "virgin", "wanadoo", "bt", "sina", "qq", "naver", "hanmail", "daum", "nate", "163", "yeah", "126", "21cn", "aliyun", "foxmail", "laposte", "sfr", "neuf", "free", "online", "t-online", "web", "libero", "virgilio", "alice", "tin", "poste", "teletu", "rambler", "ya", "list", "skynet", "voo", "tvcablenet", "telenet", "fibertel", "speedy", "arnet", "prodigy", "bell", "shaw", "sympatico", "rogers", "uol", "bol", "terra", "ig", "itelefonica", "r7", "zipmail", "globo", "globomail", "oi", "family"]
    domain_regex = /@(#{domains.join("|")})\./
    email.match(domain_regex)
  end

  def block!
    update! is_blocked: true, blocked_at: Time.now, type: 'IndividualUser', school_id: nil
  end

  def unblock!
    update! is_blocked: false, blocked_at: nil
  end

  def last_mailchimp_sync_log
    mailchimp_sync_logs.recent.first
  end

  def mailchimp_sync_successful?
    mailchimp_sync_status == 'success'
  end

  def mailchimp_sync_failed?
    mailchimp_sync_status == 'failed'
  end

  def total_referral_earnings
    user_referrals.completed.sum(:reward_amount)
  end

  def pending_referral_earnings
    user_referrals.where(paid_at: nil).sum(:reward_amount)
  end

  def paid_referral_earnings
    user_referrals.paid.sum(:reward_amount)
  end

  def total_successful_referrals
    user_referrals.accepted.count + user_referrals.completed.count
  end

  private

  def reasonable_date_of_birth
    return if dob.blank? # Skip if optional

    begin
      # Check if dob is a string and needs parsing
      date = dob.is_a?(String) ? Date.parse(dob) : dob

      if date > Date.today
        errors.add(:dob, "cannot be in the future")
      elsif date < Date.new(1900, 1, 1)
        errors.add(:dob, "is too far in the past")
      end
    rescue ArgumentError, TypeError
      errors.add(:dob, "is invalid")
    end
  end

end

DOMAINS = [
  "AAA",
  "AARP",
  "ABARTH",
  "ABB",
  "ABBOTT",
  "ABBVIE",
  "ABC",
  "ABLE",
  "ABOGADO",
  "ABUDHABI",
  "AC",
  "ACADEMY",
  "ACCENTURE",
  "ACCOUNTANT",
  "ACCOUNTANTS",
  "ACO",
  "ACTOR",
  "AD",
  "ADAC",
  "ADS",
  "ADULT",
  "AE",
  "AEG",
  "AERO",
  "AETNA",
  "AF",
  "AFAMILYCOMPANY",
  "AFL",
  "AFRICA",
  "AG",
  "AGAKHAN",
  "AGENCY",
  "AI",
  "AIG",
  "AIRBUS",
  "AIRFORCE",
  "AIRTEL",
  "AKDN",
  "AL",
  "ALFAROMEO",
  "ALIBABA",
  "ALIPAY",
  "ALLFINANZ",
  "ALLSTATE",
  "ALLY",
  "ALSACE",
  "ALSTOM",
  "AM",
  "AMAZON",
  "AMERICANEXPRESS",
  "AMERICANFAMILY",
  "AMEX",
  "AMFAM",
  "AMICA",
  "AMSTERDAM",
  "ANALYTICS",
  "ANDROID",
  "ANQUAN",
  "ANZ",
  "AO",
  "AOL",
  "APARTMENTS",
  "APP",
  "APPLE",
  "AQ",
  "AQUARELLE",
  "AR",
  "ARAB",
  "ARAMCO",
  "ARCHI",
  "ARMY",
  "ARPA",
  "ART",
  "ARTE",
  "AS",
  "ASDA",
  "ASIA",
  "ASSOCIATES",
  "AT",
  "ATHLETA",
  "ATTORNEY",
  "AU",
  "AUCTION",
  "AUDI",
  "AUDIBLE",
  "AUDIO",
  "AUSPOST",
  "AUTHOR",
  "AUTO",
  "AUTOS",
  "AVIANCA",
  "AW",
  "AWS",
  "AX",
  "AXA",
  "AZ",
  "AZURE",
  "BA",
  "BABY",
  "BAIDU",
  "BANAMEX",
  "BANANAREPUBLIC",
  "BAND",
  "BANK",
  "BAR",
  "BARCELONA",
  "BARCLAYCARD",
  "BARCLAYS",
  "BAREFOOT",
  "BARGAINS",
  "BASEBALL",
  "BASKETBALL",
  "BAUHAUS",
  "BAYERN",
  "BB",
  "BBC",
  "BBT",
  "BBVA",
  "BCG",
  "BCN",
  "BD",
  "BE",
  "BEATS",
  "BEAUTY",
  "BEER",
  "BENTLEY",
  "BERLIN",
  "BEST",
  "BESTBUY",
  "BET",
  "BF",
  "BG",
  "BH",
  "BHARTI",
  "BI",
  "BIBLE",
  "BID",
  "BIKE",
  "BING",
  "BINGO",
  "BIO",
  "BIZ",
  "BJ",
  "BLACK",
  "BLACKFRIDAY",
  "BLOCKBUSTER",
  "BLOG",
  "BLOOMBERG",
  "BLUE",
  "BM",
  "BMS",
  "BMW",
  "BN",
  "BNPPARIBAS",
  "BO",
  "BOATS",
  "BOEHRINGER",
  "BOFA",
  "BOM",
  "BOND",
  "BOO",
  "BOOK",
  "BOOKING",
  "BOSCH",
  "BOSTIK",
  "BOSTON",
  "BOT",
  "BOUTIQUE",
  "BOX",
  "BR",
  "BRADESCO",
  "BRIDGESTONE",
  "BROADWAY",
  "BROKER",
  "BROTHER",
  "BRUSSELS",
  "BS",
  "BT",
  "BUDAPEST",
  "BUGATTI",
  "BUILD",
  "BUILDERS",
  "BUSINESS",
  "BUY",
  "BUZZ",
  "BV",
  "BW",
  "BY",
  "BZ",
  "BZH",
  "CA",
  "CAB",
  "CAFE",
  "CAL",
  "CALL",
  "CALVINKLEIN",
  "CAM",
  "CAMERA",
  "CAMP",
  "CANCERRESEARCH",
  "CANON",
  "CAPETOWN",
  "CAPITAL",
  "CAPITALONE",
  "CAR",
  "CARAVAN",
  "CARDS",
  "CARE",
  "CAREER",
  "CAREERS",
  "CARS",
  "CASA",
  "CASE",
  "CASH",
  "CASINO",
  "CAT",
  "CATERING",
  "CATHOLIC",
  "CBA",
  "CBN",
  "CBRE",
  "CBS",
  "CC",
  "CD",
  "CENTER",
  "CEO",
  "CERN",
  "CF",
  "CFA",
  "CFD",
  "CG",
  "CH",
  "CHANEL",
  "CHANNEL",
  "CHARITY",
  "CHASE",
  "CHAT",
  "CHEAP",
  "CHINTAI",
  "CHRISTMAS",
  "CHROME",
  "CHURCH",
  "CI",
  "CIPRIANI",
  "CIRCLE",
  "CISCO",
  "CITADEL",
  "CITI",
  "CITIC",
  "CITY",
  "CITYEATS",
  "CK",
  "CL",
  "CLAIMS",
  "CLEANING",
  "CLICK",
  "CLINIC",
  "CLINIQUE",
  "CLOTHING",
  "CLOUD",
  "CLUB",
  "CLUBMED",
  "CM",
  "CN",
  "CO",
  "COACH",
  "CODES",
  "COFFEE",
  "COLLEGE",
  "COLOGNE",
  "COM",
  "COMCAST",
  "COMMBANK",
  "COMMUNITY",
  "COMPANY",
  "COMPARE",
  "COMPUTER",
  "COMSEC",
  "CONDOS",
  "CONSTRUCTION",
  "CONSULTING",
  "CONTACT",
  "CONTRACTORS",
  "COOKING",
  "COOKINGCHANNEL",
  "COOL",
  "COOP",
  "CORSICA",
  "COUNTRY",
  "COUPON",
  "COUPONS",
  "COURSES",
  "CPA",
  "CR",
  "CREDIT",
  "CREDITCARD",
  "CREDITUNION",
  "CRICKET",
  "CROWN",
  "CRS",
  "CRUISE",
  "CRUISES",
  "CSC",
  "CU",
  "CUISINELLA",
  "CV",
  "CW",
  "CX",
  "CY",
  "CYMRU",
  "CYOU",
  "CZ",
  "DABUR",
  "DAD",
  "DANCE",
  "DATA",
  "DATE",
  "DATING",
  "DATSUN",
  "DAY",
  "DCLK",
  "DDS",
  "DE",
  "DEAL",
  "DEALER",
  "DEALS",
  "DEGREE",
  "DELIVERY",
  "DELL",
  "DELOITTE",
  "DELTA",
  "DEMOCRAT",
  "DENTAL",
  "DENTIST",
  "DESI",
  "DESIGN",
  "DEV",
  "DHL",
  "DIAMONDS",
  "DIET",
  "DIGITAL",
  "DIRECT",
  "DIRECTORY",
  "DISCOUNT",
  "DISCOVER",
  "DISH",
  "DIY",
  "DJ",
  "DK",
  "DM",
  "DNP",
  "DO",
  "DOCS",
  "DOCTOR",
  "DOG",
  "DOMAINS",
  "DOT",
  "DOWNLOAD",
  "DRIVE",
  "DTV",
  "DUBAI",
  "DUCK",
  "DUNLOP",
  "DUPONT",
  "DURBAN",
  "DVAG",
  "DVR",
  "DZ",
  "EARTH",
  "EAT",
  "EC",
  "ECO",
  "EDEKA",
  "EDU",
  "EDUCATION",
  "EE",
  "EG",
  "EMAIL",
  "EMERCK",
  "ENERGY",
  "ENGINEER",
  "ENGINEERING",
  "ENTERPRISES",
  "EPSON",
  "EQUIPMENT",
  "ER",
  "ERICSSON",
  "ERNI",
  "ES",
  "ESQ",
  "ESTATE",
  "ET",
  "ETISALAT",
  "EU",
  "EUROVISION",
  "EUS",
  "EVENTS",
  "EXCHANGE",
  "EXPERT",
  "EXPOSED",
  "EXPRESS",
  "EXTRASPACE",
  "FAGE",
  "FAIL",
  "FAIRWINDS",
  "FAITH",
  "FAMILY",
  "FAN",
  "FANS",
  "FARM",
  "FARMERS",
  "FASHION",
  "FAST",
  "FEDEX",
  "FEEDBACK",
  "FERRARI",
  "FERRERO",
  "FI",
  "FIAT",
  "FIDELITY",
  "FIDO",
  "FILM",
  "FINAL",
  "FINANCE",
  "FINANCIAL",
  "FIRE",
  "FIRESTONE",
  "FIRMDALE",
  "FISH",
  "FISHING",
  "FIT",
  "FITNESS",
  "FJ",
  "FK",
  "FLICKR",
  "FLIGHTS",
  "FLIR",
  "FLORIST",
  "FLOWERS",
  "FLY",
  "FM",
  "FO",
  "FOO",
  "FOOD",
  "FOODNETWORK",
  "FOOTBALL",
  "FORD",
  "FOREX",
  "FORSALE",
  "FORUM",
  "FOUNDATION",
  "FOX",
  "FR",
  "FREE",
  "FRESENIUS",
  "FRL",
  "FROGANS",
  "FRONTDOOR",
  "FRONTIER",
  "FTR",
  "FUJITSU",
  "FUN",
  "FUND",
  "FURNITURE",
  "FUTBOL",
  "FYI",
  "GA",
  "GAL",
  "GALLERY",
  "GALLO",
  "GALLUP",
  "GAME",
  "GAMES",
  "GAP",
  "GARDEN",
  "GAY",
  "GB",
  "GBIZ",
  "GD",
  "GDN",
  "GE",
  "GEA",
  "GENT",
  "GENTING",
  "GEORGE",
  "GF",
  "GG",
  "GGEE",
  "GH",
  "GI",
  "GIFT",
  "GIFTS",
  "GIVES",
  "GIVING",
  "GL",
  "GLADE",
  "GLASS",
  "GLE",
  "GLOBAL",
  "GLOBO",
  "GM",
  "GMAIL",
  "GMBH",
  "GMO",
  "GMX",
  "GN",
  "GODADDY",
  "GOLD",
  "GOLDPOINT",
  "GOLF",
  "GOO",
  "GOODYEAR",
  "GOOG",
  "GOOGLE",
  "GOP",
  "GOT",
  "GOV",
  "GP",
  "GQ",
  "GR",
  "GRAINGER",
  "GRAPHICS",
  "GRATIS",
  "GREEN",
  "GRIPE",
  "GROCERY",
  "GROUP",
  "GS",
  "GT",
  "GU",
  "GUARDIAN",
  "GUCCI",
  "GUGE",
  "GUIDE",
  "GUITARS",
  "GURU",
  "GW",
  "GY",
  "HAIR",
  "HAMBURG",
  "HANGOUT",
  "HAUS",
  "HBO",
  "HDFC",
  "HDFCBANK",
  "HEALTH",
  "HEALTHCARE",
  "HELP",
  "HELSINKI",
  "HERE",
  "HERMES",
  "HGTV",
  "HIPHOP",
  "HISAMITSU",
  "HITACHI",
  "HIV",
  "HK",
  "HKT",
  "HM",
  "HN",
  "HOCKEY",
  "HOLDINGS",
  "HOLIDAY",
  "HOMEDEPOT",
  "HOMEGOODS",
  "HOMES",
  "HOMESENSE",
  "HONDA",
  "HORSE",
  "HOSPITAL",
  "HOST",
  "HOSTING",
  "HOT",
  "HOTELES",
  "HOTELS",
  "HOTMAIL",
  "HOUSE",
  "HOW",
  "HR",
  "HSBC",
  "HT",
  "HU",
  "HUGHES",
  "HYATT",
  "HYUNDAI",
  "IBM",
  "ICBC",
  "ICE",
  "ICU",
  "ID",
  "IE",
  "IEEE",
  "IFM",
  "IKANO",
  "IL",
  "IM",
  "IMAMAT",
  "IMDB",
  "IMMO",
  "IMMOBILIEN",
  "IN",
  "INC",
  "INDUSTRIES",
  "INFINITI",
  "INFO",
  "ING",
  "INK",
  "INSTITUTE",
  "INSURANCE",
  "INSURE",
  "INT",
  "INTERNATIONAL",
  "INTUIT",
  "INVESTMENTS",
  "IO",
  "IPIRANGA",
  "IQ",
  "IR",
  "IRISH",
  "IS",
  "ISMAILI",
  "IST",
  "ISTANBUL",
  "IT",
  "ITAU",
  "ITV",
  "JAGUAR",
  "JAVA",
  "JCB",
  "JE",
  "JEEP",
  "JETZT",
  "JEWELRY",
  "JIO",
  "JLL",
  "JM",
  "JMP",
  "JNJ",
  "JO",
  "JOBS",
  "JOBURG",
  "JOT",
  "JOY",
  "JP",
  "JPMORGAN",
  "JPRS",
  "JUEGOS",
  "JUNIPER",
  "KAUFEN",
  "KDDI",
  "KE",
  "KERRYHOTELS",
  "KERRYLOGISTICS",
  "KERRYPROPERTIES",
  "KFH",
  "KG",
  "KH",
  "KI",
  "KIA",
  "KIM",
  "KINDER",
  "KINDLE",
  "KITCHEN",
  "KIWI",
  "KM",
  "KN",
  "KOELN",
  "KOMATSU",
  "KOSHER",
  "KP",
  "KPMG",
  "KPN",
  "KR",
  "KRD",
  "KRED",
  "KUOKGROUP",
  "KW",
  "KY",
  "KYOTO",
  "KZ",
  "LA",
  "LACAIXA",
  "LAMBORGHINI",
  "LAMER",
  "LANCASTER",
  "LANCIA",
  "LAND",
  "LANDROVER",
  "LANXESS",
  "LASALLE",
  "LAT",
  "LATINO",
  "LATROBE",
  "LAW",
  "LAWYER",
  "LB",
  "LC",
  "LDS",
  "LEASE",
  "LECLERC",
  "LEFRAK",
  "LEGAL",
  "LEGO",
  "LEXUS",
  "LGBT",
  "LI",
  "LIDL",
  "LIFE",
  "LIFEINSURANCE",
  "LIFESTYLE",
  "LIGHTING",
  "LIKE",
  "LILLY",
  "LIMITED",
  "LIMO",
  "LINCOLN",
  "LINDE",
  "LINK",
  "LIPSY",
  "LIVE",
  "LIVING",
  "LIXIL",
  "LK",
  "LLC",
  "LLP",
  "LOAN",
  "LOANS",
  "LOCKER",
  "LOCUS",
  "LOFT",
  "LOL",
  "LONDON",
  "LOTTE",
  "LOTTO",
  "LOVE",
  "LPL",
  "LPLFINANCIAL",
  "LR",
  "LS",
  "LT",
  "LTD",
  "LTDA",
  "LU",
  "LUNDBECK",
  "LUXE",
  "LUXURY",
  "LV",
  "LY",
  "MA",
  "MACYS",
  "MADRID",
  "MAIF",
  "MAISON",
  "MAKEUP",
  "MAN",
  "MANAGEMENT",
  "MANGO",
  "MAP",
  "MARKET",
  "MARKETING",
  "MARKETS",
  "MARRIOTT",
  "MARSHALLS",
  "MASERATI",
  "MATTEL",
  "MBA",
  "MC",
  "MCKINSEY",
  "MD",
  "ME",
  "MED",
  "MEDIA",
  "MEET",
  "MELBOURNE",
  "MEME",
  "MEMORIAL",
  "MEN",
  "MENU",
  "MERCKMSD",
  "MG",
  "MH",
  "MIAMI",
  "MICROSOFT",
  "MIL",
  "MINI",
  "MINT",
  "MIT",
  "MITSUBISHI",
  "MK",
  "ML",
  "MLB",
  "MLS",
  "MM",
  "MMA",
  "MN",
  "MO",
  "MOBI",
  "MOBILE",
  "MODA",
  "MOE",
  "MOI",
  "MOM",
  "MONASH",
  "MONEY",
  "MONSTER",
  "MORMON",
  "MORTGAGE",
  "MOSCOW",
  "MOTO",
  "MOTORCYCLES",
  "MOV",
  "MOVIE",
  "MP",
  "MQ",
  "MR",
  "MS",
  "MSD",
  "MT",
  "MTN",
  "MTR",
  "MU",
  "MUSEUM",
  "MUTUAL",
  "MV",
  "MW",
  "MX",
  "MY",
  "MZ",
  "NA",
  "NAB",
  "NAGOYA",
  "NAME",
  "NATURA",
  "NAVY",
  "NBA",
  "NC",
  "NE",
  "NEC",
  "NET",
  "NETBANK",
  "NETFLIX",
  "NETWORK",
  "NEUSTAR",
  "NEW",
  "NEWS",
  "NEXT",
  "NEXTDIRECT",
  "NEXUS",
  "NF",
  "NFL",
  "NG",
  "NGO",
  "NHK",
  "NI",
  "NICO",
  "NIKE",
  "NIKON",
  "NINJA",
  "NISSAN",
  "NISSAY",
  "NL",
  "NO",
  "NOKIA",
  "NORTHWESTERNMUTUAL",
  "NORTON",
  "NOW",
  "NOWRUZ",
  "NOWTV",
  "NP",
  "NR",
  "NRA",
  "NRW",
  "NTT",
  "NU",
  "NYC",
  "NZ",
  "OBI",
  "OBSERVER",
  "OFF",
  "OFFICE",
  "OKINAWA",
  "OLAYAN",
  "OLAYANGROUP",
  "OLDNAVY",
  "OLLO",
  "OM",
  "OMEGA",
  "ONE",
  "ONG",
  "ONL",
  "ONLINE",
  "OOO",
  "OPEN",
  "ORACLE",
  "ORANGE",
  "ORG",
  "ORGANIC",
  "ORIGINS",
  "OSAKA",
  "OTSUKA",
  "OTT",
  "OVH",
  "PA",
  "PAGE",
  "PANASONIC",
  "PARIS",
  "PARS",
  "PARTNERS",
  "PARTS",
  "PARTY",
  "PASSAGENS",
  "PAY",
  "PCCW",
  "PE",
  "PET",
  "PF",
  "PFIZER",
  "PG",
  "PH",
  "PHARMACY",
  "PHD",
  "PHILIPS",
  "PHONE",
  "PHOTO",
  "PHOTOGRAPHY",
  "PHOTOS",
  "PHYSIO",
  "PICS",
  "PICTET",
  "PICTURES",
  "PID",
  "PIN",
  "PING",
  "PINK",
  "PIONEER",
  "PIZZA",
  "PK",
  "PL",
  "PLACE",
  "PLAY",
  "PLAYSTATION",
  "PLUMBING",
  "PLUS",
  "PM",
  "PN",
  "PNC",
  "POHL",
  "POKER",
  "POLITIE",
  "PORN",
  "POST",
  "PR",
  "PRAMERICA",
  "PRAXI",
  "PRESS",
  "PRIME",
  "PRO",
  "PROD",
  "PRODUCTIONS",
  "PROF",
  "PROGRESSIVE",
  "PROMO",
  "PROPERTIES",
  "PROPERTY",
  "PROTECTION",
  "PRU",
  "PRUDENTIAL",
  "PS",
  "PT",
  "PUB",
  "PW",
  "PWC",
  "PY",
  "QA",
  "QPON",
  "QUEBEC",
  "QUEST",
  "QVC",
  "RACING",
  "RADIO",
  "RAID",
  "RE",
  "READ",
  "REALESTATE",
  "REALTOR",
  "REALTY",
  "RECIPES",
  "RED",
  "REDSTONE",
  "REDUMBRELLA",
  "REHAB",
  "REISE",
  "REISEN",
  "REIT",
  "RELIANCE",
  "REN",
  "RENT",
  "RENTALS",
  "REPAIR",
  "REPORT",
  "REPUBLICAN",
  "REST",
  "RESTAURANT",
  "REVIEW",
  "REVIEWS",
  "REXROTH",
  "RICH",
  "RICHARDLI",
  "RICOH",
  "RIL",
  "RIO",
  "RIP",
  "RMIT",
  "RO",
  "ROCHER",
  "ROCKS",
  "RODEO",
  "ROGERS",
  "ROOM",
  "RS",
  "RSVP",
  "RU",
  "RUGBY",
  "RUHR",
  "RUN",
  "RW",
  "RWE",
  "RYUKYU",
  "SA",
  "SAARLAND",
  "SAFE",
  "SAFETY",
  "SAKURA",
  "SALE",
  "SALON",
  "SAMSCLUB",
  "SAMSUNG",
  "SANDVIK",
  "SANDVIKCOROMANT",
  "SANOFI",
  "SAP",
  "SARL",
  "SAS",
  "SAVE",
  "SAXO",
  "SB",
  "SBI",
  "SBS",
  "SC",
  "SCA",
  "SCB",
  "SCHAEFFLER",
  "SCHMIDT",
  "SCHOLARSHIPS",
  "SCHOOL",
  "SCHULE",
  "SCHWARZ",
  "SCIENCE",
  "SCJOHNSON",
  "SCOT",
  "SD",
  "SE",
  "SEARCH",
  "SEAT",
  "SECURE",
  "SECURITY",
  "SEEK",
  "SELECT",
  "SENER",
  "SERVICES",
  "SES",
  "SEVEN",
  "SEW",
  "SEX",
  "SEXY",
  "SFR",
  "SG",
  "SH",
  "SHANGRILA",
  "SHARP",
  "SHAW",
  "SHELL",
  "SHIA",
  "SHIKSHA",
  "SHOES",
  "SHOP",
  "SHOPPING",
  "SHOUJI",
  "SHOW",
  "SHOWTIME",
  "SI",
  "SILK",
  "SINA",
  "SINGLES",
  "SITE",
  "SJ",
  "SK",
  "SKI",
  "SKIN",
  "SKY",
  "SKYPE",
  "SL",
  "SLING",
  "SM",
  "SMART",
  "SMILE",
  "SN",
  "SNCF",
  "SO",
  "SOCCER",
  "SOCIAL",
  "SOFTBANK",
  "SOFTWARE",
  "SOHU",
  "SOLAR",
  "SOLUTIONS",
  "SONG",
  "SONY",
  "SOY",
  "SPA",
  "SPACE",
  "SPORT",
  "SPOT",
  "SR",
  "SRL",
  "SS",
  "ST",
  "STADA",
  "STAPLES",
  "STAR",
  "STATEBANK",
  "STATEFARM",
  "STC",
  "STCGROUP",
  "STOCKHOLM",
  "STORAGE",
  "STORE",
  "STREAM",
  "STUDIO",
  "STUDY",
  "STYLE",
  "SU",
  "SUCKS",
  "SUPPLIES",
  "SUPPLY",
  "SUPPORT",
  "SURF",
  "SURGERY",
  "SUZUKI",
  "SV",
  "SWATCH",
  "SWIFTCOVER",
  "SWISS",
  "SX",
  "SY",
  "SYDNEY",
  "SYSTEMS",
  "SZ",
  "TAB",
  "TAIPEI",
  "TALK",
  "TAOBAO",
  "TARGET",
  "TATAMOTORS",
  "TATAR",
  "TATTOO",
  "TAX",
  "TAXI",
  "TC",
  "TCI",
  "TD",
  "TDK",
  "TEAM",
  "TECH",
  "TECHNOLOGY",
  "TEL",
  "TEMASEK",
  "TENNIS",
  "TEVA",
  "TF",
  "TG",
  "TH",
  "THD",
  "THEATER",
  "THEATRE",
  "TIAA",
  "TICKETS",
  "TIENDA",
  "TIFFANY",
  "TIPS",
  "TIRES",
  "TIROL",
  "TJ",
  "TJMAXX",
  "TJX",
  "TK",
  "TKMAXX",
  "TL",
  "TM",
  "TMALL",
  "TN",
  "TO",
  "TODAY",
  "TOKYO",
  "TOOLS",
  "TOP",
  "TORAY",
  "TOSHIBA",
  "TOTAL",
  "TOURS",
  "TOWN",
  "TOYOTA",
  "TOYS",
  "TR",
  "TRADE",
  "TRADING",
  "TRAINING",
  "TRAVEL",
  "TRAVELCHANNEL",
  "TRAVELERS",
  "TRAVELERSINSURANCE",
  "TRUST",
  "TRV",
  "TT",
  "TUBE",
  "TUI",
  "TUNES",
  "TUSHU",
  "TV",
  "TVS",
  "TW",
  "TZ",
  "UA",
  "UBANK",
  "UBS",
  "UG",
  "UK",
  "UNICOM",
  "UNIVERSITY",
  "UNO",
  "UOL",
  "UPS",
  "US",
  "UY",
  "UZ",
  "VA",
  "VACATIONS",
  "VANA",
  "VANGUARD",
  "VC",
  "VE",
  "VEGAS",
  "VENTURES",
  "VERISIGN",
  "VERSICHERUNG",
  "VET",
  "VG",
  "VI",
  "VIAJES",
  "VIDEO",
  "VIG",
  "VIKING",
  "VILLAS",
  "VIN",
  "VIP",
  "VIRGIN",
  "VISA",
  "VISION",
  "VIVA",
  "VIVO",
  "VLAANDEREN",
  "VN",
  "VODKA",
  "VOLKSWAGEN",
  "VOLVO",
  "VOTE",
  "VOTING",
  "VOTO",
  "VOYAGE",
  "VU",
  "VUELOS",
  "WALES",
  "WALMART",
  "WALTER",
  "WANG",
  "WANGGOU",
  "WATCH",
  "WATCHES",
  "WEATHER",
  "WEATHERCHANNEL",
  "WEBCAM",
  "WEBER",
  "WEBSITE",
  "WED",
  "WEDDING",
  "WEIBO",
  "WEIR",
  "WF",
  "WHOSWHO",
  "WIEN",
  "WIKI",
  "WILLIAMHILL",
  "WIN",
  "WINDOWS",
  "WINE",
  "WINNERS",
  "WME",
  "WOLTERSKLUWER",
  "WOODSIDE",
  "WORK",
  "WORKS",
  "WORLD",
  "WOW",
  "WS",
  "WTC",
  "WTF",
  "XBOX",
  "XEROX",
  "XFINITY",
  "XIHUAN",
  "XIN",
  "XN--11B4C3D",
  "XN--1CK2E1B",
  "XN--1QQW23A",
  "XN--2SCRJ9C",
  "XN--30RR7Y",
  "XN--3BST00M",
  "XN--3DS443G",
  "XN--3E0B707E",
  "XN--3HCRJ9C",
  "XN--3OQ18VL8PN36A",
  "XN--3PXU8K",
  "XN--42C2D9A",
  "XN--45BR5CYL",
  "XN--45BRJ9C",
  "XN--45Q11C",
  "XN--4DBRK0CE",
  "XN--4GBRIM",
  "XN--54B7FTA0CC",
  "XN--55QW42G",
  "XN--55QX5D",
  "XN--5SU34J936BGSG",
  "XN--5TZM5G",
  "XN--6FRZ82G",
  "XN--6QQ986B3XL",
  "XN--80ADXHKS",
  "XN--80AO21A",
  "XN--80AQECDR1A",
  "XN--80ASEHDB",
  "XN--80ASWG",
  "XN--8Y0A063A",
  "XN--90A3AC",
  "XN--90AE",
  "XN--90AIS",
  "XN--9DBQ2A",
  "XN--9ET52U",
  "XN--9KRT00A",
  "XN--B4W605FERD",
  "XN--BCK1B9A5DRE4C",
  "XN--C1AVG",
  "XN--C2BR7G",
  "XN--CCK2B3B",
  "XN--CCKWCXETD",
  "XN--CG4BKI",
  "XN--CLCHC0EA0B2G2A9GCD",
  "XN--CZR694B",
  "XN--CZRS0T",
  "XN--CZRU2D",
  "XN--D1ACJ3B",
  "XN--D1ALF",
  "XN--E1A4C",
  "XN--ECKVDTC9D",
  "XN--EFVY88H",
  "XN--FCT429K",
  "XN--FHBEI",
  "XN--FIQ228C5HS",
  "XN--FIQ64B",
  "XN--FIQS8S",
  "XN--FIQZ9S",
  "XN--FJQ720A",
  "XN--FLW351E",
  "XN--FPCRJ9C3D",
  "XN--FZC2C9E2C",
  "XN--FZYS8D69UVGM",
  "XN--G2XX48C",
  "XN--GCKR3F0F",
  "XN--GECRJ9C",
  "XN--GK3AT1E",
  "XN--H2BREG3EVE",
  "XN--H2BRJ9C",
  "XN--H2BRJ9C8C",
  "XN--HXT814E",
  "XN--I1B6B1A6A2E",
  "XN--IMR513N",
  "XN--IO0A7I",
  "XN--J1AEF",
  "XN--J1AMH",
  "XN--J6W193G",
  "XN--JLQ480N2RG",
  "XN--JLQ61U9W7B",
  "XN--JVR189M",
  "XN--KCRX77D1X4A",
  "XN--KPRW13D",
  "XN--KPRY57D",
  "XN--KPUT3I",
  "XN--L1ACC",
  "XN--LGBBAT1AD8J",
  "XN--MGB9AWBF",
  "XN--MGBA3A3EJT",
  "XN--MGBA3A4F16A",
  "XN--MGBA7C0BBN0A",
  "XN--MGBAAKC7DVF",
  "XN--MGBAAM7A8H",
  "XN--MGBAB2BD",
  "XN--MGBAH1A3HJKRD",
  "XN--MGBAI9AZGQP6J",
  "XN--MGBAYH7GPA",
  "XN--MGBBH1A",
  "XN--MGBBH1A71E",
  "XN--MGBC0A9AZCG",
  "XN--MGBCA7DZDO",
  "XN--MGBCPQ6GPA1A",
  "XN--MGBERP4A5D4AR",
  "XN--MGBGU82A",
  "XN--MGBI4ECEXP",
  "XN--MGBPL2FH",
  "XN--MGBT3DHD",
  "XN--MGBTX2B",
  "XN--MGBX4CD0AB",
  "XN--MIX891F",
  "XN--MK1BU44C",
  "XN--MXTQ1M",
  "XN--NGBC5AZD",
  "XN--NGBE9E0A",
  "XN--NGBRX",
  "XN--NODE",
  "XN--NQV7F",
  "XN--NQV7FS00EMA",
  "XN--NYQY26A",
  "XN--O3CW4H",
  "XN--OGBPF8FL",
  "XN--OTU796D",
  "XN--P1ACF",
  "XN--P1AI",
  "XN--PGBS0DH",
  "XN--PSSY2U",
  "XN--Q7CE6A",
  "XN--Q9JYB4C",
  "XN--QCKA1PMC",
  "XN--QXA6A",
  "XN--QXAM",
  "XN--RHQV96G",
  "XN--ROVU88B",
  "XN--RVC1E0AM3E",
  "XN--S9BRJ9C",
  "XN--SES554G",
  "XN--T60B56A",
  "XN--TCKWE",
  "XN--TIQ49XQYJ",
  "XN--UNUP4Y",
  "XN--VERMGENSBERATER-CTB",
  "XN--VERMGENSBERATUNG-PWB",
  "XN--VHQUV",
  "XN--VUQ861B",
  "XN--W4R85EL8FHU5DNRA",
  "XN--W4RS40L",
  "XN--WGBH1C",
  "XN--WGBL6A",
  "XN--XHQ521B",
  "XN--XKC2AL3HYE2A",
  "XN--XKC2DL3A5EE0H",
  "XN--Y9A3AQ",
  "XN--YFRO4I67O",
  "XN--YGBI2AMMX",
  "XN--ZFR164B",
  "XXX",
  "XYZ",
  "YACHTS",
  "YAHOO",
  "YAMAXUN",
  "YANDEX",
  "YE",
  "YODOBASHI",
  "YOGA",
  "YOKOHAMA",
  "YOU",
  "YOUTUBE",
  "YT",
  "YUN",
  "ZA",
  "ZAPPOS",
  "ZARA",
  "ZERO",
  "ZIP",
  "ZM",
  "ZONE",
  "ZUERICH",
  "ZW"
]

DOMAIN_STR = DOMAINS.map { |domain| "(\.#{domain}$)" }.join("|")
