# frozen_string_literal: true

class Ability
  include CanCan::Ability

  def initialize(user)
    # Define abilities for the passed in user here. For example:
    #

    user ||= User.new # guest user (not logged in)

    can [:read, :update], Pupil, id: user.id
    can [:read, :update], User, id: user.id
    can :read, Career, { complete: true, organisation_id: [nil, user.organisation_id, user.white_label_organisation_id, user.school&.organisation_id] }
    can :read, TrainingRoute
    can :read, WordSearchLobby
    can :read, WordSearchLobbyUser
    can :read, Achievement
    can :read, Accreditation
    can :read, CareerTag
    can :read, CareerVacancy, published: true
    can :read, CareerCourse, published: true
    can :read, Event, published: true
    can :read, Organisation, published: true
    can :read, false
    can :read, NewPresentation, { published: [1] }
    can :read, Lesson::Template, { user_generated: false }
    can :read, Lesson::Slide
    can :read, Lesson::Keyword
    can :read, QuizOld::Question
    can :read, UkSchool
    can :read, NewLibrary::Year
    can :read, Video
    can :create, VideoView
    can :read, NewLibrary::Unit
    can :read, QuipQuestion
    can :read, Questionnaire, { published: true }
    can :read, QuestionnaireQuestion
    can :read, QuestionnaireOption
    can :create, QuestionnaireAnswer, { user_id: [nil, user.id] }
    can :manage, QuestionnaireAnswer, { user_id: user.id }
    can :read, SignUpEvent
    can :read, DeMedia, { published: true }
    can :read, DeSocial, { published: true }
    can :read, Sponsor
    can :read, CustomSignUpUrl, { published: true }
    can :read, ScientificEnquiryType
    can :read, JobListing, { published: true }
    can :create, LessonReport

    can :read, LiveStream, { published: true }

    can :create, LiveStreamMessage, { user_id: user.id }
    can :read, LiveStreamMessage, { user_id: user.id }

    can :read, CampaignUnit
    can :create, CampaignUnitView
    can :create, CampaignUnitExternalClick

    can :read, CampaignLesson
    can :create, CampaignLessonView
    can :create, CampaignLessonExternalClick

    can :create, CampaignEventView
    can :create, CampaignEventExternalClick

    can :create, CampaignVacancyView
    can :create, CampaignVacancyExternalClick

    can :create, CampaignCourseView
    can :create, CampaignCourseExternalClick

    can :create, ProfileView
    can :create, ProfileExternalClick

    can :read, Quiz
    can :create, QuizView

    can :read, Tour
    can :create, TourView

    can :manage, Invite, user_id: user.id
    can :manage, InviteRequest, guardian_id: user.id

    can :create, LessonPlanView

    can :read, Author

    can :read, Country

    can :read, QuipQuiz, { user_generated: false } # Published by DE
    can :read, QuipQuiz, { user_generated: true, user_id: user.id } # Own
    can :read, QuipQuiz, { user_generated: true, user: { school_id: user.school_id } } # School

    # can read own
    can :read, QuipQuizResult, { user: user }

    can :read, LiveStreamDocument, { published: true }

    can :read, ExemplarWork, ["status = ? AND display_name IS NOT NULL AND display_name != ''", 1]
    can :read, ExemplarWork, user_id: user.id if user.present?

    can :read, Glossary, { published: true }

    if user.admin?
      can :read, NewLibrary::Curriculum
    elsif user.id.blank? || user.individual_user?
      can :read, NewLibrary::Curriculum, { id: [18, 7] }
    else
      can :read, NewLibrary::Curriculum, { published: true }
    end

    if user.admin?
      can :manage, QuipQuizResult
      can :manage, LessonReport
      can :manage, Homework
      can :manage, HomeworkTask
      can :manage, HomeworkTaskSubmission
      can :manage, HomeworkFile
      can :manage, JobListing
      can :manage, ScientificEnquiryType
      can :manage, CustomSignUpUrl
      can :manage, Glossary
      can :manage, ExemplarWork
      can :manage, SignUpEvent
      can :manage, LiveStreamDocument
      can :manage, LiveStream
      can :manage, LiveStreamMessage
      can :manage, Questionnaire
      can :manage, QuestionnaireQuestion
      can :manage, QuestionnaireOption
      can :manage, QuestionnaireAnswer
      can :manage, CurriculumDocument
      can :manage, LessonPlanView
      can :manage, Motd
      can :manage, QuipQuiz
      can :manage, QuipQuestion
      can :manage, Campaign
      can :manage, Pupil
      can :manage, Teacher
      can :manage, Form
      can :manage, Lesson::Lesson
      can :manage, School
      can :manage, IndustryTag
      can :manage, Lesson::Template
      can :manage, Sponsor
      can :manage, JobListing
      can :manage, Career
      can :manage, Event
      can :manage, CareerVacancy
      can :manage, CareerCourse
      can :manage, Organisation
      can :read, WordSearchLobby
      can :read, WordSearchLobbyUser
      can :manage, User
      can :manage, TrainingRoute
      can :manage, Achievement
      can :manage, Accreditation
      can :manage, CareerTag
      can :manage, TrackingLogin
      can :manage, Invite
      can :manage, InviteRequest
      can :manage, CareerCourse
      can :manage, Video
      can :manage, VideoView
      can :manage, NewLibrary::Unit
      can :manage, NewLibrary::Year
      can :manage, CampaignUnit
      can :manage, CampaignUnitView
      can :manage, CampaignLesson
      can :manage, CampaignLessonView
      can :manage, CampaignEventView
      can :manage, CampaignVacancyView
      can :manage, CampaignCourseView
      can :manage, ProfileView
      can :manage, ProfileExternalClick
      can :manage, CampaignUnitExternalClick
      can :manage, CampaignLessonExternalClick
      can :manage, CampaignEventExternalClick
      can :manage, CampaignVacancyExternalClick
      can :manage, CampaignCourseExternalClick
      can :manage, Quiz
      can :manage, QuizView
      can :manage, TrackingSummativeQuiz
      can :manage, Tour
      can :manage, TourView
      can :manage, WondeImport
      can :manage, WondeTeacherRealForm
      can :manage, WondePupilRealForm
      can :manage, WondeImportTeacher
      can :manage, WondeImportPupil
      can :manage, WondeImportForm
      can :manage, WondeImportPupilForm
      can :manage, WondeImportTeacherForm
      can :manage, NewPresentation
      can :manage, UserRankEvent
      can :manage, LessonTemplateFolder
      can :manage, FormUnit
      can :manage, DeMedia
      can :manage, DeSocial
    end

    if user.employer?
      can :read, Motd, { is_draft: false }
      can :manage, CareerCourse, organisation_id: user.organisation_id
      can :manage, CareerVacancy, organisation_id: user.organisation_id
      can :manage, Event, organisation_id: user.organisation_id
      can :manage, Organisation, id: user.organisation_id
      can :manage, Campaign, organisation_id: user.organisation_id
      can :read, NewLibrary::Unit
      can :manage, Career, organisation_id: user.organisation_id
      can :manage, TrainingRoute, career: { organisation_id: user.organisation_id }

      can :manage, NewPresentation, { user_id: user.id }
      can :read, NewPresentation, { published: [1, 2] }
      can :manage, Lesson::Template, { user_generated: true, organisation: user.organisation }
      can :manage, Lesson::Keyword, { template_id: user.user_lesson_templates.ids }
      can :manage, Lesson::Document, { template_id: user.user_lesson_templates.ids }
      can :manage, Author, { organisation: user&.organisation }
      can :manage, QuipQuiz, { user_generated: true, organisation: user.organisation }
    end

    if user.teacher?
      can :manage, QuipQuiz, { user_generated: true, user: user }
      can :read, QuipQuizResult, { user: { school_id: user.school_id } }
      can :create, Homework
      can :manage, Homework, { school_id: user.school_id, created_by_id: user.id }
      can :manage, HomeworkTask
      can :manage, HomeworkTaskSubmission
      can :manage, HomeworkFile
      can :read, Teacher, school_id: user.school_id
      can :manage, ExemplarWork, user_id: user.id
      can :manage, LessonTemplateFolder, { user_id: user.id }
      can :read, CurriculumDocument
      can :read, Motd, { is_draft: false }
      can :read, School, id: user.school_id
      can :manage, Pupil, id: user.pupil_ids
      can :read, Lesson::Lesson, id: user.lessons.ids
      can :manage, Form, enrollments: { user_id: user.id }
      can :manage, PupilUnitMark, { user_id: user.pupil_ids }
      can :read, Organisation

      can :manage, NewPresentation, { user_id: user.id }
      can :read, NewPresentation, { published: [1, 2] }
      # THESE SHOULD MATCH THE LESSON TEMPLATE VISIBILITY RULES
      can :read, NewPresentation, { lesson_template: { user_generated: true, available: true, user: { school_id: user.school_id } } }
      can :read, NewPresentation, { lesson_template: { user_generated: true, forms: { teachers: { id: user.id } } } }
      can :read, Lesson::Template, { user_generated: false }
      can :read, Lesson::Template, { user_generated: true, available: true, user: { school_id: user.school_id } }
      can :read, Lesson::Template, { user_generated: true, forms: { teachers: { id: user.id } } }
      can :manage, Lesson::Template, { user_generated: true, user_id: user.id }
      can :manage, Lesson::Keyword, { template_id: user.user_lesson_templates.ids }
      can :manage, Lesson::Document, { template_id: user.user_lesson_templates.ids }
      can :manage, Author, { user: user }
      can :manage, FormUnit, { form_id: user.all_forms.ids }

      if user.is_school_admin?
        can :read, Lesson::Lesson, id: user.school.lessons.ids
        can :manage, InviteRequest, pupil: { school_id: user.school_id }
        can :manage, Invite, pupil: { school_id: user.school_id }
        can :manage, Pupil, school_id: user.school_id
        can :manage, Form, school_id: user.school.id
        can :update, School, id: user.school_id
        can :manage, Teacher, school_id: user.school_id
        can :manage, WondeImport, school_id: user.school_id
        can :manage, WondeTeacherRealForm, wonde_import: { school_id: user.school_id }
        can :manage, WondePupilRealForm, wonde_import: { school_id: user.school_id }
        can :manage, WondeImportTeacher, wonde_import: { school_id: user.school_id }
        can :manage, WondeImportPupil, wonde_import: { school_id: user.school_id }
        can :manage, WondeImportForm, wonde_import: { school_id: user.school_id }
        can :manage, WondeImportPupilForm, wonde_import: { school_id: user.school_id }
        can :manage, WondeImportTeacherForm, wonde_import: { school_id: user.school_id }
        can :manage, FormUnit, { form_id: user.school.all_forms.ids }
      else
        can :manage, InviteRequest, pupil: { id: user.pupil_ids }
        can :manage, Invite, pupil: { id: user.pupil_ids }
      end
    end

    if (user.pupil?)
      can :read, Homework, { school_id: user.school_id, published: true, pupils: { id: user.id } }
      can :read, HomeworkTask
      can :manage, HomeworkTaskSubmission, { user: user }
      can :manage, HomeworkFile

      can :read, NewPresentation, { published: [1, 3] }
      # THESE SHOULD MATCH THE LESSON TEMPLATE VISIBILITY RULES
      can :read, NewPresentation, { lesson_template: { user_generated: true, available: true, user: { school_id: user.school_id } } }
      can :read, NewPresentation, { lesson_template: { user_generated: true, forms: { pupils: { id: user.id } } } }
      can :read, Motd, { is_draft: false }
      can :read, Lesson::Lesson, { pupils: { id: user.id }, template: user.accessible_lesson_templates }
      can :read, Lesson::Template, { user_generated: false }
      can :read, Lesson::Template, { user_generated: true, available: true, user: { school_id: user.school_id } }
      can :read, Lesson::Template, { user_generated: true, forms: { pupils: { id: user.id } } }
      can :read, Form, enrollments: { user_id: user.id }
      can :manage, InviteRequest, pupil_id: user.id
      can :manage, FormUnit, { form_id: user.all_forms.ids }
    end
    #
    # The first argument to `can` is the action you are giving the user
    # permission to do.
    # If you pass :manage it will apply to every action. Other common actions
    # here are :read, :create, :update and :destroy.
    #
    # The second argument is the resource the user can perform the action on.
    # If you pass :all it will apply to every resource. Otherwise pass a Ruby
    # class of the resource.
    #
    # The third argument is an optional hash of conditions to further filter the
    # objects.
    # For example, here the user can only update published articles.
    #
    #   can :update, Article, :published => true
    #
    # See the wiki for details:
    # https://github.com/CanCanCommunity/cancancan/wiki/Defining-Abilities
  end
end
