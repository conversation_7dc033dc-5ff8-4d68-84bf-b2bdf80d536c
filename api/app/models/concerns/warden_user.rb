module WardenUser
  extend ActiveSupport::Concern

  included do
    has_secure_password

    # raise 'oh no'

    before_validation do
      self.provider ||= "email"
      if provider == "email" && (uid.blank? || (will_save_change_to_email? && email_was == uid))
        self.uid = email.to_s.downcase
      end
    end

    validates :provider, :uid, :email, presence: true

    validate do
      next unless uid.present? && provider.present?

      if User.where.not(id: id).where(uid: uid, provider: provider).any?
        if provider == 'email'
          errors.add(:email, 'has already been taken')
        else
          errors.add(:uid, "has already been taken for provider '#{provider}'")
        end
      end
    end
  end

  def new_session password = nil, skip_authentication: false
    client = new_token
    token = new_token
    expiry = Time.current + 20.years

    my_tokens = tokens || {}

    with_lock do
      return false unless skip_authentication || authenticate(password)
      my_tokens[client] = { "access-token": token, expiry: expiry }.as_json
      User.logger.silence { update_columns(tokens: my_tokens, last_sign_in_at: Time.current) }
    end

    my_tokens[client].merge('uid' => uid, 'client' => client, 'provider' => provider)
  end

  def close_session client
    with_lock do
      User.logger.silence { update_columns(tokens: {}, last_sign_out_at: Time.current) }
    end
  end

  def verify_token client, token
    with_lock do
      if client && token && tokens.dig(client, 'access-token') == token
        User.logger.silence { update_columns(last_activity_at: Time.current) }
        true
      else
        false
      end
    end
  end

  def new_recovery_token
    token = new_token
    with_lock { update_columns(recovery_token: token, recovery_requested_at: Time.current) }
    token
  end

  private

  def new_token
    User.generate_unique_secure_token
  end
end
