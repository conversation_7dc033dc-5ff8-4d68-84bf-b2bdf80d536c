# == Schema Information
#
# Table name: organisations
#
#  id                   :bigint           not null, primary key
#  address_line_1       :string
#  address_line_2       :string
#  address_line_3       :string
#  alias                :string
#  body                 :string
#  county               :string
#  education_range      :string
#  establishment_name   :string
#  establishment_number :string
#  establishment_type   :string
#  fax                  :string
#  featured             :boolean          default(FALSE)
#  for_gender           :string
#  head_teacher_name    :string
#  is_sponsor           :boolean          default(FALSE)
#  kpi_stats_cache      :jsonb
#  lat                  :float
#  lng                  :float
#  name                 :string
#  open_date            :datetime
#  organisation_type    :integer          default("company")
#  phone_number         :string
#  postcode             :string
#  published            :boolean          default(FALSE)
#  pupil_text           :text
#  slug                 :string
#  student_count        :integer
#  testimonial          :text
#  testimonial_by       :text
#  town                 :string
#  uid                  :string
#  use_white_labelling  :boolean          default(FALSE)
#  website              :string
#  white_label_content  :jsonb
#  white_label_style    :jsonb
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  expert_video_id      :bigint
#  fileboy_image_id     :string
#  gtr_id               :integer
#  hesa_id              :integer
#  promotional_video_id :bigint
#  testimonial_video_id :bigint
#
# Indexes
#
#  index_organisations_on_expert_video_id       (expert_video_id)
#  index_organisations_on_is_sponsor            (is_sponsor)
#  index_organisations_on_promotional_video_id  (promotional_video_id)
#  index_organisations_on_slug                  (slug) UNIQUE
#  index_organisations_on_testimonial_video_id  (testimonial_video_id)
#
# Foreign Keys
#
#  fk_rails_...  (expert_video_id => videos2.id)
#  fk_rails_...  (promotional_video_id => videos.id)
#  fk_rails_...  (testimonial_video_id => videos.id)
#
class Organisation < ApplicationRecord
  extend FriendlyId
  friendly_id :name, use: :slugged

  enum organisation_type: { company: 0, university: 1, college: 2, school: 3, training_provider: 4 }

  validates :name, :organisation_type, presence: true

  has_many :profile_views
  has_many :career_courses
  has_many :career_vacancies
  has_many :events
  has_many :users
  has_many :campaigns

  has_many :campaign_lesson_views, through: :campaigns
  has_many :campaign_unit_views, through: :campaigns
  has_many :campaign_course_views, through: :campaigns
  has_many :campaign_vacancy_views, through: :campaigns
  has_many :campaign_event_views, through: :campaigns
  has_many :video_views, through: :campaigns
  has_many :motds

  has_many :campaign_lessons, through: :campaigns
  has_many :campaign_units, through: :campaigns

  has_many :campaign_lesson_external_clicks, through: :campaigns
  has_many :campaign_unit_external_clicks, through: :campaigns
  has_many :campaign_course_external_clicks, through: :campaigns
  has_many :campaign_vacancy_external_clicks, through: :campaigns
  has_many :campaign_event_external_clicks, through: :campaigns

  has_many :organisation_careers
  has_many :careers, through: :organisation_careers

  has_many :careers

  belongs_to :expert_video, optional: true, class_name: 'Video'

  has_many :videos, through: :campaigns
  has_many :campaign_vacancies, through: :campaigns

  has_many :white_label_users, class_name: 'User', foreign_key: :white_label_organisation_id
  has_many :schools

  has_many :lesson_templates, class_name: 'Lesson::Template'

  has_one :author

  belongs_to :promotional_video, class_name: 'Video', optional: true
  belongs_to :testimonial_video, class_name: 'Video', optional: true
  belongs_to :expert_video, class_name: 'Video', optional: true

  def external_promotional_video_id
    promotional_video&.external_id
  end

  def external_testimonial_video_id
    testimonial_video&.external_id
  end

  def external_expert_video_id
    expert_video&.external_id
  end

  def organisation_logo
    return nil unless fileboy_image_id.present?
    "https://www.developingexperts.com/file-cdn/images/get/#{fileboy_image_id}?transform=resize:200x_"
  end

  def organisation_icon
    return nil unless fileboy_image_id.present?
    "https://www.developingexperts.com/file-cdn/images/get/#{fileboy_image_id}?transform=resize:64x_"
  end

  def self.with_dashboard_data(organisation_ids)
    # Assuming the following associations:
    # Organisation has_many :users
    # User has_many :campaign_lesson_views
    # User has_many :campaign_unit_views

    # Fetch organization IDs where users have campaign_lesson_views
    org_ids_with_lesson_views = Organisation
                                .joins(:campaign_lesson_views)
                                .where(id: organisation_ids)
                                .distinct
                                .pluck(:id)

    # Fetch organization IDs where users have campaign_unit_views
    org_ids_with_unit_views = Organisation
                              .joins(:campaign_unit_views)
                              .where(id: organisation_ids)
                              .distinct
                              .pluck(:id)

    # Combine IDs and convert to a Set for efficient lookups
    (org_ids_with_lesson_views + org_ids_with_unit_views).uniq.to_set
  end

  def v2_as_json(_options = nil)
    as_json.merge({
                    career_course_ids: career_course_ids,
                    career_vacancy_ids: career_vacancy_ids,
                    event_ids: event_ids,
                    promotionalVideo: promotional_video,
                    testimonialVideo: testimonial_video,
                    school_ids: schools.ids,
                    white_label_user_ids: white_label_users.ids,
                    career_ids: careers.ids,
                    expert_video: expert_video,
                  })
  end

  def kpi_stats_by_type(stat_type, options, recache: false)
    allowed_stats = {
      'career' => { 'event_name' => 'career path viewed', 'filter_name' => 'career' },
      'lesson' => { 'event_name' => 'lesson template viewed', 'filter_name' => 'lesson_template' },
      'unit' => { 'event_name' => 'unit viewed', 'filter_name' => 'unit' },
      'school' => { 'event_name' => 'lesson template viewed', 'filter_name' => 'school' },
    }

    return unless allowed_stats[stat_type].present?

    filters = options.compact.transform_values { 1 }

    filter_name = allowed_stats[stat_type]['filter_name']
    filter_key = [filter_name, *options.values.map { _1 || '*' }].join('_')
    puts "FETCHING FILTER KEY #{filter_key}"

    existing_data = kpi_stats_cache || {}
    if existing_data[filter_key].present? && !recache
      puts "FETCHING CACHE FILTER KEY #{filter_key}"
      # Check if the data is older than 1 hour
      return existing_data[filter_key]['data'] if existing_data[filter_key]['date'] && (Time.now.to_i - existing_data[filter_key]['date']) < 1.day.to_i
      # If the data is older than 1 day, remove it from the cache
      existing_data.delete(filter_key)
    end

    puts "FETCHING  KEY #{filter_key}"

    range = {
      'eyfs' => [1, 4],
      'ks1_2' => [5, 11],
      'ks3' => [12, 14],
      'ks4' => [15, 17],
    }[options[:age]]
    options[:dob_start] = DateTime.now.strftime('%Y-%m-%d')
    options[:dob_end] = DateTime.now.strftime('%Y-%m-%d')
    if filters.key?(:age) && range
      options[:dob_start] = (Date.today - range[1].years).strftime('%Y-%m-%d')
      options[:dob_end] = (Date.today - range[0].years).strftime('%Y-%m-%d')
    end

    campaign_ids = self.campaign_ids.map { |id| "'#{id}'" }.join(',')

    results = case stat_type
              when 'lesson'
                OrganisationStatsCacheService.get_lesson_kpi_stats(campaign_ids, options, filters)
              when 'career'
                OrganisationStatsCacheService.get_career_kpi_stats(options, filters)
              when 'unit'
                OrganisationStatsCacheService.get_unit_kpi_stats(campaign_ids, options, filters)
              when 'school'
                OrganisationStatsCacheService.get_school_kpi_stats(campaign_ids, options, filters)
              end

    reload
    existing_data = kpi_stats_cache || {}
    existing_data[filter_key] = {
      date: Time.now.to_i,
      data: results
    }

    update(kpi_stats_cache: existing_data)
    results
  end

  def to_param
    if RequestStore.store[:admin_request]
      id.to_s
    else
      super # returns slug
    end
  end
end
