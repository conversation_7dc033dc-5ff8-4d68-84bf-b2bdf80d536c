# frozen_string_literal: true

# == Schema Information
#
# Table name: users
#
#  id                                    :bigint           not null, primary key
#  admin_permissions                     :jsonb
#  alias                                 :string
#  avatar_configuration                  :jsonb
#  beta_features                         :jsonb
#  blocked_at                            :datetime
#  can_access_career_builder             :boolean
#  can_access_lesson_ai                  :boolean
#  can_create_lessons                    :boolean          default(FALSE)
#  can_view_school_data                  :boolean          default(FALSE)
#  communication_preferences             :jsonb
#  deleted                               :boolean          default(FALSE), not null
#  dob                                   :datetime
#  education                             :string           default("")
#  email                                 :string           not null
#  ethnicity                             :integer
#  gender                                :string
#  geography_lead                        :boolean          default(FALSE)
#  glossary_list                         :string           default([]), is an Array
#  has_career_profile                    :boolean          default(FALSE), not null
#  hs_visitor_cache                      :jsonb
#  hubspot_marketing_email_name_received :string
#  identifier                            :string
#  import_data                           :jsonb
#  is_blocked                            :boolean          default(FALSE)
#  is_school_admin                       :boolean
#  job_hunter_interests                  :string
#  job_title                             :string
#  last_activity_at                      :datetime
#  last_sign_in_at                       :datetime
#  last_sign_out_at                      :datetime
#  last_sync_hubspot_activity_at         :datetime
#  lead_source                           :integer
#  linked_in_url                         :string           default("")
#  location                              :string
#  login_count                           :integer          default(0)
#  mailchimp_last_error                  :text
#  mailchimp_last_sync                   :datetime
#  mailchimp_last_sync_attempt           :datetime
#  mailchimp_list                        :string
#  mailchimp_sync_status                 :string           default("pending")
#  name                                  :string
#  onboarded                             :boolean
#  password_digest                       :string           not null
#  phone                                 :string
#  points                                :integer          default(0), not null
#  preferred_location                    :string
#  presentation_settings                 :jsonb
#  profile_color                         :jsonb
#  provider                              :string           not null
#  questionnaire_taken                   :boolean          default(FALSE)
#  recovery_requested_at                 :datetime
#  recovery_token                        :string
#  referral_actioned                     :boolean          default(FALSE)
#  referral_code                         :string
#  referral_share_name                   :boolean          default(TRUE), not null
#  require_password_reset                :boolean
#  science_lead                          :boolean          default(FALSE)
#  session_token                         :string
#  sign_in_token                         :string
#  specified_class                       :string
#  specified_year                        :string
#  tasks_completed_cache                 :jsonb
#  tokens                                :json
#  type                                  :string
#  uid                                   :string           default(""), not null
#  unique_wonde_identifier               :string
#  use_new_presentation_player           :boolean
#  work_experience                       :jsonb            is an Array
#  working_days                          :string
#  years_of_experience                   :string           default("")
#  created_at                            :datetime         not null
#  updated_at                            :datetime         not null
#  cv_fileboy_id                         :string
#  fileboy_image_id                      :string
#  hubspot_id                            :string
#  mailchimp_id                          :string
#  my_login_id                           :string
#  organisation_id                       :bigint
#  referred_from_user_id                 :integer
#  school_id                             :bigint
#  stripe_customer_id                    :string
#  white_label_organisation_id           :bigint
#  wonde_id                              :string
#
# Indexes
#
#  index_users_on_email                           (email) UNIQUE WHERE (NOT deleted)
#  index_users_on_identifier_and_school_id        (identifier,school_id) UNIQUE WHERE (NOT deleted)
#  index_users_on_organisation_id                 (organisation_id)
#  index_users_on_school_id                       (school_id)
#  index_users_on_sign_in_token                   (sign_in_token) UNIQUE
#  index_users_on_uid_and_provider                (uid,provider) UNIQUE WHERE (NOT deleted)
#  index_users_on_white_label_organisation_id     (white_label_organisation_id)
#  index_users_unique_wonde_id_where_not_deleted  (wonde_id) UNIQUE WHERE ((deleted = false) AND (wonde_id IS NOT NULL) AND ((wonde_id)::text <> ''::text))
#
# Foreign Keys
#
#  fk_rails_...  (referred_from_user_id => users.id)
#  fk_rails_...  (white_label_organisation_id => organisations.id)
#
require "rqrcode"

class Pupil < User
  scope :no_form, -> { where.not(id: Enrollment.select(:user_id)) }

  has_many :quip_quiz_results, dependent: :destroy, class_name: "QuipQuizResult"

  has_many :tracking_rocket_words, dependent: :destroy, class_name: "TrackingRocketWord", foreign_key: :pupil_id
  has_many :tracking_films, dependent: :destroy, class_name: "TrackingFilm", foreign_key: :pupil_id
  has_many :tracking_lesson_template_views, dependent: :destroy, class_name: "TrackingLessonTemplateViewed", foreign_key: :pupil_id
  has_many :tracking_word_searches, dependent: :destroy, class_name: "TrackingWordSearch", foreign_key: :pupil_id
  has_many :tracking_summative_quizzes, dependent: :destroy, class_name: "TrackingSummativeQuiz", foreign_key: :pupil_id
  has_many :tracking_link_trackings, dependent: :destroy, class_name: "TrackingLinkTracking", foreign_key: :pupil_id
  has_many :tracking_documents, dependent: :destroy, class_name: "TrackingDocument", foreign_key: :pupil_id
  has_many :tracking_lesson_template_favourites, dependent: :destroy, class_name: "TrackingLessonTemplateFavourite", foreign_key: :pupil_id
  has_many :tracking_logins, class_name: "TrackingLogin", dependent: :destroy, foreign_key: :user_id
  has_many :tracking_mark_assignments, dependent: :destroy, class_name: "TrackingMarkAssignment", foreign_key: :user_id
  has_many :active_days, dependent: :destroy, foreign_key: :user_id
  has_many :word_search_lobby_users, dependent: :destroy, foreign_key: :user_id
  has_many :tracking_account_edits, dependent: :destroy, foreign_key: :user_id
  has_many :pupil_unit_marks, foreign_key: :user_id, primary_key: :id
  has_many :pupil_lesson_marks, foreign_key: :user_id, primary_key: :id
  has_many :pupil_unit_notes, foreign_key: :user_id, primary_key: :id

  before_validation :auto_details, on: :create

  validates :identifier, uniqueness: {
    scope: :school_id,
    unless: :deleted?,
    conditions: -> { where(deleted: false) }
  }, presence: {
    conditions: -> { where(deleted: false) }
  }

  has_many :lesson_results, -> { current_school_year }, foreign_key: :pupil_id, class_name: 'Lesson::Result'
  has_many :all_lesson_results, dependent: :destroy, foreign_key: :pupil_id, class_name: 'Lesson::Result'

  has_many :teachers, through: :forms
  has_many :all_teachers, through: :all_forms, source: :teachers

  has_many :pupil_quip_attempts

  scope :home_school, -> { where(id: home) }
  scope :real_school, -> { where(id: real) }
  scope :school, -> { where.not(id: home) }

  scope :active, -> { where('last_activity_at >= ?', 6.months.ago) }

  def active_homework
    homeworks = homework.published.where("date_due >= ?", Date.today).left_joins(tasks: :submissions).group("homeworks.id")
    homeworks.select do |homework|
      complete_tasks = homework.tasks.select { |task| task.submissions.find_by(user_id: id)&.submitted_at&.present? }
      complete_tasks.length != homework.tasks.length
    end
  end

  def present_lesson_results
    lesson_results.with_data
  end

  after_create do
    next unless school
    school.complete_checklist_item(:create_pupils) unless school.setup_checklist[:create_pupils]
  end

  after_save do
    changes = {}
    %w[email name alias gender dob identifier avatar_configuration profile_color].map do |key|
      if saved_changes.keys.include?(key)
        changes[key] = { old: saved_changes[key][0], new: saved_changes[key][1] }
      end
    end
  end

  def graph_data
    lesson_results.with_data.pluck(:mark)
  end

  def self.real
    joins(:school).where('schools.school_type = ?', School.school_types[:generic])
  end

  def self.top
    order(points: :desc)
  end

  def ordered_lessons
    lessons.ordered
  end

  def auto_details
    self.identifier = (rand * 1_000_000).to_i if identifier.blank?
    self.identifier = identifier.parameterize
    self.email = "#{name&.parameterize || 'pupil'}#{identifier}@developingexperts.com" if email.blank?
    self.uid = email
    self.password ||= 'aoshd!@#!@#asdsdsdasduwqo!@#SDF23432password'
  end

  def quiz_results_for(lesson)
    quiz_attempts.find_by(lesson_template_id: lesson.template_id)
  end

  def quip_attempt_ids
    pupil_quip_attempts.pluck(:quip_attempt_id)
  end

  def next_reward
    Reward.all.ordered.detect { |reward| reward.points_required > points }
  end

  def next_reward_id
    next_reward&.id
  end

  def latest_quiz_results
    quiz_attempts.order(created_at: :desc).limit(7).pluck(:created_at, :score).reverse
  end

  def unlocked? id
    lessons.past.where(template_id: id).any?
  end

  def unlocked_lesson_ids
    lessons.past.pluck(:template_id)
  end

  def unlocked_lesson_lesson_ids
    lessons.past.ids # this was changed so that home school pupils are the same as school
  end

  def mark_for_lesson template_id
    return lesson_results.find_by(template_id: template_id)
  end

  def accessible_schools
    school
  end

  def accessible_teachers
    school&.teachers
  end

  def accessible_forms
    forms
  end

  def accessible_lesson_lessons
    lessons
  end

  def accessible_lesson_results
    lesson_results
  end

  def accessible_pupils
    id
  end

  def user_templates
    Lesson::Template.all.where(user_id: school.teacher_ids, user_generated: true)
  end

  def accessible_lesson_templates
    result = if school&.generic?
               Lesson::Template.all
             else
               Lesson::Template.anonymous_or_demo
             end

    result.available.or(user_templates)
  end

  def accessible_lesson_slides
    Lesson::Template.where(id: unlocked_lesson_ids).joins(:slides).select('lesson_slides.id')
  end

  def accessible_lesson_documents
    Lesson::Template.where(id: unlocked_lesson_ids).joins(:documents).select('lesson_documents.id')
  end

  def accessible_lesson_keywords
    Lesson::Keyword.where(template_id: accessible_lesson_templates)
  end

  def accessible_quiz_questions
    QuizOld::Question.where(lesson_template_id: accessible_lesson_templates)
  end

  def self.mark_book_data(pupil_ids, template_ids)
    load_sql("user/mark_book_data").strip_comments.compact.assign(
      user_filter_sql: sql("users.id IN (?)", pupil_ids),
      template_filter_sql: sql('lesson_templates.id IN (?)', template_ids),
      select_sql: sql(<<-SQL).compact,
        'pupils' AS record_type,
        user_templates.user_id AS record_id,
        user_templates.user_name AS record_name,
        JSONB_AGG(DISTINCT forms) AS collections
      SQL
      group_sql: sql('user_templates.user_id, user_templates.user_name'),
      order_sql: sql('user_templates.user_name'),
      row_count_sql: sql('template_id'),
    )
  end

  def v1_as_json(options = nil)
    as_json(options).merge(
      school: { name: school&.name, id: school&.id },
      form_ids: form_ids,
      lesson_ids: lesson_ids,
      classes: forms.pluck(:name),
      average_marks: self.average_marks,
      points: self.points,
    )
  end

  def qr_signin_link
    url = "#{Rails.application.config.x.frontend_url}/accounts/sign-in?" + {
      type: school.school_type, identifier: identifier, school: school.id, handle: "qr",
    }.to_param
    qrcode = RQRCode::QRCode.new(url)
    qrcode.as_svg(
      color: "000",
      shape_rendering: "crispEdges",
      module_size: 11,
      standalone: true,
      use_path: true,
      viewbox: true,
      svg_attributes: { height: 160, width: 160 }
    )
  end
end
