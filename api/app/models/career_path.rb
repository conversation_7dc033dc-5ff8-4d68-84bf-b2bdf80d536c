# == Schema Information
#
# Table name: career_paths
#
#  id                    :bigint           not null, primary key
#  age                   :integer
#  career_name           :string
#  career_path           :jsonb
#  education_level       :string
#  facts                 :string           default([]), is an Array
#  favourite             :boolean          default(FALSE)
#  flagged_reason        :text
#  generation_attempts   :integer
#  generation_key        :string
#  generation_started_at :datetime
#  info                  :json
#  is_saved              :boolean          default(FALSE)
#  qualifications        :jsonb            not null
#  status                :string           default("pending")
#  status_changed_at     :datetime
#  created_at            :datetime         not null
#  updated_at            :datetime         not null
#  career_id             :integer
#  job_family_id         :bigint
#  user_id               :bigint
#
# Indexes
#
#  index_career_paths_on_job_family_id       (job_family_id)
#  index_career_paths_on_name_age_education  (career_name,age,education_level)
#  index_career_paths_on_user_id             (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (job_family_id => job_families.id)
#  fk_rails_...  (user_id => users.id)
#
class CareerPath < ApplicationRecord
  belongs_to :career, optional: true
  belongs_to :job_family, optional: true

  has_many :audios, as: :audible

  has_many :tracking_career_paths, dependent: :destroy
  has_many :user_career_paths, dependent: :destroy
  has_many :career_path_logs, dependent: :destroy

  has_many :lesson_template_career_paths, dependent: :destroy, foreign_key: :career_path_id
  has_many :lesson_templates, through: :lesson_template_career_paths

  has_and_belongs_to_many :videos, join_table: :career_paths_videos

  has_many :flagged_images, as: :flaggable, dependent: :destroy

  before_create :set_status_changed_at
  before_update :update_status_changed_at, if: :will_save_change_to_status?

  validates :education_level, inclusion: { in: ['primary', 'secondary', 'post-16'], allow_nil: true }

  scope :primary, -> { where(education_level: 'primary') }
  scope :secondary, -> { where(education_level: 'secondary') }
  scope :post_16, -> { where(education_level: 'post-16') }
  scope :by_education_level, -> {
    order(
      Arel.sql(
        "CASE education_level " \
        "WHEN 'primary' THEN 1 " \
        "WHEN 'secondary' THEN 2 " \
        "WHEN 'post-16' THEN 3 " \
        "ELSE 4 END"
      )
    )
  }
  scope :v1_careers, -> { where(education_level: nil) }
  scope :v2_careers, -> { where.not(education_level: nil) }

  scope :completed, -> { where(status: 'completed') }
  scope :errored, -> { where(status: 'error') }

  def log(msg)
    Rails.logger.info "career path (" + id.to_s + "): " + msg
    career_path_logs.create(message: msg)
  end

  def self.find_or_create_for_generation(career_name:, age:, education_level: nil)
    record = find_by(career_name: career_name, education_level: education_level) if education_level.present?
    record = find_by(career_name: career_name, age: age) if record.nil? && age.present?
    record = create!(career_name: career_name, age: age, education_level: education_level) if record.nil?
    record
  end

  # Helper method to determine if this is a V2 career
  def v2_career?
    education_level.present?
  end
  
  # Helper method to get display name for education level
  def education_level_display
    case education_level
    when 'primary'
      'Primary (Ages 5-11)'
    when 'secondary'
      'Secondary (Ages 11-16)'
    when 'post-16'
      'Post-16 (Ages 16+)'
    else
      "Age #{age}"
    end
  end
  
  # Helper method to get short display name for education level
  def education_level_short
    case education_level
    when 'primary'
      'Primary'
    when 'secondary'
      'Secondary'
    when 'post-16'
      'Post-16'
    else
      "Age #{age}"
    end
  end

  def self.uniq_by_name
    # Use a hash to filter out duplicates by career_name only
    careers = {}
    all.each do |career|
      key = career.career_name
      careers[key] ||= career
    end
    where(id: careers.values.map(&:id))
  end

  def generate_qualifications
    result = CareerAIGeneratorService.new.generate_qualifications(career_name)
    update!(qualifications: result)
  end

  private

  def set_status_changed_at
    self.status_changed_at ||= Time.current
  end

  def update_status_changed_at
    self.status_changed_at = Time.current
  end
end
