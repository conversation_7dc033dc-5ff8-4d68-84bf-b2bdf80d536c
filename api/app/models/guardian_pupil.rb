# == Schema Information
#
# Table name: guardian_pupils
#
#  id          :bigint           not null, primary key
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  guardian_id :bigint
#  pupil_id    :bigint
#
# Indexes
#
#  index_guardian_pupils_on_guardian_id  (guardian_id)
#  index_guardian_pupils_on_pupil_id     (pupil_id)
#
# Foreign Keys
#
#  fk_rails_...  (guardian_id => users.id)
#  fk_rails_...  (pupil_id => users.id)
#
class GuardianPupil < ApplicationRecord
  # REMOVE
end
