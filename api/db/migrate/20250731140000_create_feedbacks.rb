class CreateFeedbacks < ActiveRecord::Migration[6.0]
  def change
    create_table :feedbacks do |t|
      t.text :message, null: false
      t.string :page_url, null: false
      t.string :user_agent
      t.string :fileboy_image_id
      t.references :user, null: true, foreign_key: true

      t.timestamps
    end

    add_index :feedbacks, :created_at
    add_index :feedbacks, :page_url
    add_index :feedbacks, :fileboy_image_id
  end
end
