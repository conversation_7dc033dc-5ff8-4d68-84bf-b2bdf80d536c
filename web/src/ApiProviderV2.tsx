import qs from "qs"
import React, { useContext } from "react"
import {
  IWhiteLabelContentTypes,
  IWhiteLabelStyleTypes,
} from "./components/Admin/Organisations/whiteLabelStyleTypes"
import { apiFetch } from "./components/Application/2019Comps/requestUtils"
import { IPresentationPublishedState } from "./components/Application/NewPresentations/constants"
import { INarrationCache } from "./components/Application/NewPresentations/Viewer/PresentationRenderAudio"
import { IBuildQuipQuizData } from "./components/Shared/UsersLessonTemplates/QuizBuilder"
import { IResetOptions } from "./components/Shared/UsersLessonTemplates/UsersLessonTemplatesEditPage"
import { QuestionTypes } from "./components/SignUpQuestionnaire/questionData"

import { IV2QuipQuestion } from "./quip/components/quipQuestionTypes"
import { ILibraryUnit, IMotd, IPresentationQuizQuestion, IPupil } from "./types/apiRecordTypes"
import { toFormData } from "./utils/formData"
import { QuizJson } from "./components/Admin/DeQuiz/QuizEditor"

export type IApiReturn<T extends (...args: any) => any> = ReturnType<T> extends Promise<infer U>
  ? U
  : T

export interface Iv2User {
  id: Id
  uid: string
  created_at: string
  updated_at: string
  last_sign_in_at: null | string
  require_password_reset: null
  name: string
  email: string
  is_school_admin: boolean
  dob: null | string
  school_id: number
  working_days: null
  alias: null
  identifier: null
  specified_class: null
  specified_year: null
  home: boolean
  distributor_id: null
  average_marks_cache: Record<string, any>
  image_cache: Record<string, any>
  pupil_import_id: null
  cord_cache: Record<string, any>
  last_activity_at: null | string
  last_sign_out_at: null | string
  last_modified_by: null | string
  last_modified_at: null | string
  job_title: null
  sign_in_token: null
  login_count: number
  lead_source: null
  deleted: boolean
  points: number
  fileboy_image_id: string | null
  gender?: null | string
  ethnicity?: null | string
  user_type: string
  preferred_roles: string[]
  preferred_location: string
  years_of_experience: string
  preferred_career_ids: Id[]
  interest_tags: string[]
  work_experience: {
    employer: string
    jobTitle: string
    startDate: string | null
    endDate: string | null
  }[]
  education: string
  linked_in_url: string
  cv_fileboy_id?: string | null
  avatar_configuration: Record<string, any>
  profile_color: Record<string, any>
  has_career_profile?: boolean
  job_hunter_interests?: string
  password?: string
  organisation_id?: Id
  white_label_organisation_id?: Id
  questionnaire_taken?: boolean
  referral_actioned?: boolean
  is_blocked: boolean
}

export interface Iv2Teacher extends Iv2User {
  id: Id
}

export interface Iv2TeacherTaskProgress {
  profile: boolean
  invites: boolean
  class: boolean
  pupils: boolean
  course: boolean
  marks: boolean
  import: boolean
  library: boolean
  referral: boolean
}

export interface Iv2Pupil extends Iv2User {
  id: Id
}

export interface Iv2Guardian extends Iv2User {
  pupil_ids: Id[]
}

export interface Iv2School {
  id: Id
  name: string
  school_type: string
  finance_email?: string
  finance_name?: string
  annual_subscription?: boolean
  subscription_externally_covered?: boolean
  po_number?: string
  country_id: Id
  uk_school_id: Id
  wonde_id: Id
  wonde_request_status: "not_requested" | "pending" | "active" | "declined"
  wonde_enabled: boolean
  fileboy_image_id?: string
  postcode: string
  organisation_id: Id
  show_lesson_plan_pupil_area: boolean
  trial_end_date: string
  admin_notes?: string | null
  hubspot_errors: {
    source: string
    message: string
    data: Record<string, any>
    user: string
    timestamp: string
  }[]
}

export interface Iv2Curricula {
  id: Id
  name: string
  weight: number
  created_at: string
  updated_at: string
  fileboy_image_id: string
}

export interface Iv2Year {
  id: Id
  name: string
  curriculum_id: Id
  fileboy_image_id: string
}

export interface Iv2Unit {
  id: Id
  name: string
  weight: number
  summary: string
  fileboy_image_id: string
  fileboy_video_id: string
  year_id: Id
  jw_title?: string
  jw_body?: string
  jw_id?: string
  details?: string
  documents: { id: Id; name: string; fileboy_file_id: string }[]
  year?: { id: Id; name: string }
  curriculum?: { id: Id; name: string }
  campaignUnitIds: Id[]
  displayCampaignUnitIds: Id[]
  lessonTemplateIds: Id[]
  exemplar_works: IExemplarWork[]
  sponsors?: { id: Id; name: string; fileboy_image_id: string }[]
  video_id?: Id
}

export interface Iv2Form {
  id: Id
  name: string
  school_id: Id
}

export interface Iv2TrackingRocketWord {
  id: Id
  time_taken: number
  score: number
  answers_json: Record<string, any>
  lesson_id: Id
  lesson_template_id: Id
  pupil_id: Id
  created_at: string
  updated_at: string
}

export interface Iv2TrackingFilms {
  id: Id
  time_viewing: number
  film_type: string
  jw_id: string
  video_resource_type: string
  video_resource_id: Id
  lesson_id: Id
  lesson_template_id: Id
  pupil_id: Id
  created_at: string
  updated_at: string
  film_duration: number
}

export interface Iv2TrackingWordSearch {
  id: Id
  time_taken: number
  lesson_id: Id
  lesson_template_id: Id
  pupil_id: Id
  created_at: string
  updated_at: string
}

export interface Iv2TrackingSummativeQuiz {
  id: Id
  time_taken: number
  score: number
  answers_json: Record<string, any>
  noodle_quiz_id: string
  lesson_id: Id
  lesson_template_id: Id
  pupil_id: Id
  created_at: string
  updated_at: string
  total_score: number
}

export interface Iv2TrackingLinkTrackings {
  id: Id
  url: string
  link_type: string
  lesson_id: Id
  lesson_template_id: Id
  pupil_id: Id
  created_at: string
  updated_at: string
}

export interface Iv2TrackingDocuments {
  id: Id
  document_type: string
  lesson_id: Id
  lesson_template_id: Id
  pupil_id: Id
  created_at: string
  updated_at: string
  document_id: Id
}

export interface Iv2TrackingLessonTemplateViews {
  id: Id
  lesson_id: Id
  lesson_template_id: Id
  pupil_id: Id
  created_at: string
  updated_at: string
}

export interface Iv2TrackingPresentationViews {
  id: Id
  lesson_id: Id | null
  lesson_template_id: Id
  user_id: Id
  time_viewed: number
  created_at: string
  updated_at: string
}

export interface Iv2TrackingLessonTemplateFavourites {
  id: Id
  lesson_id: Id
  lesson_template_id: Id
  pupil_id: Id
  created_at: string
  updated_at: string
}

export interface Iv2LessonTemplatePlan {
  intent: string
  new_lesson_plan_resources: string
  implementation: string
  impact_assessment: string

  specification?: string
  ks4_learning_outcomes?: string
  core_knowledge?: string
  analogies_models?: string
  ks4_teacher_mastery?: string
}

export interface Iv2Keyword {
  id: Id
  fileboy_image_id: string
  name: string
  body: string
  lesson_slide_id?: Id | null
  quiz_question?: Record<string, any>
}

export interface Iv2Document {
  id: Id
  fileboy_file_id: string
  name: string
  for_pupil?: boolean
}

export interface Iv2LessonTemplate extends Iv2LessonTemplatePlan {
  id: Id
  name: string
  machine_name: string
  image_uid: string
  image_name: string
  resources: string
  sentence_structure: string
  activities: string
  assessment: string
  learning_outcomes: string
  teacher_mastery: string
  pupil_mastery: string
  risk_assessment: null
  enquiry_types: []
  created_at: string
  updated_at: string
  pupils_awaiting_marks_count_cache: {}
  anonymous: boolean
  secondary_unit_id: Id
  tertiary_unit_id: null
  image_cache: {
    small: string
    thumbnail: string
  }
  last_modified_by: null
  last_modified_at: null
  objectives: string[]
  previous_rocket_word_narration: null
  deleted: boolean
  available: boolean
  use_new: boolean
  quip_quiz_id: Id
  lesson_check_summative_quiz: boolean
  admin_checklist: []
  demo: boolean
  weight: number
  sponsor_logo_uid: null
  sponsor_logo_name: null
  sponsor_logo_cache: {}
  film_link: null
  employer_link: null
  post_16_link: null
  film_jw_id: null
  employer_jw_id: null
  post_16_jw_id: null
  group_id: Id
  country_id: Id
  awaiting_translation: boolean
  content_uploaded: boolean
  quip_quiz_key: string
  uploaded_old_quiz: boolean
  fileboy_image_id: string
  fileboy_sponsor_logo_id: string | null
  fileboy_video_film_id: string | null
  use_2022_lesson_plan: boolean
  use_new_presentation: boolean
  author_ids: Id[]
  year?: { id: string; name: string; curriculum_id: Id }
  user_generated?: boolean
  source_template_id?: Id
  recommended_career_ids: Id[]
  user_id?: Id | null
  keywords: {
    id: Id
    fileboy_image_id: string
    name: string
    body: string
    lesson_slide_id?: Id | null
  }[]
  presentation_rating: null | { avg: number; count: number }
  scientific_enquiry_types: Iv2ScientificEnquiryType[]

  specification?: string
  ks4_learning_outcomes?: string
  core_knowledge?: string
  analogies_models?: string
  ks4_teacher_mastery?: string

  is_ks4_lesson_plan?: boolean
  lesson_plan_layout: ILessonPlanLayout
}

export type ILessonPlanLayout = "legacy" | "ks1_3" | "ks4"

export type Iv2LessonTemplateShow = Iv2LessonTemplate & {
  unit_ids: Id[]
  documents: { id: Id; name: string; fileboy_file_id: string; for_pupil: boolean }[]
  keywords: Iv2Keyword[]
  primary_unit?: Iv2Unit
  scientific_enquiry_types: Iv2ScientificEnquiryType[]
  expert_slide?: {
    jw_id: string
    fileboy_video_id: string
    id: number
    video_id?: number
    video_url?: string
  }
  mission_assignment_slide?: {
    jw_id: string
    fileboy_video_id: string
    id: number
    video_id?: number
    video_url?: string
  }
  authors: {
    id: Id
    title: string
    name: string
    body: string
    fileboy_image_id: string
  }[]
  plan_activities: {
    name: string
    method: string
    resources: string
    required_resources?: boolean | undefined
  }[]
  plan_learning_outcomes: { higher: string; middle: string; lower: string; body: string }[]
  plan_assessment_marks?: boolean
  plan_assessment_phrases?: boolean
  plan_assessment_questions?: boolean
  plan_cbse?: boolean
  plan_century_skills_for_life?: boolean
  plan_chinese_compulsory_education_primary_school_science?: boolean
  plan_cross_curriculum_opportunities?: boolean
  plan_curriculum_of_excellence?: boolean
  plan_early_years_framework?: boolean
  plan_international_baccalaureate?: boolean
  plan_kingdom_of_saudi_arabia?: boolean
  plan_national_curriculum?: boolean
  plan_next_generation_science_standards?: boolean
  plan_scientific_enquiry_type?: boolean
  plan_working_scientifically_skills?: boolean
  slide_count: number
  film_fileboy_video_id: string
  employer_fileboy_video_id: string
  post_16_fileboy_video_id: string
  film_video_id?: Id
  employer_video_id?: Id
  post16_video_id?: Id
  recommended_template_ids: Id[]
  recommended_career_ids: Id[]
  exemplar_works: IExemplarWork[]
  is_published?: boolean
  published_state?: any
  user_generated: boolean
  is_default: boolean
  viewable_only: boolean
  creator: string
}
export interface Iv2LessonTemplateUserData {
  lesson_override_id?: Id | null
  best_word_search_result?: number | null
  best_result_for_lesson_current_user?: number | null
  new_presentation_id?: Id | null
}
export type Iv2LessonTemplateWithUser = Iv2LessonTemplateShow & Iv2LessonTemplateUserData

export interface Iv2LessonLesson {
  id: Id
  active: boolean
  created_at: string
  form_id: Id
  template_id: Id
  time: string
  updated_at: string
  template: Iv2LessonTemplate
  lesson_taught: boolean
}

export interface IWordSearchLobby {
  id: Id
  users: {
    id: Id
    user_id: Id
    name: string
    score: number
    words: string[]
    finishedAt: string | null
    gameName: string
  }[]
  startsAt: string
}
export interface IWordSearchLobbyIndex {
  id: Id
  startsAt: string
}

export interface Iv2IndustryTag {
  id: Id
  name: string
  description: string
  lesson_template_ids: Id[]
}

export interface Iv2Sponsor {
  id: Id
  name: string
  fileboy_image_id: string
  body: string
  school_ids: Id[]
}

export interface Iv2JobListing {
  id: Id
  title: string
  length: string
  end_date: string
  body: string
  salary: string
  created_at: string
  updated_at: string
  deleted: boolean
  pdf_uid: string
  pdf_name: string
  application_form_uid: string
  application_form_name: string
  fileboy_pdf_id: string
  fileboy_application_form_id: string
}

export interface Iv2Career {
  id: Id
  name: string
  body: string
  fileboy_image_id: string
  video_id: Id
  video?: Iv2Video
  training_routes: string
  competencies_technical: string
  competencies_non_technical: string
  work_location: string
  family: string
  qualifications: string
  salary_from: string
  salary_to: string
  definition: string
  sectors: string
  keywords: string
  training_route_ids: Id[]
  career_tags: { id: Id; name: string }[]
  created_at: string
  tour_id?: Id
  tour?: { name: string; id: Id }
  tours?: { name: string; id: Id }[]
}

export interface Iv2TrainingRoute {
  id: Id
  name: string
  qualifications: string
  career_id: Id
  route_type: string
}

export interface Iv2UkSchool {
  id: Id
  name: string
  phone: string
  postcode: string
  county: string
  created_at: string
  updated_at: string
  category: string
  phase_of_education: string
  number_of_pupils: number
}

export interface Iv2Achievement {
  id: Id
  name: string
  body: string
  fileboy_image_id: string
  achievement_type: string
  target: number
  points: number
}

export interface Iv2PupilAchievement extends Iv2Achievement {
  progress: number
  completed_at: string | null
}

export interface Iv2Accreditation {
  id: Id
  name: string
  fileboy_image_id: string
  weight: number
  created_at: string
}

export interface Iv2CareerTag {
  id: Id
  name: string
}

export interface Iv2CareerTagWithCounts extends Iv2CareerTag {
  career_count: number
  lesson_template_count: number
}

export interface Iv2TrackingLogin {
  id: Id
  created_at: string
  updated_at: string
}

export interface Iv2TrackingAccountEdit {
  id: Id
  user_id: Id
  changed_data_json: Record<string, any>
  created_at: string
  updated_at: string
}

export interface Iv2TrackingMarkAssignment {
  id: Id
  lesson_template_id: Id
  user_id: Id
  mark: number
  comments: string
  created_at: string
  updated_at: string
}

export interface Iv2LessonShow {
  lesson: Iv2LessonLesson
  template: Iv2LessonTemplate
  form: Iv2Form
  progress: ILessonProgress
  homeworks: (IHomework & { tasks: (IHomeworkTask & { submission?: IHomeworkTaskSubmission })[] })[]
}

export interface ILessonProgress {
  presentation_viewed: boolean
  rocket_word_quiz: boolean
  summative_quiz: boolean
  tracking_word_search: boolean
}

export interface Iv2Invite {
  id: Id
  email: string
  pupil_id: Id
  pupil: { id: Id; name: string }
  inviteCreator: { id: Id; name: string }
  user_type: string
  created_at: string
  updated_at: string
}

export interface Iv2InviteRequest {
  id: Id
  pupil_id: Id
  guardian_id: Id | null
  name: string
  email: string
  school: { id: Id; name: string }
  pupil: { id: Id; identifier: string }
  created_at: string
  updated_at: string
}

export interface Iv2RocketWordsQuizQuestion {
  type: string
  slide_id: number
  key: string | number
  prompt: string
  validResponse: string | number
  options: {
    label: string
    value: string | number
    imageSrc?: string
  }[]
  question_body: string
  question_video_url: string
  question_fileboy_image_id: string
}

export interface Iv2LessonSlide {
  id: Id
  body: string
  jw_id?: string
  fileboy_image_id: string
  template_id: Id
  slide_type: string
  name: string
  templateName: string
  end_of_unit_assessment?: boolean
  quip_question_id?: Id
  video_id: Id
}

export type Iv2TrackingDataType =
  | "tracking_login"
  | "tracking_rocket_word"
  | "tracking_film"
  | "tracking_word_search"
  | "tracking_quiz"
  | "tracking_link"
  | "tracking_document"
  | "tracking_template_view"
  | "tracking_favourite"
  | "tracking_presentation_view"
  | "tracking_account_edit"
  | "tracking_mark_assignment"

export interface Iv2UkSchool {
  id: Id
  name: string
  phone: string
  postcode: string
  county: string
  created_at: string
  updated_at: string
  local_authority: string
}

export interface Iv2TeacherStats {
  id: Id
  email: string
  name: string
  created_at: string
  lessons_marked: number
  teachers: number
  my_forms: number
  all_forms: number
  my_pupils: number
  all_pupils: number
  login_streak: number
  presentation_seconds: number
  lessons_taken: number
  forms_with_courses: number
  referrals: number
}

export interface Iv2SchoolStats {
  id: Id
  name: string
  created_at: string
  teachers_count: number
  forms_count: number
  pupils_count: number
}
export interface Iv2SchoolStatsAdmin {
  id: Id
  activeTeachersToday: number
  activeTeachersThisWeek: number
  activeTeachersThisMonth: number

  activePupilsToday: number
  activePupilsThisWeek: number
  activePupilsThisMonth: number

  presentationViewsToday: number
  presentationViewsThisWeek: number
  presentationViewsThisMonth: number

  rocketWordViewsToday: number
  rocketWordViewsThisWeek: number
  rocketWordViewsThisMonth: number

  filmViewsToday: number
  filmViewsThisWeek: number
  filmViewsThisMonth: number

  wordSearchesToday: number
  wordSearchesThisWeek: number
  wordSearchesThisMonth: number

  lessonTemplateViewsToday: number
  lessonTemplateViewsThisWeek: number
  lessonTemplateViewsThisMonth: number

  summativeQuizzesViewsToday: number
  summativeQuizzesViewsThisWeek: number
  summativeQuizzesViewsThisMonth: number
}

export type IRegionChoices =
  | "greater_london"
  | "south_east"
  | "south_west"
  | "west_midlands"
  | "north_west"
  | "north_east"
  | "yorkshire_and_the_humber"
  | "east_midlands"
  | "east_of_england"
  | "scotland"
  | "wales"
  | "northern_ireland"

interface IRankingParams {
  page?: number
  query?: string
  countryId?: Id
  region?: IRegionChoices
  schoolId?: Id
  classId?: Id
  from?: Date | null
  to?: Date | null
}

export interface Iv2Organisation {
  id: Id
  name: string
  body?: string
  organisation_type?: string
  establishment_type?: string
  uid?: number
  establishment_number?: number
  establishment_name?: string
  alias?: string
  lat?: number
  lng?: number
  address_line_1?: string
  address_line_2?: string
  address_line_3?: string
  town?: string
  postcode?: string
  county?: string
  website?: string
  phone_number?: string
  fax?: string
  open_date?: string
  education_range?: string
  head_teacher_name?: string
  for_gender?: string
  gtr_id?: number
  hesa_id?: number
  published: boolean
  fileboy_image_id: string
  career_course_ids: Id[]
  career_vacancy_ids: Id[]
  event_ids: Id[]
  featured: boolean
  promotional_video_id?: Id
  testimonial_video_id?: Id
  promotionalVideo?: Iv2Video
  testimonialVideo?: Iv2Video
  use_white_labelling: boolean
  white_label_style: IWhiteLabelStyleTypes
  white_label_content: IWhiteLabelContentTypes
  school_ids: Id[]
  white_label_user_ids: Id[]
  testimonial: string
  testimonial_by: string
  expert_video_id?: Id
  expert_video?: Iv2Video
}

export interface Iv2Event {
  id: Id
  name: string
  fileboy_image_id: string
  summary: string
  body: string
  start_date: string
  end_date: string
  organisation_id: Id
  organisationName: string
  published: boolean
  views: number
  externalClicks: number
  video_id: Id
  video?: Iv2Video
  external_url: string
}

export interface Iv2CareerVacancy {
  id: Id
  name: string
  body: string
  description: string
  category: string
  organisation_id: string
  contract_type: string
  contract_time: string
  lat: number
  lng: number
  salary_min: number
  salary_max: number
  location: string
  origin: string
  published: boolean
  fileboy_image_id: string
  organisationName: string
  created_at: string
  career_tags: { id: Id; name: string }[]
  views: number
  externalClicks: number
  video_id: Id
  video?: Iv2Video
  external_url: string
}

export interface Iv2CareerCourse {
  id: Id
  name: string
  body: string
  description: string
  start_date: string
  length: string
  organisation_id: string
  location: string
  published: boolean
  fileboy_image_id: string
  organisationName: string
  video_url?: string
  campaignCourseId?: Id
  video_id: Id
  video?: Iv2Video
  external_url: string
  views: number
  externalClicks: number
}

export interface Iv2Author {
  [key: string]: any

  id: Id
  name: string
  title: string
  body: string
  fileboy_image_id: string
}

export type Iv2CareerFeedItem = {
  id: null
  common_words: string[]
  taggable_id: Id
} & (
  | { taggable_type?: "Career"; taggable?: Iv2Career }
  | { taggable_type?: "CareerCourse"; taggable?: Iv2CareerCourse }
  | { taggable_type?: "CareerVacancy"; taggable?: Iv2CareerVacancy }
  | { taggable_type?: "Event"; taggable?: Iv2Event }
)

export interface Iv2Country {
  name: string
  id: Id
}

export interface Iv2Campaign {
  id: Id
  name: string
  employment_year: number | null
  organisation_id: Id
  event_ids: Id[]
  interest_tags: string[]
  location: { radiusMiles: number; postcode: string }[] | null
  ethnicities: string[]
  gender: "men" | "women" | "any"
  updated_at: string
  created_at: string
  contract_expiry_date: string
  contact_name: string
  contact_number: string
  contact_email: string
  organisation: Iv2Organisation
  number_of_units: number
  campaign_page_link: string
}

export interface Iv2Video {
  id: Id
  fileboyVideoId?: string
  campaign_id: Id
  name: string
  campaign: {
    name: string
    id: Id
  } | null
  views: number
  video_url?: string
  tour_id?: Id
  tour_scene?: string
}

export interface Iv2VideoViews {
  id: Id
  video?: { name: string; id: Id; fileboyVideoId?: string; video_url?: string } | null
  time_viewing: number
  user_id?: Id
  created_at: string
  gender: string
}

export interface Iv2CampaignUnitView {
  id: Id
  campaign: { name: string; id: Id }
  campaign_unit_id: Id
  user_id?: Id
  created_at: string
  newLibraryUnits: { name: string; id: Id }[]
  gender: string
}

export interface Iv2CampaignUnitExternalClick {
  id: Id
  campaign: { name: string; id: Id }
  campaign_unit_id: Id
  user_id?: Id
  created_at: string
  newLibraryUnits: { name: string; id: Id }[]
  gender: string
}

export interface Iv2CampaignLessonView {
  id: Id
  campaign: { name: string; id: Id }
  lessonTemplate: { name: string; id: Id }
  campaign_lesson_id: Id
  user_id?: Id
  created_at: string
  gender: string
}

export interface Iv2CampaignLessonExternalClick {
  id: Id
  campaign: { name: string; id: Id }
  lessonTemplate: { name: string; id: Id }
  campaign_lesson_id: Id
  user_id?: Id
  created_at: string
  gender: string
}

export interface Iv2CampaignUnit {
  id: Id
  external_url: string
  videos2_id: Id
  video?: { name: string; id: Id; fileboyVideoId?: string; video_url?: string } | null
  videoViews?: number
  newLibraryUnits?: { name: string; id: string }[]
  quiz_id?: Id | null
  quiz?: { name: string; noodle_quiz_key: string; id: Id }
  quizViews?: number
  campaign_id: Id
  campaign: { name: string; id: Id; campaign_page_link: string }
  new_library_unit_ids: Id[]
  views: number
  externalClicks: number
  organisation_id: Id
  body: string
  tour?: { name: string; id: Id }
  tourViews: number
  campaignLessonIds: Id[]
  show_on_unit_page: boolean
}

export interface Iv2CampaignLesson {
  id: Id
  video_id: Id
  user_id: Id
  campaign_id: Id
  lesson_template_id: Id
  campaign: { name: string; id: Id }
  lessonTemplate: { name: string; id: Id; machine_name: string }
  video?: { name: string; id: Id; fileboyVideoId?: string; video_url?: string } | null
  external_url: string
}

export interface Iv2CampaignCourseView {
  id: Id
  careerCourse: { name: string; id: string }
  created_at: string
  gender: string
}

export interface Iv2CampaignCourseExternalClick {
  id: Id
  careerCourse: { name: string; id: string }
  created_at: string
  gender: string
}

export interface Iv2CampaignEventView {
  id: Id
  event_id: Id
  event: { name: string; id: Id }
  created_at: string
  gender: string
}

export interface Iv2CampaignEventExternalClick {
  id: Id
  event_id: Id
  event: { name: string; id: Id }
  created_at: string
  gender: string
}

export interface Iv2CampaignVacancyView {
  id: Id
  careerVacancyId: Id
  careerVacancy: { name: string; id: Id }
  created_at: string
  gender: string
}

export interface Iv2CampaignVacancyExternalClick {
  id: Id
  careerVacancyId: Id
  careerVacancy: { name: string; id: Id }
  created_at: string
  gender: string
}

export interface Iv2Quiz {
  id: Id
  campaignUnitIds: Id[]
  campaign: null | { name: string; id: Id }
  views: number
  completions: number
  name: string
  noodle_quiz_key: string
}

export interface Iv2QuizView {
  id: Id
  user_id: Id
  quiz: { id: Id; name: string }
  created_at: string
  gender: string
}

export interface Iv2Tour {
  id: Id
  name: string
  fileboy_image_id: string
  data: Record<string, any>
  campaign_id: Id | null
  organisation?: { name: string; id: Id }
  campaign?: { name: string; id: Id } | null
  views: number
  created_at: string
  campaignUnitIds: Id[]
  careerIds: Id[]
  available_to_pupils?: boolean
}

export interface Iv2TourView {
  id: Id
  created_at: string
  tour: { name: string; id: Id }
  gender: string
}

export interface Iv2VacancyImport {
  id: Id
  fileboy_id: string
  status: "pending" | "processing" | "complete" | "error"
  data: {
    // imported attrs
    id: number
    name: string
    body: string
    description: string
    category: string
    salary_min: number
    salary_max: number
    location: string
    postcode: string
    contract_type: string
    contract_time: string
    external_url: string
    image_url: string
    closing_date: string
    published: boolean
    organisation_name: string
    ////////////////////////////////////
    // meta attrs
    _record_id: string // looked up from id; if not presnet; will be new record
    _organisation_id: string // looked up from name or assoc; if not present will be new org
    _lat: number // looked up from postcode
    _lng: number // looked up from postcode
    _import_valid: boolean // will be imported
    _import_validation_errors: Record<string, string> // key/string for errors
    _import_status: "pending" | "complete" | "error"
    _import_error_message: string // if it completelye failed (ruby error or something)
  }[]
  created_at: string
}
export interface Iv2WondeImport {
  id: Id
  success: boolean
  created_at: string
  import_status: "running" | "complete" | "imported" | "importing" | "cancelled"
  formCount: number
  teacherCount: number
  pupilCount: number
  wonde_pupils_count: number
  wonde_teachers_count: number
  wonde_forms_count: number
  error?: IWondeImportError
  raw_wonde_cache?: Record<string, any>[]
}

interface IWondeError {
  fatal?: string[]
  teachers: string[]
  pupils: string[]
  forms: string[]
  raw?: string
}
export interface IWondeImportError {
  error_data: IWondeError
  warning_data: IWondeError
  wonde_import_id: Id
  created_at: string
  updated_at: string
}

interface IWondeDate {
  date: string
  timezone: string
  timezone_type: number
}

export interface Iv2WondeImportPupil {
  id: Id
  wonde_id: string
  pupil?: Iv2Pupil
  name: string
  forms: { id: Id; name: string }[]
  real_forms: { id: Id; name: string }[]
  wonde_data: {
    id: string
    upi: string
    gender: string //"FEMALE",
    mis_id: string
    surname: string
    forename: string
    initials: string
    created_at: IWondeDate
    updated_at: IWondeDate
    middle_names: null
    date_of_birth: IWondeDate
    legal_surname: string
    legal_forename: string
    extended_details: {
      data: Record<string, any>
    }
  }
}

export interface Iv2WondeImportTeacher {
  id: Id
  wonde_id: string
  teacher?: Iv2Teacher
  name: string
  email: string
  forms: { id: Id; name: string }[]
  real_forms: { id: Id; name: string }[]
  wonde_data: {
    id: string
    upi: string
    meta: {
      role: string //"Main Class Teacher"
      all_roles: string[]
      is_main_teacher: true
      is_class_teacher: true
    }
    title: string
    gender: string //"MALE"
    mis_id: string
    surname: string
    forename: string
    initials: string
    created_at: IWondeDate
    updated_at: IWondeDate
    middle_names: null
    date_of_birth: IWondeDate
    legal_surname: string
    legal_forename: string
    contact_details: {
      data: {
        emails: {
          home: string
          work: null
          email: string
          primary: string
        }
        phones: {
          home: null
          work: null
          phone: null
          mobile: null
          primary: null
        }
        addresses: {
          home: {
            town: null
            county: null
            street: null
            country: null
            district: null
            postcode: null
            apartment: null
            house_name: null
            house_number: null
          }
          work: {
            town: null
            county: null
            street: null
            country: null
            district: null
            postcode: null
            apartment: null
            house_name: null
            house_number: null
          }
          postal: {
            town: null
            county: null
            street: null
            country: null
            district: null
            postcode: null
            apartment: null
            house_name: null
            house_number: null
          }
        }
        salutation: null
      }
    }
  }
}

export interface Iv2WondeImportForm {
  id: Id
  wonde_id: string
  form?: Iv2Form
  teacher_ids: Id[]
  pupil_ids: Id[]
  name: string
  wonde_data: {
    id: string
    code: null
    name: string
    mis_id: string
    subject: string
    created_at: IWondeDate
    updated_at: IWondeDate
    alternative: null
    description: string
  }
}

export interface IHubspotSyncRecord {
  id: Id
  subscribed_count: number
  unsubscribed_count: number
  number_of_schools: number
  created_at: string
}

export interface IV2PupilUnitMark {
  pupil_id: Id
  unit_id: Id
  mark: string
}

export interface Iv2SchoolTeacherEngagementStat {
  id: string
  name: string
  created_at: string
  hubspot_subscription_status: string
  teachers: {
    id: string
    name: string
    email: string
    login_count: number
    created_at: string
    last_activity_at: string
  }[]
}

export interface ISchoolExtendedIndexStats {
  admin_teachers: {
    id: Id
    name: string
    email: string
  }[]
  active_teachers_30: number | null
  active_teachers_30_percent: number | null
  active_teachers_60: number | null
  active_teachers_60_percent: number | null
}

export interface ILessonPlanViewData {
  id: Id
  name: string
  machine_name: string
  view_count: number
  unique_view_count: number
}

export interface Iv2Presentation {
  id: Id
  name: string
  user_id: Id
  created_at: string
  updated_at: string
  published: keyof typeof IPresentationPublishedState
  lesson_template_id?: Id
  slides: {
    id: Id
    type: string
    data: unknown
    weight: number
    created_at: string
    updated_at: string
  }[]
  settings: {
    font?: string
    backgroundColor?: string
  }
}

export interface Iv2PresentationFeedback {
  id: Id
  user_id?: Id
  new_presentation_id: Id
  created_at: string
  updated_at: string
  rating: number
  feedback: string
}

export interface ISignUpEvent {
  id: Id
  name: string
  published: boolean
}

export type IExemplarWorkStatus = "pending" | "approved" | "rejected"
export interface IExemplarWork {
  id: string
  lesson_template_id: Id
  template: { id: Id; name: string } | null
  new_library_unit_id: Id
  unit: { id: Id; name: string } | null
  user_id: Id
  submitted_by: { id: Id; name: string } | null
  approved_by_id: Id | null
  approved_by: { id: Id; name: string } | null
  fileboy_id: string
  status: IExemplarWorkStatus
  title: string
  display_name: string
  body: string
}
interface ITwitterMedia {
  id: number
  id_str: string
  indices: number[]
  media_url: string //"http://pbs.twimg.com/tweet_video_thumb/FivrhcFWQAE4U4-.jpg"
  media_url_https: string //"https://pbs.twimg.com/tweet_video_thumb/FivrhcFWQAE4U4-.jpg"
  url: string //"https://t.co/rtw5rxHTUN"
  display_url: string //"pic.twitter.com/rtw5rxHTUN"
  expanded_url: string //"https://twitter.com/DevelopExperts/status/1597630036974374912/photo/1"
  type: "photo" | "animated_gif"
  sizes: {
    thumb: {
      w: 150
      h: 150
      resize: "crop"
    }
    large: {
      w: 700
      h: 394
      resize: "fit"
    }
    small: {
      w: 680
      h: 383
      resize: "fit"
    }
    medium: {
      w: 700
      h: 394
      resize: "fit"
    }
  }
  video_info?: {
    aspect_ratio: [350, 197]
    variants: [
      {
        bitrate: 0
        content_type: "video/mp4"
        url: "https://video.twimg.com/tweet_video/FivrhcFWQAE4U4-.mp4"
      },
    ]
  }
}

interface ITwitterData {
  created_at: string
  id: number
  id_str: string
  full_text: string
  display_text_range: number[]
  truncated: boolean
  entities: {
    hashtags: any[]
    symbols: any[]
    user_mentions: {
      screen_name: string
      name: string
      id: number
      id_str: string
      indices: number[]
    }[]
    urls?: {
      url: string
      expanded_url: string
      display_url: string
      indices: number[]
    }[]
    media?: ITwitterMedia[]
  }
  retweeted_status?: ITwitterData
  extended_entities?: {
    media: ITwitterMedia[]
  }
  source: string
  in_reply_to_status_id: null | number
  in_reply_to_status_id_str: null | string
  in_reply_to_user_id: null | number
  in_reply_to_user_id_str: null | string
  in_reply_to_screen_name: null | string
  user: {
    id: number
    id_str: string
    name: string
    screen_name: string
    location: string
    description: string
    url: string
    entities: {
      url: {
        urls: {
          url: string
          expanded_url: string
          display_url: string
          indices: number[]
        }[]
      }
      description: {
        urls: any[]
      }
    }
    protected: boolean
    followers_count: number
    friends_count: number
    listed_count: number
    created_at: string
    favourites_count: number
    utc_offset: null
    time_zone: null
    geo_enabled: boolean
    verified: boolean
    statuses_count: number
    lang: null
    contributors_enabled: boolean
    is_translator: boolean
    is_translation_enabled: boolean
    profile_background_color: string
    profile_background_image_url: string
    profile_background_image_url_https: string
    profile_background_tile: boolean
    profile_image_url: string
    profile_image_url_https: string
    profile_banner_url: string
    profile_link_color: string
    profile_sidebar_border_color: string
    profile_sidebar_fill_color: string
    profile_text_color: string
    profile_use_background_image: boolean
    has_extended_profile: boolean
    default_profile: boolean
    default_profile_image: boolean
    following: null
    follow_request_sent: null
    notifications: null
    translator_type: string
  }
  geo: null
  coordinates: null
  place: null
  contributors: null
  is_quote_status: boolean
  retweet_count: number
  favorite_count: number
  favorited: boolean
  retweeted: boolean
  possibly_sensitive?: boolean
  lang: string
  quoted_status?: ITwitterData
}

export interface ICurriculumDocument {
  id: Id
  name: string
  active: boolean
  fileboy_id: string
  weight: number
  available_to_trial: boolean
}

export interface IQuestionnaire {
  id: Id
  name: string
  include_demographics_questions: boolean
  // questionnaire_type: IQuestionnaireTypes
  new_library_unit?: { id: Id; name: string }
  new_library_unit_id?: Id | null
}

export interface IQuestionnaireQuestion {
  id: Id
  question: string
  description: string
  question_type: QuestionTypes
  question_data?: any
  weight: number
  questionnaire_id: Id
}
export interface IQuestionnaireQuestionWithOptions {
  id: Id
  question: string
  description: string
  question_type: QuestionTypes
  question_data?: any
  weight: number
  questionnaire_id: Id
  options: IQuestionnaireOption<any>
}
export interface IQuestionnaireOption<T> {
  id: Id
  questionnaire_question_id: Id
  option_data: T
  weight: number
  career_tags?: { id: Id; name: string }[]
  career_tag_ids?: Id[]
  _destroy?: boolean
}
export interface IQuestionnaireAnswer {
  id: Id
  user_id?: Id
  questionnaire_option_id: Id
  questionnaire_question_id: Id
  answer_value: string
}

interface IQuestionnaireStats {
  question: IQuestionnaireQuestion
  options_with_data: {
    option: IQuestionnaireOption<any>
    count: number
    recent_responses: IQuestionnaireAnswer[]
  }[]
}
export interface IQuestionnaireDemographicsStats {
  answer: IQuestionnaireAnswer
  gender: string
  ethnicity: string
  date_of_birth: string
  county: string
}

export interface ILessonTemplateFolder {
  id: Id
  parent_folder_id: Id
  name: string
  templates: {
    id: Id
    name: string
    fileboy_image_id: string
    user_id: Id
    new_presentation_id: Id
  }[]
  folders: ILessonTemplateFolder[]
}

export interface IYoutubeDocument {
  id: Id
  name: string
  fileboy_id: string
  live_stream_id: Id
  weight: number
}
export interface Iv2YoutubeStream {
  id: Id
  title: string
  url: string
  published: boolean
  documents?: IYoutubeDocument[]
  start_time?: Date | null
  end_time?: Date | null
  is_live?: boolean
  schools: { id: Id; name: string }[]
  school_ids?: Id[]
  fileboy_image_id?: string
}
export interface Iv2YoutubeStreamMessages {
  id: Id
  message: string
  user_id: Id
  user: { id: Id; name: string }
  school: null | { id: Id; name: string; type: string }
  live_stream_id: Id
  live_stream_message_id: Id
  created_at: string
  pinned: boolean
  replies: Iv2YoutubeStreamMessages[]
}

export interface Iv2FormUnit {
  id: Id
  form_id: Id
  new_library_unit_id: Id
  start_date: Date
  end_date: string
  form: Iv2Form
  new_library_unit: Iv2Unit
  lessons: { id: Id; template_id: Id }[]
  active: boolean
  questionnaire?: IQuestionnaire
}

interface ISchoolsIndexExtendedParams {
  ids?: Id[]
  page?: number
  query?: string
  scope?: string
  schoolType?: string
  unpaginated?: boolean
  hasAnnualSubscription?: boolean
  country_id?: string
  region?: string
  scopes?: string
  localAuthority?: string
  postcodes?: string
  whiteLabelled?: boolean
  hubspot_subscription_status?: string
  sort_by?: string
  sort_dir?: string
  created_at?: Date | string | null
}
export interface Iv2ResourceChange {
  school_id: Id | null
  source_name: string
  attribute_name: string
  old_value: string
  new_value: string
  reason: string
}

interface Iv2DeMedia {
  id: Id
  de_social_id: Id
  videos2_id: Id
  video: Iv2Video
  fileboy_image_id: string
  weight: number
  published: boolean
}
interface Iv2DeSocial {
  id: Id
  name: string
  body: string
  published: boolean
  published_at?: string
  de_media: Iv2DeMedia[]
  created_at?: string
}

interface Iv2CustomSignUpUrl {
  id: Id
  name: string
  published: boolean
  url: string
}
interface Iv2Glossary {
  id: Id
  name: string
  short_description: string
  content: string
  fileboy_image_id: string
  usage: string
  image_caption: string
  secondary_content: string
  secondary_usage: string
  secondary_fileboy_image_id: string
  secondary_image_caption: string
  word_forms: string
  etymology: string
  task_question: string
  aqa_science_exam_question_answer: string
  slug: string
  related_glossary_ids: Id[]
  related_glossary_slugs: string[]
  is_suggested: boolean
  published: boolean
  user_id: Id
}

export interface Iv2ScientificEnquiryType {
  id: Id
  title: string
  body: string
  fileboy_icon_id: string
}

export interface IHomework {
  id: Id
  created_by_id: Id
  date_set: string
  date_due: string
  title: string
  body: string
  cover_image_fileboy_id: string
  published: boolean
  lesson_lesson_id: Id
  created_at: string
  updated_at: string
  show_lesson_files: boolean
  school_id: Id
}
export type IHomeworkIndexRecord = IHomework & {
  lesson: Iv2LessonTemplate
  pupils: Iv2Pupil[]
  tasks: (IHomeworkTask & { submissions: IHomeworkTaskSubmission[] })[]
}
export interface IHomeworkShow extends IHomework {
  created_by: Iv2User
  lesson?: Iv2LessonTemplate
  files: IHomeworkFile[]
  pupils: Iv2Pupil[]
  tasks: (IHomeworkTask & { submissions: IHomeworkTaskSubmission[]; files: IHomeworkFile[] })[]
}
export interface IHomeworkTask {
  id: Id
  title: string
  body: string
  homework_id: Id
  task_type: "text" | "upload" | "quiz" | "lesson_quiz"
  task_score: number | null
  files: IHomeworkFile[]
  quiz_question_data?: QuizJson | null
}

export interface IHomeworkTaskSubmission {
  id: Id
  user_id: Id
  homework_task_id: Id
  body: string
  files: IHomeworkFile[]
  complete_at?: string | null
  submitted_at?: string | null
  score?: number | null
  notes?: string
  quiz_result?: any
}
export interface IHomeworkFile {
  id: Id
  name: string
  fileboy_id: string
  homework_id?: Id
  homework_task_id?: Id
  homework_task_submission_id?: Id
}

export type Id = string | number

export type IApiMethodNames = keyof ReturnType<typeof apiProvider["use"]>
export type IApiParameters<Key extends IApiMethodNames> = Parameters<
  ReturnType<typeof apiProvider["use"]>[Key]
>
export type IApiV2 = ReturnType<typeof apiProvider["use"]>
type Awaited<T> = T extends Promise<infer U> ? U : never

export type IApiV2Response<Key extends IApiMethodNames> = Awaited<
  ReturnType<ReturnType<typeof apiProvider["use"]>[Key]>
>

type IIndexRequest<Record> = { records: Record[]; meta: IIndexMeta }
type ICreateRequest<Record> = { saved: true; record: Record } | { saved: false; errors: string[] }

export interface IIndexMeta {
  page: number
  perPage: number
  totalCount: number
  itemCount: number
}

type IUpdateResponse<T = undefined> =
  | { saved: false; errors: string[]; data?: never }
  | { saved: true; data: T; errors?: never }

export type ILessonReport = {
  id: Id
  body: string
  report_type: string
  status: "pending" | "actioned"
  user: Iv2User
  school: Iv2School
  lesson_template: Iv2LessonTemplate
  created_at: string
}
const apiProvider = createApiProvider({
  accreditationsIndex(request, query: { ids?: Id[]; page?: number; query?: string } = {}) {
    return request<{ records: Iv2Accreditation[]; meta: IIndexMeta }>(
      buildUrlWithQueryString("/accreditations", query),
      undefined,
      { method: "GET" },
    )
  },
  accreditationsUnpaginatedIndex(request) {
    return request<{ records: Iv2Accreditation[] }>(
      "/accreditations/unpaginated_index",
      undefined,
      {
        method: "GET",
      },
    )
  },
  accreditationsShow(request, id: Id) {
    return request<Iv2Accreditation>(`/accreditations/${id}`, {}, { method: "GET" })
  },
  accreditationsDelete(request, id: Id) {
    return request<{ success?: boolean; error?: string }>(
      `/accreditations/${id}`,
      {},
      { method: "DELETE" },
    )
  },
  accreditationsUpdate(request, id: Id, data: Partial<Iv2Accreditation>) {
    return request<
      { success: true; record: Iv2Accreditation } | { success: false; errors: string[] }
    >(`/accreditations/${id}`, toFormData(data), {
      method: "PUT",
      formData: true,
    })
  },
  accreditationsCreate(request, data: Partial<Iv2Accreditation>) {
    return request<
      | { success: true; record: Iv2Accreditation; errors?: never }
      | { errors: string[]; success: false }
    >("/accreditations", data, {
      method: "POST",
    })
  },
  accreditationsUpdateWeights(request, data: { id: Id; weight: number }[]) {
    return request<{ success: true; errors?: never } | { errors: string[]; success: false }>(
      "/accreditations/update_weights",
      { weight_data: data },
      {
        method: "POST",
      },
    )
  },
  campaignsIndex(request, opts: { page?: number; query?: string; organisationId?: Id }) {
    return request<IIndexRequest<Iv2Campaign>>(
      buildUrlWithQueryString("/campaigns", opts),
      undefined,
      { method: "GET" },
    )
  },
  campaignsShow(request, opts: { id: Id }) {
    return request<Iv2Campaign>(buildUrlWithQueryString(`/campaigns/${opts.id}`, opts), undefined, {
      method: "GET",
    })
  },
  campaignsCreate(request, data: any) {
    return request<{ saved: true; record: Iv2Campaign } | { saved: false; errors: string[] }>(
      `/campaigns`,
      data,
    )
  },
  campaignsUpdate(request, id: Id, data: any) {
    return request<{ saved: true; record: Iv2Campaign } | { saved: false; errors: string[] }>(
      `/campaigns/${id}`,
      data,
      { method: "PUT" },
    )
  },
  campaignsPredictedReach(request, id: Id) {
    return request<{
      returningCurrentUnits: boolean
      schoolCount: number
      pupilCount: number
      lessonCount: number
      unitRecommendations: {
        id: Id
        tags: string[]
        potentialUnit?: boolean
        pupilCount: number
        schoolCount: number
        lessonCount: number
        isAttached?: boolean
        name: string
        lessonTemplateIds: Id[]
      }[]
    }>(`/campaigns/${id}/predicted_reach`, undefined, {
      method: "GET",
    })
  },
  wordSearchLobbiesIndexLite(request) {
    return request<IWordSearchLobbyIndex[]>(
      "/word_search_lobbies/lobby_index",
      {},
      {
        method: "GET",
      },
    )
  },
  wordSearchLobbiesShow(request, id: string) {
    return request<IWordSearchLobby | { error: "not found" }>(
      `/word_search_lobbies/${id}`,
      undefined,
      {
        method: "GET",
        throwErrorOn404: false,
      },
    )
  },
  wordSearchLobbiesJoin(request, lobbyId: string) {
    return request<IWordSearchLobby | { error: string }>(
      `/word_search_lobbies/${lobbyId}/join`,
      undefined,
      {
        method: "POST",
      },
    )
  },
  wordSearchLobbiesLeave(request, lobbyId: string) {
    return request<IWordSearchLobby | { error: string }>(
      `/word_search_lobbies/${lobbyId}/leave`,
      undefined,
      {
        method: "POST",
      },
    )
  },
  wordSearchLobbyUserIncrementScore(request, lobbyUserId: Id) {
    return request<IWordSearchLobby["users"][number]>(
      `/word_search_lobby_users/${lobbyUserId}/increment_score`,
      {
        method: "POST",
      },
    )
  },
  wordSearchStats(request, userId: Id) {
    return request<{ best_time: number | null }>("/word_search_lobbies/user-stats", {
      user_id: userId,
    })
  },
  schoolsSyncHubspot(request, query: { id: Id }) {
    return request<{ saved: boolean; message?: string }>(
      `/schools/${query.id}/sync_hubspot`,
      undefined,
      {
        method: "GET",
      },
    )
  },
  schoolsHubspotSignup(request, query: { id: Id }) {
    return request<{ saved: boolean; message?: string }>(
      `/schools/${query.id}/sync_hubspot_signup`,
      undefined,
      {
        method: "GET",
      },
    )
  },
  schoolsHubspotSubscribe(request, query: { id: Id }) {
    return request<{ saved: boolean; message?: string }>(
      `/schools/${query.id}/sync_hubspot_subscribe`,
      undefined,
      {
        method: "GET",
      },
    )
  },
  schoolsDeleteAllPupils(request, id: Id) {
    return request<{ success: true; error?: never } | { success: false; error: string }>(
      `/schools/${id}/delete_all_pupils`,
      undefined,
      { method: "POST" },
    )
  },
  campaignSchoolsIndex(
    request,
    id: Id,
    query: {
      organisationId?: Id
      page?: number
      query?: string
      scope?: "generic" | "demo" | "home_school" | "unregistered"
      unpaginated?: boolean
      range_min?: Date
      range_max?: Date
    } = {},
  ) {
    return request<{ records: Iv2School[]; meta: IIndexMeta }>(
      buildUrlWithQueryString(`/campaigns/${id}/school_interactions_index`, query),
      undefined,
      { method: "GET" },
    )
  },
  schoolsIndex(
    request,
    query: {
      ids?: Id[]
      organisationId?: Id
      page?: number
      query?: string
      scope?: "generic" | "demo" | "home_school" | "unregistered"
      unpaginated?: boolean
    } = {},
  ) {
    return request<{ records: Iv2School[]; meta: IIndexMeta }>(
      buildUrlWithQueryString("/schools", query),
      undefined,
      { method: "GET" },
    )
  },
  schoolsIndexExtended(request, query: ISchoolsIndexExtendedParams = {}) {
    return request<{
      records: (Iv2School & {
        uk_school: Iv2UkSchool
        teachers_count: number
        classes_count: number
        pupils_count: number
        created_at: string
        cache_activity_30_days: number
        active_teachers_30_percent: number
        cache_activity_60_days: number
        active_teachers_60_percent: number
      })[]
      meta: IIndexMeta
    }>(buildUrlWithQueryString("/schools/extended_index", query), undefined, { method: "GET" })
  },
  schoolsIndexExtendedCsv(request, query: ISchoolsIndexExtendedParams = {}) {
    return request<{ csv: string; filename: string }>(
      buildUrlWithQueryString("/schools/extended_index_csv", query),
      undefined,
      { method: "GET" },
    )
  },
  schoolsLocationList(request, data?: { subscribed?: boolean }) {
    return request<{ id: Id; category: string; postcode: string }[]>(
      buildUrlWithQueryString("/schools/location_list", data as any),
      undefined,
      { method: "GET" },
    )
  },
  schoolsShow(request, id: Id) {
    return request<Iv2School>(`/schools/${id}`, {}, { method: "GET" })
  },
  motdsIndex(request, query: { organisationId?: Id; query?: string; page: number }) {
    return request<{ records: IMotd[]; meta: IIndexMeta }>(
      buildUrlWithQueryString("/motds", query),
      undefined,
      { method: "GET" },
    )
  },
  unitsIndex(
    request,
    query: {
      year_id?: string
      curriculum_id?: string
      query?: string
      ids?: Id[]
      unpaginated?: boolean
      organisationId?: Id
    } = {},
  ) {
    return request<{ records: Iv2Unit[]; meta: IIndexMeta }>(
      buildUrlWithQueryString("/units", query),
      undefined,
      { method: "GET" },
    )
  },
  unitsShow(request, id) {
    return request<Iv2Unit>(`/units/${id}`, {}, { method: "GET" })
  },
  pupilGuardianIds(request, query: { id: Id }) {
    return request<{ guardian_ids: Id[] }>(
      `/pupils/${query.id}/guardian_ids`,
      {},
      { method: "GET" },
    )
  },
  pupilsIndex(
    request,
    query: { ids?: Id[]; userId?: Id; page?: number; query?: string; schoolId?: Id } = {},
  ) {
    return request<{ records: Iv2Pupil[]; meta: IIndexMeta }>(
      buildUrlWithQueryString("/pupils", query),
      undefined,
      { method: "GET" },
    )
  },
  pupilsShow(request, id: Id) {
    return request<Iv2Pupil>(`/pupils/${id}`, {}, { method: "GET" })
  },
  pupilDelete(request, id: Id) {
    return request<{ success?: boolean; error?: string }>(`/pupils/${id}`, {}, { method: "DELETE" })
  },
  pupilsUpdate(request, id: Id, data: Partial<IPupil>) {
    return request<{ saved: true; record: IPupil } | { saved: false; errors: string[] }>(
      `/pupils/${id}`,
      toFormData(data),
      { method: "PUT", formData: true },
    )
  },
  pupilsRemoveGuardian(request, { pupilId, guardianId }: { pupilId: Id; guardianId: Id }) {
    return request<{ saved: true; record: IPupil } | { saved: false; errors: string[] }>(
      `/pupils/${pupilId}/remove_guardian`,
      toFormData({ guardianId }),
      { method: "PUT", formData: true },
    )
  },
  pupilsTrackingStatsShow(request) {
    return request<
      Iv2Pupil & {
        tracking_rocket_words: Iv2TrackingRocketWord[]
        tracking_films: Iv2TrackingFilms[]
        tracking_word_searches: Iv2TrackingWordSearch[]
        tracking_summative_quizzes: Iv2TrackingSummativeQuiz[]
        tracking_link_trackings: Iv2TrackingLinkTrackings[]
        tracking_documents: Iv2TrackingDocuments[]
        tracking_lesson_template_views: Iv2TrackingLessonTemplateViews[]
        tracking_lesson_template_favourites: Iv2TrackingLessonTemplateFavourites[]
        tracking_presentation_views: Iv2TrackingPresentationViews[]
      }
    >("/pupils/my_tracking_data", {}, { method: "GET" })
  },

  achievementsIndex(request, query: { page?: number; query?: string } = {}) {
    return request<{ records: Iv2Achievement[]; meta: IIndexMeta }>(
      buildUrlWithQueryString("/achievements", query),
      {},
      { method: "GET" },
    )
  },
  achievementsShow(request, id: Id) {
    return request<Iv2Achievement>(`/achievements/${id}`, {}, { method: "GET" })
  },
  achievementsCreate(request, data: Omit<Iv2Achievement, "id">) {
    return request<{ saved: true; record: Iv2Achievement } | { saved: false; errors: string[] }>(
      "/achievements",
      toFormData(data),
      { method: "POST", formData: true },
    )
  },
  achievementsUpdate(request, id: Id, data: Partial<Iv2Achievement>) {
    return request<{ saved: true; record: Iv2Achievement } | { saved: false; errors: string[] }>(
      `/achievements/${id}`,
      toFormData(data),
      { method: "PUT", formData: true },
    )
  },
  achievementsDelete(request, id: Id) {
    return request<{ success: true } | { success?: false; error: string }>(
      `/achievements/${id}`,
      {},
      { method: "DELETE" },
    )
  },
  achievementsDataForPupil(request, id: Id) {
    return request<{ records: Iv2PupilAchievement[] }>(
      "/achievements/data_for_pupil",
      { pupil_id: id },
      { method: "GET" },
    )
  },
  pupilsLoginCodeCsv(request, query: { form_id?: Id; school_id?: Id; teacher_id?: Id } = {}) {
    return request<{ csv: string }>(
      buildUrlWithQueryString("/pupils/login_code_csv", query),
      undefined,
      { method: "GET" },
    )
  },
  lessonsIndex(
    request,
    query: {
      userId?: Id
      teacherId?: Id
      page?: number
      query?: string
      template_id?: Id
      templateIds?: Id[]
      from_date?: Date
      unpaginated?: boolean
    } = {},
  ) {
    return request<{
      records: (Iv2LessonLesson & { template: Iv2LessonTemplate; form: Iv2Form })[]
      meta: IIndexMeta
    }>(`/lessons`, { ...query, templateIds: query.templateIds?.join(",") }, { method: "GET" })
  },
  teachersIndex(
    request,
    query: {
      userId?: Id
      page?: number
      query?: string
      schoolQuery?: string
      referralQuery?: string
      schoolId?: Id
      unpaginated?: boolean
      serialized?: boolean
    } = {},
  ) {
    return request<{
      records: (Iv2Teacher & {
        school_name: string
        school_id: Id
        school_type: "generic" | "demo" | "home_school" | "unregistered"
      })[]
      meta: IIndexMeta
    }>(buildUrlWithQueryString("/teachers", query), undefined, { method: "GET" })
  },
  formsShow(request, id: Id) {
    return request<Iv2Form>(`/forms/${id}`, undefined, { method: "GET" })
  },
  formsIndex(
    request,
    query: {
      schoolId?: Id
      userId?: Id
      page?: number
      query?: string
    } = {},
  ) {
    const queryObj: Record<string, any> = {}

    for (const key in query) {
      queryObj[key] = String(query[key as keyof typeof query])
    }

    const queryStr = "?" + new URLSearchParams(queryObj).toString()

    return request<{ records: Iv2Form[]; meta: IIndexMeta }>(`/forms${queryStr}`, undefined, {
      method: "GET",
    })
  },
  formsRemovePupil(request, opts: { formId: Id; pupilId: Id }) {
    return request<IUpdateResponse>(
      `/forms/${opts.formId}/removePupil?pupilId=${encodeURIComponent(opts.pupilId)}`,
      undefined,
      { method: "DELETE" },
    )
  },
  formsAddPupil(request, opts: { formId: Id; pupilId: Id }) {
    return request<IUpdateResponse>(
      `/forms/${opts.formId}/addPupil?pupilId=${encodeURIComponent(opts.pupilId)}`,
      undefined,
      { method: "POST" },
    )
  },
  guardiansIndex(request, data?: { pupilId?: Id; page?: number; query?: string }) {
    return request<{ records: Iv2Guardian[]; meta: IIndexMeta }>("/guardians", data, {
      method: "GET",
    })
  },
  guardiansDelete(request, id: Id) {
    return request<{ success: true; errors?: never } | { errors: string[]; success: false }>(
      `/guardians/${id}`,
      {},
      {
        method: "DELETE",
      },
    )
  },
  guardiansShow(request, id: Id) {
    return request<Iv2Guardian>(`/guardians/${id}`, {}, { method: "GET" })
  },
  guardiansUpdate(request, id: Id, data: Partial<Iv2Guardian>) {
    return request<{ saved: true; record: Iv2Guardian } | { saved: false; errors: string[] }>(
      `/guardians/${id}`,
      toFormData(data),
      { method: "PUT", formData: true },
    )
  },
  trackingPresentationViewEnd(request, params: { templateId: Id; timeViewed: number }) {
    return request<Iv2TrackingPresentationViews>(`/trackings/presentation_view_end`, params, {
      method: "POST",
    })
  },
  trackingFilmViewed(
    request,
    params: {
      lessonId: string
      templateId: string
      fileboyVideoId: string
      timeViewing: number
      filmDuration: number
      filmType: string
    },
  ) {
    return request<Iv2TrackingPresentationViews>(`/trackings/track_film_viewed`, params, {
      method: "POST",
    })
  },
  trackUserTemplateViewed(
    request,
    params: {
      templateId: Id
      userId?: Id
    },
  ) {
    return request<{ saved?: boolean }>(`/users/${params.userId}/track_template_viewed`, params, {
      method: "POST",
    })
  },
  trackingRecordsChronologicalIndex(request, params: { userId?: Id; page: number }) {
    return request<{
      records: (
        | (Iv2TrackingRocketWord & { type: "tracking_rocket_word" })
        | (Iv2TrackingFilms & { type: "tracking_film" })
        | (Iv2TrackingWordSearch & { type: "tracking_word_search" })
        | (Iv2TrackingSummativeQuiz & { type: "tracking_quiz" })
        | (Iv2TrackingLinkTrackings & { type: "tracking_link" })
        | (Iv2TrackingDocuments & { type: "tracking_document" })
        | (Iv2TrackingLessonTemplateViews & { type: "tracking_template_view" })
        | (Iv2TrackingLessonTemplateFavourites & { type: "tracking_favourite" })
        | (Iv2TrackingLogin & { type: "tracking_login" })
        | (Iv2TrackingPresentationViews & { type: "tracking_presentation_view" })
        | (Iv2TrackingAccountEdit & { type: "tracking_account_edit" })
        | (Iv2TrackingMarkAssignment & { type: "tracking_mark_assignment" })
      )[]
      meta: IIndexMeta
    }>(`/trackings/chronological_data`, params, {
      method: "POST",
    })
  },
  formsRemoveTeacher(request, opts: { formId: Id; teacherId: Id }) {
    return request<IUpdateResponse>(
      `/forms/${opts.formId}/removeTeacher?teacherId=${encodeURIComponent(opts.teacherId)}`,
      undefined,
      { method: "DELETE" },
    )
  },
  formsAddTeacher(request, opts: { formId: Id; teacherId: Id }) {
    return request<IUpdateResponse>(
      `/forms/${opts.formId}/addTeacher?teacherId=${encodeURIComponent(opts.teacherId)}`,
      undefined,
      { method: "POST" },
    )
  },
  formsRemoveAllLessons(request, formId: Id) {
    return request<IUpdateResponse>(`/forms/${formId}/remove_all_lessons`, undefined, {
      method: "POST",
    })
  },
  teachersShow(request, id: Id) {
    return request<Iv2Teacher>(`/teachers/${id}`, {}, { method: "GET" })
  },
  teachersTaskProgress(request, id: Id) {
    return request<Iv2TeacherTaskProgress>(`/teachers/${id}/task_progress`, {}, { method: "GET" })
  },
  teachersUpdate(request, id: Id, data: Partial<Iv2Teacher>) {
    return request<{ saved: true; record: Iv2Teacher } | { saved: false; errors: string[] }>(
      `/teachers/${id}`,
      toFormData(data),
      { method: "PUT", formData: true },
    )
  },
  industryTagsIndex(
    request,
    query: {
      lessonTemplateId?: Id
      page?: number
      query?: string
      ids?: Id[]
    } = {},
  ) {
    return request<{
      records: Iv2IndustryTag[]
      meta: IIndexMeta
    }>(buildUrlWithQueryString("/industry_tags", query), undefined, { method: "GET" })
  },
  industryTagsShow(request, id: Id) {
    return request<Iv2IndustryTag>(`/industry_tags/${id}`, {}, { method: "GET" })
  },
  industryTagsDelete(request, id: Id) {
    return request<{ success?: boolean; error?: string }>(
      `/industry_tags/${id}`,
      {},
      { method: "DELETE" },
    )
  },
  industryTagsUpdate(request, id: Id, data: Partial<Iv2IndustryTag>) {
    return request<{ saved: true; record: Iv2IndustryTag } | { saved: false; errors: string[] }>(
      `/industry_tags/${id}`,
      toFormData(data),
      { method: "PUT", formData: true },
    )
  },
  industryTagsCreate(request, data: Partial<Iv2IndustryTag>) {
    return request<{ saved: true; record: Iv2IndustryTag } | { saved: false; errors: string[] }>(
      `/industry_tags`,
      data,
      { method: "POST" },
    )
  },
  lessonTemplatesIndex(
    request,
    query: {
      page?: number
      query?: string
      ids?: Id[]
      taggedWithAll?: Id[]
      taggedWithAny?: Id[]
      unpaginated?: boolean
      withRocketWords?: boolean
      user_id?: Id
      organisationId?: Id
      unit_id?: Id
      with_user_generated?: boolean
    } = {},
  ) {
    return request<{
      records: Iv2LessonTemplate[]
      meta: IIndexMeta
    }>(buildUrlWithQueryString("/lesson_templates", query), undefined, { method: "GET" })
  },
  lessonTemplatesShow(request, id: Id) {
    return request<Iv2LessonTemplateShow>(`/lesson_templates/${id}`, {}, { method: "GET" })
  },
  lessonTemplatesUserData(request, id: Id) {
    return request<Iv2LessonTemplateUserData>(
      `/lesson_templates/${id}/data_for_user`,
      {},
      { method: "GET" },
    )
  },
  lessonTemplatesLoadRocketWordsQuiz(request, id: Id) {
    return request<Iv2RocketWordsQuizQuestion[]>(
      `/lesson_templates/${id}/load_rocket_words_quiz`,
      {},
      { method: "GET" },
    )
  },
  lessonTemplatesLoadQuizQuestions(request, id: Id) {
    return request<IPresentationQuizQuestion[]>(
      `/lesson_templates/${id}/load_quiz_questions`,
      {},
      { method: "GET" },
    )
  },
  lessonSlidesShow(request, id: Id) {
    return request<Iv2LessonSlide>(`/lesson_slides/${id}`, {}, { method: "GET" })
  },
  lessonSlideSetAsCurrent(request, id: Id) {
    return request<{}>(`/lesson_slides/${id}/set_as_current`, {}, { method: "POST" })
  },
  lessonSlidesNarration(request, id: Id) {
    return request<Record<string, string>>(`/lesson_slides/${id}/narration`, {}, { method: "GET" })
  },
  lessonKeywordsNarration(request, id: Id) {
    return request<Record<string, string>>(
      `/lesson_keywords/${id}/narration`,
      {},
      { method: "GET" },
    )
  },
  quizOldQuestionsNarration(request, id: Id) {
    return request<Record<string, string>>(
      `/quiz_old_questions/${id}/narration`,
      {},
      { method: "GET" },
    )
  },
  sponsorsShow(request, id: Id) {
    return request<Iv2Sponsor>(`/sponsors/${id}`, {}, { method: "GET" })
  },
  sponsorsDelete(request, id: Id) {
    return request<{ success?: boolean; error?: string }>(`/sponsors/${id}`)
  },
  sponsorsUpdate(request, id: Id, data: Partial<Iv2Sponsor>) {
    return request<{ saved: true; record: Iv2Sponsor } | { saved: false; errors: string[] }>(
      `/sponsors/${id}`,
      toFormData(data),
      { method: "PUT", formData: true },
    )
  },
  sponsorsCreate(request, data: Partial<Iv2Sponsor>) {
    return request<{ saved: true; record: Iv2Sponsor } | { saved: false; errors: string[] }>(
      `/sponsors`,
      toFormData(data),
      { method: "POST", formData: true },
    )
  },
  careersIndex(
    request,
    query: {
      ids?: Id[]
      page?: number
      query?: string
      families?: string
      salaryMin?: string
      salaryMax?: string
      taggedWithAll?: Id[]
      taggedWithAny?: Id[]
      sort?: string
      unpaginated?: boolean
      organisationId?: Id
      organisationId_many?: Id
    } = {},
  ) {
    return request<{
      records: Iv2Career[]
      meta: IIndexMeta
      salary_range: { max_salary: number; min_salary: number } | null
    }>(buildUrlWithQueryString("/careers", query), undefined, { method: "GET" })
  },
  careersShow(request, id: Id) {
    return request<Iv2Career>(`/careers/${id}`, {}, { method: "GET" })
  },
  careersDelete(request, id: Id) {
    return request<{ success?: boolean; error?: string }>(
      `/careers/${id}`,
      {},
      { method: "DELETE" },
    )
  },
  careersUpdate(request, id: Id, data: Partial<Iv2Career>) {
    return request<{ saved: true; record: Iv2Career } | { saved: false; errors: string[] }>(
      `/careers/${id}`,
      toFormData(data),
      { method: "PUT", formData: true },
    )
  },
  careersCreate(request, data: Partial<Iv2Career>) {
    return request<{ saved: true; record: Iv2Career } | { saved: false; errors: string[] }>(
      `/careers`,
      data,
      { method: "POST" },
    )
  },
  careersFamilies(request, params?: { query?: string }) {
    return request<{ families: string[] }>(`/careers/career_families`, params, { method: "GET" })
  },
  careersRelatedCareers(request, id: Id) {
    return request<{
      records: Iv2Career[]
      meta: IIndexMeta
    }>(`/careers/${id}/related_careers`, {}, { method: "GET" })
  },
  careersRelatedCourses(request, id: Id) {
    return request<{
      records: Iv2CareerCourse[]
      meta: IIndexMeta
    }>(`/careers/${id}/related_courses`, {}, { method: "GET" })
  },
  careersRelatedVacancies(request, id: Id) {
    return request<{
      records: Iv2CareerVacancy[]
      meta: IIndexMeta
    }>(`/careers/${id}/related_vacancies`, {}, { method: "GET" })
  },
  careersRelatedEvents(request, id: Id) {
    return request<{
      records: Iv2Event[]
      meta: IIndexMeta
    }>(`/careers/${id}/related_events`, {}, { method: "GET" })
  },
  careersRelatedLessonTemplates(request, id: Id) {
    return request<{
      records: Iv2LessonTemplate[]
      meta: IIndexMeta
    }>(`/careers/${id}/related_lesson_templates`, {}, { method: "GET" })
  },
  usersIndex(
    request,
    query: {
      page?: number
      query?: string
      ids?: Id[]
      organisationId?: Id
      whiteLabelOrganisationId?: Id
      userType?: string
    } = {},
  ) {
    return request<{
      records: Iv2User[]
      meta: IIndexMeta
    }>(buildUrlWithQueryString("/users", query), undefined, { method: "GET" })
  },
  usersShow(request, id: Id) {
    return request<Iv2User>(`/users/${id}`, {}, { method: "GET" })
  },
  usersDelete(request, id: Id, params: { permanentlyDelete?: boolean }) {
    return request<{ success?: boolean; error?: string }>(
      buildUrlWithQueryString(`/users/${id}`, params),
      {},
      {
        method: "DELETE",
      },
    )
  },
  usersUpdate(request, id: Id, data: Partial<Iv2User>) {
    return request<{ saved: true; record: Iv2User } | { saved: false; errors: string[] }>(
      `/users/${id}`,
      toFormData(data),
      { method: "PUT", formData: true },
    )
  },
  usersCareerFeed(request, id: Id, query: { page?: number } = {}) {
    return request<{
      records: Iv2CareerFeedItem[]
      meta: IIndexMeta
    }>(buildUrlWithQueryString(`/users/${id}/career_feed`, query), undefined, { method: "GET" })
  },

  invitesIndex(
    request,
    data: { userId?: Id; pupilIdsInclude?: Id[]; page?: number; query?: string } = {},
  ) {
    return request<IIndexRequest<Iv2Invite>>(
      buildUrlWithQueryString("/invites", {
        user_id: data.userId,
        pupil_ids_include: data.pupilIdsInclude,
        query: data.query,
        page: data.page,
      }),
      undefined,
      { method: "GET" },
    )
  },
  invitesInviteGuardian(
    request,
    data: ({ email: string; guardianId?: never } | { email?: never; guardianId: Id }) & {
      pupilId?: Id
    },
  ) {
    return request<
      | { saved: true; record: Iv2Invite }
      | { saved: false; errors: string[]; guardian_id?: Id; guardian_name?: string }
    >("/invites/invite_guardian", {
      guardian_id: data.guardianId,
      email: data.email,
      pupil_id: data.pupilId,
    })
  },
  invitesAccept(request, inviteId: Id) {
    return request<{ success: true } | { success: false; errors: string[] }>(
      `/invites/${inviteId}/accept`,
      undefined,
      {
        method: "POST",
      },
    )
  },
  inviteRequestsIndex(
    request,
    data: { guardianId?: Id; pupilId?: Id; schoolId?: Id; page?: number; query?: string } = {},
  ) {
    return request<IIndexRequest<Iv2InviteRequest>>(
      buildUrlWithQueryString("/invite_requests", {
        guardian_id: data.guardianId,
        pupil_id: data.pupilId,
        school_id: data.schoolId,
        query: data.query,
        page: data.page,
      }),
      undefined,
      { method: "GET" },
    )
  },
  inviteRequestsDelete(request, id: Id) {
    return request<{ success: true }>(`/invite_requests/${id}`, {}, { method: "DELETE" })
  },
  trainingRoutesIndex(
    request,
    query: {
      page?: number
      query?: string
      ids?: Id[]
    } = {},
  ) {
    return request<{
      records: Iv2TrainingRoute[]
      meta: IIndexMeta
    }>(buildUrlWithQueryString("/training_routes", query), undefined, { method: "GET" })
  },
  trainingRoutesDelete(request, id: Id) {
    return request<{ success?: boolean; error?: string }>(
      `/training_routes/${id}`,
      {},
      { method: "DELETE" },
    )
  },
  trainingRoutesUpdate(request, id: Id, data: Partial<Iv2TrainingRoute>) {
    return request<{ saved: true; record: Iv2TrainingRoute } | { saved: false; errors: string[] }>(
      `/training_routes/${id}`,
      toFormData(data),
      { method: "PUT", formData: true },
    )
  },
  trainingRoutesCreate(request, data: Partial<Iv2TrainingRoute>) {
    return request<{ saved: true; record: Iv2TrainingRoute } | { saved: false; errors: string[] }>(
      `/training_routes`,
      data,
      { method: "POST" },
    )
  },
  careerTagsIndex(request, query: { ids?: Id[]; page?: number; query?: string } = {}) {
    return request<{ records: Iv2CareerTagWithCounts[]; meta: IIndexMeta }>(
      buildUrlWithQueryString("/career_tags", query),
      undefined,
      { method: "GET" },
    )
  },
  careerTagsShow(request, id: Id) {
    return request<Iv2CareerTagWithCounts>(`/career_tags/${id}`, {}, { method: "GET" })
  },
  careerTagsCreate(request, data: Partial<Iv2CareerTag>) {
    return request<{ saved: true; record: Iv2CareerTag } | { saved: false; errors: string[] }>(
      "/career_tags",
      data,
      { method: "POST" },
    )
  },
  careerTagsCreateFromArray(request, data: { tags: string[] }) {
    return request<{ id: Id; name: string }[]>("/career_tags/create_from_array", data, {
      method: "POST",
    })
  },
  careerTagsUpdate(request, id: Id, data: Partial<Iv2CareerTag>) {
    return request<{ saved: true; record: Iv2CareerTag } | { saved: false; errors: string[] }>(
      `/career_tags/${id}`,
      toFormData(data),
      { method: "PUT", formData: true },
    )
  },
  careerTagsDelete(request, id: Id) {
    return request<{ success?: boolean; error?: string }>(
      `/career_tags/${id}`,
      {},
      { method: "DELETE" },
    )
  },
  ////////////
  // referrals
  referralsIndex(request, query: { ids?: Id[]; page?: number; query?: string } = {}) {
    return request<{ records: { id: string; created_at: string }[]; meta: IIndexMeta }>(
      `/referrals`,
      query,
      {
        method: "GET",
      },
    )
  },
  referralsMyData(request) {
    return request<{ code: string; count: number; share_name: boolean }>(
      `/referrals/my_data`,
      {},
      { method: "GET" },
    )
  },
  lessonShow(request, id: Id) {
    return request<{
      lesson: Iv2LessonLesson
      template: Iv2LessonTemplate
      form: Iv2Form
      homeworks: IHomework[]
      progress: {
        presentation_viewed: boolean
        rocket_word_quiz: boolean
        summative_quiz: boolean
        tracking_word_search: boolean
      }
      error?: never
    }>(`/lessons/${id}`, {}, { method: "GET" })
  },
  ukSchoolIndex(
    request,
    query: {
      page?: number
      query?: string
      postcode?: string
      ids?: Id[]
    } = {},
  ) {
    return request<{
      records: Iv2UkSchool[]
      meta: IIndexMeta
    }>(buildUrlWithQueryString("/uk_schools", query), undefined, { method: "GET" })
  },
  schoolsWithoutUkSchoolIndex(
    request,
    query: {
      page?: number
      query?: string
    } = {},
  ) {
    return request<{
      records: Iv2School[]
      meta: IIndexMeta
    }>(buildUrlWithQueryString("/uk_schools/schools_without_assignment", query), undefined, {
      method: "GET",
    })
  },
  schoolUpdate(request, id: Id, data: Partial<Iv2School & { uk_school_id: Id }>) {
    return request<{ saved: true; record: Iv2School } | { saved: false; errors: string[] }>(
      `/schools/${id}`,
      toFormData(data),
      { method: "PUT", formData: true },
    )
  },
  rankingsAchievementPoints(request, params: IRankingParams) {
    return request<{
      records: {
        id: Id
        name: string
        points: number
      }[]
      meta: IIndexMeta
    }>(`/rankings/achievement_points`, params, { method: "GET" })
  },
  referralsFor(request, payload: { page?: number; query?: string; id: Id }) {
    return request<{ records: { id: string; name: string; count: number }[]; meta: IIndexMeta }>(
      `/referrals/referrals_for`,
      payload,
      { method: "GET" },
    )
  },
  careerProfileUserStats(request) {
    return request<{ total: number; online: number }>(
      "/users/career_profile_user_stats",
      {},
      { method: "GET" },
    )
  },
  careerVacanciesRelatedCareers(request, id: Id) {
    return request<{
      records: Iv2Career[]
      meta: IIndexMeta
    }>(`/career_vacancies/${id}/related_careers`, {}, { method: "GET" })
  },
  careerVacanciesRelatedCourses(request, id: Id) {
    return request<{
      records: Iv2CareerCourse[]
      meta: IIndexMeta
    }>(`/career_vacancies/${id}/related_courses`, {}, { method: "GET" })
  },
  careerVacanciesRelatedVacancies(request, id: Id) {
    return request<{
      records: Iv2CareerVacancy[]
      meta: IIndexMeta
    }>(`/career_vacancies/${id}/related_vacancies`, {}, { method: "GET" })
  },
  careerVacanciesRelatedEvents(request, id: Id) {
    return request<{
      records: Iv2Event[]
      meta: IIndexMeta
    }>(`/career_vacancies/${id}/related_events`, {}, { method: "GET" })
  },
  careerVacanciesRelatedLessonTemplates(request, id: Id) {
    return request<{
      records: Iv2LessonTemplate[]
      meta: IIndexMeta
    }>(`/career_vacancies/${id}/related_lesson_templates`, {}, { method: "GET" })
  },
  careerVacancyCategories(request, params?: { query?: string }) {
    return request<{ categories: string[] }>(`/career_vacancies/vacancy_categories`, params, {
      method: "GET",
    })
  },
  careerVacanciesIndex(
    request,
    query: {
      ids?: Id[]
      page?: number
      query?: string
      salaryMin?: string
      salaryMax?: string
      categories?: string
      organisations?: string
      sort?: string
      organisationId?: Id | null
      unpaginated?: boolean
    } = {},
  ) {
    return request<{
      records: Iv2CareerVacancy[]
      meta: IIndexMeta
      salary_range: { max_salary: number; min_salary: number } | null
    }>(buildUrlWithQueryString("/career_vacancies", query), undefined, { method: "GET" })
  },
  careerVacanciesShow(request, id: Id) {
    return request<Iv2CareerVacancy>(`/career_vacancies/${id}`, {}, { method: "GET" })
  },
  careerVacanciesDelete(request, id: Id) {
    return request<{ success?: boolean; error?: string }>(
      `/career_vacancies/${id}`,
      {},
      { method: "DELETE" },
    )
  },
  careerVacanciesUpdate(request, id: Id, data: Partial<Iv2CareerVacancy>) {
    return request<{ saved: true; record: Iv2CareerVacancy } | { saved: false; errors: string[] }>(
      `/career_vacancies/${id}`,
      toFormData(data),
      { method: "PUT", formData: true },
    )
  },
  careerVacanciesCreate(request, data: Partial<Iv2CareerVacancy>) {
    return request<{ saved: true; record: Iv2CareerVacancy } | { saved: false; errors: string[] }>(
      `/career_vacancies`,
      data,
      { method: "POST" },
    )
  },
  careerCoursesRelatedCareers(request, id: Id) {
    return request<{
      records: Iv2Career[]
      meta: IIndexMeta
    }>(`/career_courses/${id}/related_careers`, {}, { method: "GET" })
  },
  careerCoursesRelatedCourses(request, id: Id) {
    return request<{
      records: Iv2CareerCourse[]
      meta: IIndexMeta
    }>(`/career_courses/${id}/related_courses`, {}, { method: "GET" })
  },
  careerCoursesRelatedVacancies(request, id: Id) {
    return request<{
      records: Iv2CareerVacancy[]
      meta: IIndexMeta
    }>(`/career_courses/${id}/related_vacancies`, {}, { method: "GET" })
  },
  careerCoursesRelatedEvents(request, id: Id) {
    return request<{
      records: Iv2Event[]
      meta: IIndexMeta
    }>(`/career_courses/${id}/related_events`, {}, { method: "GET" })
  },
  careerCoursesRelatedLessonTemplates(request, id: Id) {
    return request<{
      records: Iv2LessonTemplate[]
      meta: IIndexMeta
    }>(`/career_courses/${id}/related_lesson_templates`, {}, { method: "GET" })
  },
  careerCoursesIndex(
    request,
    query: {
      ids?: Id[]
      page?: number
      query?: string
      organisations?: string
      hideStarted?: boolean
      sort?: string
      organisationId?: Id | null
      unpaginated?: boolean
    } = {},
  ) {
    return request<{
      records: Iv2CareerCourse[]
      meta: IIndexMeta
    }>(buildUrlWithQueryString("/career_courses", query), undefined, { method: "GET" })
  },
  careerCoursesShow(request, id: Id) {
    return request<Iv2CareerCourse>(`/career_courses/${id}`, {}, { method: "GET" })
  },
  careerCoursesDelete(request, id: Id) {
    return request<{ success?: boolean; error?: string }>(
      `/career_courses/${id}`,
      {},
      { method: "DELETE" },
    )
  },
  careerCoursesUpdate(request, id: Id, data: Partial<Iv2CareerCourse>) {
    return request<{ saved: true; record: Iv2CareerCourse } | { saved: false; errors: string[] }>(
      `/career_courses/${id}`,
      toFormData(data),
      { method: "PUT", formData: true },
    )
  },
  careerCoursesCreate(request, data: Partial<Iv2CareerCourse>) {
    return request<{ saved: true; record: Iv2CareerCourse } | { saved: false; errors: string[] }>(
      `/career_courses`,
      data,
      { method: "POST" },
    )
  },
  organisationRelatedCareers(request, id: Id) {
    return request<Iv2Career[]>(`/organisations/${id}/related_careers`, undefined, {
      method: "GET",
    })
  },
  organisationsIndex(
    request,
    query: {
      ids?: Id[]
      page?: number
      query?: string
      scope?: string
      sort?: string
      white_labelled?: boolean
      sponsor?: boolean
      per_page?: number
      has_image?: boolean
    } = {},
  ) {
    return request<{
      records: Iv2Organisation[]
      meta: IIndexMeta
    }>(buildUrlWithQueryString("/organisations", query), undefined, { method: "GET" })
  },
  organisationsShow(request, id: Id, opts?: { is_sponsor?: boolean }) {
    return request<Iv2Organisation>(`/organisations/${id}`, opts, { method: "GET" })
  },
  organisationsDelete(request, id: Id) {
    return request<{ success?: boolean; error?: string }>(
      `/organisations/${id}`,
      {},
      { method: "DELETE" },
    )
  },
  organisationsUpdate(request, id: Id, data: Partial<Iv2Organisation>) {
    return request<{ saved: true; record: Iv2Organisation } | { saved: false; errors: string[] }>(
      `/organisations/${id}`,
      toFormData(data),
      { method: "PUT", formData: true },
    )
  },
  organisationsCreate(request, data: Partial<Iv2Organisation>) {
    return request<{ saved: true; record: Iv2Organisation } | { saved: false; errors: string[] }>(
      `/organisations`,
      data,
      { method: "POST" },
    )
  },
  sendVideoError(request, data: { videoId: string; url: string; detail?: string }) {
    return request<{ sent: true; error?: never } | { sent: false; error?: string }>(
      `/slack/video_error`,
      data,
      { method: "POST" },
    )
  },
  lessonTemplatePrimaryUnit(request, id: Id) {
    return request<Iv2Unit | undefined>(
      `/lesson_templates/${id}/primary_unit_for`,
      {},
      { method: "GET" },
    )
  },
  usersGetProvider(request, id: Id) {
    return request<{ provider: string }>(`/users/${id}/user_provider`, {}, { method: "GET" })
  },
  updateUsersProviderToEmail(request, id: Id) {
    return request<{ saved: boolean }>(
      `/users/${id}/update_user_provider_to_email`,
      {},
      { method: "POST" },
    )
  },
  ////////////
  campaignUnitsIndex(
    request,
    params: {
      query?: string
      campaignId?: Id
      organisationId?: Id
      unpaginated?: boolean
      ids?: Id[]
    },
  ) {
    return request<{ records: Iv2CampaignUnit[]; meta: IIndexMeta }>("/campaign_units", params, {
      method: "GET",
    })
  },
  campaignUnitsShow(request, id: Id) {
    return request<Iv2CampaignUnit>(`/campaign_units/${id}`, {}, { method: "GET" })
  },
  campaignUnitsCreate(
    request,
    data: {
      campaign_id: Id
      videos2_id?: Id
      quiz_id?: Id | null
      external_url?: string
      new_library_unit_ids?: Id[]
      body?: string
      show_on_unit_page?: boolean
    },
  ) {
    return request<
      { saved: true; record: Iv2CampaignUnit; errors?: never } | { errors: string[]; saved: false }
    >(`/campaign_units`, data, { method: "POST" })
  },
  campaignUnitsUpdate(
    request,
    id: Id,
    data: {
      campaign_id: Id
      videos2_id?: Id | null
      quiz_id?: Id | null
      external_url?: string
      new_library_unit_ids?: Id[]
      body?: string
      tour_id?: Id
      lessonTemplateIds?: Id[]
      show_on_unit_page?: boolean
    },
  ) {
    return request<
      { saved: true; record: Iv2CampaignUnit; errors?: never } | { errors: string[]; saved: false }
    >(`/campaign_units/${id}`, data, { method: "PUT" })
  },
  videosIndex(
    request,
    params: { query?: string; campaignId?: Id; organisationId?: Id; unpaginated?: boolean },
  ) {
    return request<{ records: Iv2Video[]; meta: IIndexMeta }>("/videos", params, { method: "GET" })
  },
  videosShow(request, id: Id) {
    return request<Iv2Video>(`/videos/${id}`, {}, { method: "GET" })
  },
  videosCreate(
    request,
    data: { name: string; fileboyVideoId?: string; campaign_id?: Id; video_url?: string },
  ) {
    return request<
      { saved: true; record: Iv2Video; errors?: never } | { errors: string[]; saved: false }
    >(`/videos`, data, { method: "POST" })
  },
  campaignLessonIndex(
    request,
    params: {
      query?: string
      templateId?: Id
      campaignId?: Id
      organisationId?: Id
      unpaginated?: boolean
      campaignUnitId?: Id
      newLibraryUnitId?: Id
      ids?: Id[]
    },
  ) {
    return request<{ records: Iv2CampaignLesson[]; meta: IIndexMeta }>(
      buildUrlWithQueryString("/campaign_lessons", params),
      undefined,
      {
        method: "GET",
      },
    )
  },
  campaignUnitViewCreate(
    request,
    data: {
      user_id?: Id
      campaign_unit_id: Id
    },
  ) {
    return request<Iv2CampaignUnitView>("/campaign_unit_views", data, {
      method: "POST",
    })
  },
  videoViewsCreateForCurrentUser(
    request,
    data: {
      slideId: Id
      timeViewingSeconds: number
      resource_type?: "CareerVacancy" | "CareerCourse" | "Event" | "Organisation" | "Lesson::Slide"
      resource_id?: Id
    },
  ) {
    return request<Iv2VideoViews>("/video_views/create_for_current_user", data, {
      method: "POST",
    })
  },
  videoViewCreate(
    request,
    data: {
      time_viewing_seconds: number
      user_id?: Id
      videos2_id: Id
      resource_id?: Id
      resource_type?:
        | "CareerVacancy"
        | "CareerCourse"
        | "Event"
        | "Organisation"
        | "Lesson::Slide"
        | "Article"
    },
  ) {
    return request<Iv2VideoViews>("/video_views", data, {
      method: "POST",
    })
  },
  campaignVacancyViewCreate(
    request,
    data: {
      user_id?: Id
      career_vacancy_id: Id
    },
  ) {
    return request<Iv2CampaignVacancyView>("/campaign_vacancy_views", data, {
      method: "POST",
    })
  },
  campaignCourseViewCreate(
    request,
    data: {
      user_id?: Id
      career_course_id: Id
    },
  ) {
    return request<Iv2CampaignCourseView>("/campaign_course_views", data, {
      method: "POST",
    })
  },
  campaignUnitExternalClick(request, params: { user_id?: Id; campaign_unit_id: Id }) {
    return request<{ saved: boolean }>(`/campaign_unit_external_clicks`, params, {
      method: "POST",
    })
  },
  organisationProfileView(request, params: { organisation_id: Id; user_id?: Id }) {
    return request<{ saved: boolean }>(`/profile_views`, params, {
      method: "POST",
    })
  },
  organisationExternalLinkClick(request, params: { organisation_id: Id; user_id?: Id }) {
    return request<{ saved: boolean }>(`/profile_external_clicks`, params, {
      method: "POST",
    })
  },
  videoViewsStats(
    request,
    params: {
      startDate?: Date
      endDate?: Date
      campaignId?: Id
      organisationId?: Id
      videoIds?: Id[]
      countryId?: Id
      region?: string
      gender?: string
      ethnicity?: string
      category?: string
    },
  ) {
    return request<Iv2VideoViews[]>(
      buildUrlWithQueryString("/video_views/view_statistics", params),
      {},
      { method: "GET" },
    )
  },
  courseViewsStats(
    request,
    params: {
      startDate?: Date
      endDate?: Date
      campaignId?: Id
      organisationId?: Id
      careerCourseIds?: Id[]
      countryId?: Id
      region?: string
      gender?: string
      ethnicity?: string
      category?: string
    },
  ) {
    return request<Iv2CampaignCourseView[]>(
      buildUrlWithQueryString("/campaign_course_views/view_statistics", params),
      {},
      { method: "GET" },
    )
  },
  courseExternalLinksStats(
    request,
    params: {
      startDate?: Date
      endDate?: Date
      campaignId?: Id
      organisationId?: Id
      careerCourseIds?: Id[]
      countryId?: Id
      region?: string
      gender?: string
      ethnicity?: string
      category?: string
    },
  ) {
    return request<Iv2CampaignCourseExternalClick[]>(
      buildUrlWithQueryString("/campaign_course_external_clicks/link_statistics", params),
      {},
      { method: "GET" },
    )
  },
  vacancyViewsStats(
    request,
    params: {
      startDate?: Date
      endDate?: Date
      campaignId?: Id
      organisationId?: Id
      careerVacancyIds?: Id[]
      countryId?: Id
      region?: string
      gender?: string
      ethnicity?: string
      category?: string
    },
  ) {
    return request<Iv2CampaignVacancyView[]>(
      buildUrlWithQueryString("/campaign_vacancy_views/view_statistics", params),
      {},
      { method: "GET" },
    )
  },
  vacancyExternalLinkStats(
    request,
    params: {
      startDate?: Date
      endDate?: Date
      campaignId?: Id
      organisationId?: Id
      careerVacancyIds?: Id[]
      countryId?: Id
      region?: string
      gender?: string
      ethnicity?: string
      category?: string
    },
  ) {
    return request<Iv2CampaignVacancyExternalClick[]>(
      buildUrlWithQueryString("/campaign_vacancy_external_clicks/link_statistics", params),
      {},
      { method: "GET" },
    )
  },
  unitViewsStats(
    request,
    params: {
      startDate?: Date
      endDate?: Date
      campaignId?: Id
      organisationId?: Id
      newLibraryUnitIds?: Id[]
      countryId?: Id
      region?: string
      gender?: string
      ethnicity?: string
      category?: string
    },
  ) {
    return request<Iv2CampaignUnitView[]>(
      buildUrlWithQueryString("/campaign_unit_views/view_statistics", params),
      {},
      { method: "GET" },
    )
  },
  unitExternalLinkStats(
    request,
    params: {
      startDate?: Date
      endDate?: Date
      campaignId?: Id
      organisationId?: Id
      newLibraryUnitIds?: Id[]
      countryId?: Id
      region?: string
      gender?: string
      ethnicity?: string
      category?: string
    },
  ) {
    return request<Iv2CampaignUnitExternalClick[]>(
      buildUrlWithQueryString("/campaign_unit_external_clicks/link_statistics", params),
      {},
      { method: "GET" },
    )
  },
  campaignUnitsUnitsIndex(
    request,
    params: {
      campaignId?: Id
      query?: string
      organisationId?: Id
      unpaginated?: boolean
      campaignUnitIds?: Id[]
    },
  ) {
    return request<{ meta: IIndexMeta; records: Iv2Unit[] }>(
      buildUrlWithQueryString(`/campaign_units/units_index`, params),
      {},
      { method: "GET" },
    )
  },
  unitDemographicsViewsData(
    request,
    params: {
      campaignId?: Id
      organisationId?: Id
      type: "ethnicity" | "gender" | "region"
    },
  ) {
    return request<Record<string, number>>(
      buildUrlWithQueryString("/campaign_unit_views/demographics_data", params),
      {},
      { method: "GET" },
    )
  },
  courseDemographicsViewsData(
    request,
    params: {
      campaignId?: Id
      organisationId?: Id
      type: "ethnicity" | "gender" | "region"
    },
  ) {
    return request<Record<string, number>>(
      buildUrlWithQueryString("/campaign_course_views/demographics_data", params),
      {},
      { method: "GET" },
    )
  },
  vacancyDemographicsViewsData(
    request,
    params: {
      campaignId?: Id
      organisationId?: Id
      type: "ethnicity" | "gender" | "region"
    },
  ) {
    return request<Record<string, number>>(
      buildUrlWithQueryString("/campaign_vacancy_views/demographics_data", params),
      {},
      { method: "GET" },
    )
  },
  eventDemographicsViewsData(
    request,
    params: {
      campaignId?: Id
      organisationId?: Id
      type: "ethnicity" | "gender" | "region"
    },
  ) {
    return request<Record<string, number>>(
      buildUrlWithQueryString("/campaign_event_views/demographics_data", params),
      {},
      { method: "GET" },
    )
  },
  videoDemographicsViewsData(
    request,
    params: {
      campaignId?: Id
      organisationId?: Id
      type: "ethnicity" | "gender" | "region"
    },
  ) {
    return request<Record<string, number>>(
      buildUrlWithQueryString("/video_views/demographics_data", params),
      {},
      { method: "GET" },
    )
  },
  tourDemographicsViewsData(
    request,
    params: {
      campaignId?: Id
      organisationId?: Id
      type: "ethnicity" | "gender" | "region"
    },
  ) {
    return request<Record<string, number>>(
      buildUrlWithQueryString("/tour_views/demographics_data", params),
      {},
      { method: "GET" },
    )
  },
  quizDemographicsViewsData(
    request,
    params: {
      campaignId?: Id
      organisationId?: Id
      type: "ethnicity" | "gender" | "region"
    },
  ) {
    return request<Record<string, number>>(
      buildUrlWithQueryString("/quiz_views/demographics_data", params),
      {},
      { method: "GET" },
    )
  },
  quizIndex(
    request,
    query: {
      page?: number
      query?: string
      organisationId?: Id
      campaignId?: Id
      unpaginated?: boolean
    } = {},
  ) {
    return request<{ records: Iv2Quiz[]; meta: IIndexMeta }>("/quizzes", query, {
      method: "GET",
    })
  },
  quizCreate(request, data: { name: string; noodle_quiz_key: string; campaign_id?: Id }) {
    return request<{ saved: true; record: Iv2Quiz } | { saved: false; errors: string[] }>(
      "/quizzes",
      data,
      {
        method: "POST",
      },
    )
  },
  quizUpdate(request, id: Id, data: { name: string; noodle_quiz_key: string; campaign_id?: Id }) {
    return request<{ saved: true; record: Iv2Quiz } | { saved: false; errors: string[] }>(
      `/quizzes/${id}`,
      data,
      {
        method: "PUT",
      },
    )
  },
  quizShow(request, id: Id) {
    return request<Iv2Quiz>(
      `/quizzes/${id}`,
      {},
      {
        method: "GET",
      },
    )
  },
  tourIndex(
    request,
    query: {
      page?: number
      query?: string
      organisationId?: Id
      campaignId?: Id
      unpaginated?: boolean
      availableToPupils?: boolean
    } = {},
  ) {
    return request<{ records: Iv2Tour[]; meta: IIndexMeta }>("/tours", query, {
      method: "GET",
    })
  },
  tourShow(request, id: Id) {
    return request<Iv2Tour>(
      `/tours/${id}`,
      {},
      {
        method: "GET",
      },
    )
  },
  tourViewsStats(
    request,
    data: {
      startDate?: Date
      endDate?: Date
      campaignId?: Id
      organisationId?: Id
      tourIds?: Id[]
      countryId?: Id
      region?: string
      gender?: string
      ethnicity?: string
      category?: string
    },
  ) {
    return request<Iv2TourView[]>(
      buildUrlWithQueryString("/tour_views/view_statistics", data),
      {},
      { method: "GET" },
    )
  },
  quizViewsStats(
    request,
    data: {
      startDate?: Date
      endDate?: Date
      campaignId?: Id
      organisationId?: Id
      quizIds?: Id[]
      countryId?: Id
      region?: string
      gender?: string
      ethnicity?: string
      category?: string
    },
  ) {
    return request<Iv2QuizView[]>(
      buildUrlWithQueryString("/quiz_views/view_statistics", data),
      {},
      { method: "GET" },
    )
  },
  userLessonTemplateStats(request) {
    return request<Record<string, { count: number; max: string }>>(
      "/debug/user_lesson_template_stats",
      {},
      { method: "GET" },
    )
  },
  ///////////////////
  canSyncSchoolHubspot(request, schoolId: Id) {
    return request<[boolean, string[]]>(
      `/schools/${schoolId}/can_sync_hubspot`,
      {},
      { method: "GET" },
    )
  },
  createQuizView(request, data: { quiz_id: Id; user_id?: Id }) {
    return request<{ saved: boolean }>(`/quiz_views`, data, { method: "POST" })
  },
  schoolsMergeWithSchool(request, data: { id: Id; sourceId: Id }) {
    return request<{ success: boolean }>(
      `/schools/${data.id}/merge_with_school`,
      {
        other_school_id: data.sourceId,
      },
      { method: "POST" },
    )
  },
  campaignUnitUpdateLessonTemplates(
    request,
    data: { unitId: Id; data: { [key: string]: Id[] }; campaignId: Id; campaignUnitId: Id },
  ) {
    return request<{ saved: boolean }>(
      `/campaign_units/${data.unitId}/update_lesson_templates`,
      { data: data.data, campaignId: data.campaignId, campaignUnitId: data.campaignUnitId },
      { method: "POST" },
    )
  },
  adminDashboardSchoolEngagementStats(request) {
    return request<{
      active7Days: Record<string, number>
      fullActive7Days: Record<string, number>
      lessThan3: Record<string, number>
      lessThan3Active: Record<string, Iv2School[]>
    }>(`/admin_dashboard/school_engagement_stats`, undefined, { method: "GET" })
  },
  adminDashboardSchoolTeacherEngagementStats(request, params: { page: number; scope?: string }) {
    return request<{
      records: Iv2SchoolTeacherEngagementStat[]
      meta: IIndexMeta
    }>(`/admin_dashboard/school_teacher_engagement_stats`, params, {
      method: "GET",
    })
  },
  schoolsSyncWonde(request, schoolId: Id) {
    return request<{ running: true }>(`/schools/${schoolId}/wonde_import`, {}, { method: "GET" })
  },
  schoolsRequestWondeAccess(request, schoolId: Id) {
    return request<{ message: string }>(
      `/schools/${schoolId}/request_wonde_access`,
      {},
      { method: "GET" },
    )
  },
  schoolsLastWondeImport(request, schoolId: Id) {
    return request<Iv2WondeImport | null>(
      `/schools/${schoolId}/last_wonde_import`,
      {},
      { method: "GET" },
    )
  },
  schoolsLatestWondeImport(request, schoolId: Id) {
    return request<Iv2WondeImport | null>(
      `/schools/${schoolId}/latest_wonde_import`,
      {},
      { method: "GET" },
    )
  },
  wondeImportFormsIndex(
    request,
    wondeImportId: Id,
    query: { query?: string; page?: number; ids?: Id[] },
  ) {
    return request<{ records: Iv2WondeImportForm[]; meta: IIndexMeta }>(
      buildUrlWithQueryString(`/wonde_import/${wondeImportId}/wonde_import_forms`, query),
      undefined,
      { method: "GET" },
    )
  },
  wondeImportTeachersIndex(request, wondeImportId: Id, query: { query?: string; page?: number }) {
    return request<{ records: Iv2WondeImportTeacher[]; meta: IIndexMeta }>(
      `/wonde_import/${wondeImportId}/wonde_import_teachers`,
      query,
      { method: "GET" },
    )
  },
  wondeImportPupilsIndex(request, wondeImportId: Id, query: { query?: string; page?: number }) {
    return request<{ records: Iv2WondeImportPupil[]; meta: IIndexMeta }>(
      `/wonde_import/${wondeImportId}/wonde_import_pupils`,
      query,
      { method: "GET" },
    )
  },
  wondeSaveTeacherChanges(
    request,
    wondeImportId: Id,
    data: {
      email?: string
      user_id?: Id | null
      recordId: Id
    },
  ) {
    return request<Record<string, any>>(
      `wonde_import/${wondeImportId}/save_teacher_changes`,
      data,
      {
        method: "POST",
      },
    )
  },
  wondeSaveImport(
    request,
    wondeImportId: Id,
    archive: { forms?: boolean; teachers?: boolean; pupils?: boolean },
  ) {
    return request<{
      saved: boolean
      errors: {
        pupils: { data: Record<string, any>; messages: Record<string, string[]> }[]
        forms: { data: Record<string, any>; messages: Record<string, string[]> }[]
        teachers: { data: Record<string, any>; messages: Record<string, string[]> }[]
        fatal: [string]
      }
      warnings: {
        pupils: { data: Record<string, any>; messages: Record<string, string[]> }[]
        forms: { data: Record<string, any>; messages: Record<string, string[]> }[]
        teachers: { data: Record<string, any>; messages: Record<string, string[]> }[]
      }
    }>(`/wonde_import/${wondeImportId}/save_import`, { archive }, { method: "POST" })
  },
  wondeImportUpdate(request, id: Id, data: { import_status: Iv2WondeImport["import_status"] }) {
    return request<
      { success: true; record: Iv2WondeImport } | { success: false; errors: string[] }
    >(`/wonde_import/${id}`, data, { method: "PUT" })
  },
  wondeImportTeacherIds(request, id: Id) {
    return request<{ ids: Id[] }>(`/wonde_import/${id}/wonde_import_teacher_ids`, undefined, {
      method: "GET",
    })
  },
  hubspotChatVerifyVisitor(request) {
    return request<{ token: string; email: string } | { error: { message: string } }>(
      "/users/hubspot_visitor_verification",
      undefined,
      { method: "GET" },
    )
  },
  wondeValidateTeachers(request, id: Id) {
    return request<{ invalid_teachers: Iv2WondeImportTeacher[] }>(
      `/wonde_import/${id}/wonde_teachers_valid`,
      undefined,
      {
        method: "GET",
      },
    )
  },
  toggleLessonTaught(request, id: Id, state?: boolean) {
    return request<Iv2LessonLesson>(`/lessons/${id}/toggle_taught`, { state }, { method: "POST" })
  },
  lessonTemplateRecommendedCareers(request, id: Id) {
    return request<Iv2Career[]>(
      `/lesson_templates/${id}/recommended_careers`,
      {},
      { method: "GET" },
    )
  },
  pupilQRSignInLink(request, id: Id) {
    return request<{ qr_code: string }>(`/pupils/${id}/qr_signin_link`, {}, { method: "GET" })
  },
  adminCreateEmployer(
    request,
    data: { organisation_id: Id; name: string; password: string; email: string },
  ) {
    return request<ICreateRequest<Iv2User>>("users/create_employer", data, { method: "POST" })
  },
  lessonPlanViewed(request, templateId: Id) {
    return request<{ saved: true; record: { id: Id } } | { saved: false; error: string }>(
      "/lesson_plan_views/create_for_self",
      { lesson_template_id: templateId },
      { method: "POST" },
    )
  },
  lessonTemplateUpdate(
    request,
    id: Id,
    data: {
      name?: string
      is_ks4_lesson_plan?: boolean
      use_2022_lesson_plan?: boolean
      intent?: string
      new_lesson_plan_resources?: string
      implementation?: string
      impact_assessment?: string
      use_new_presentation?: boolean
    },
  ) {
    return request<{ saved: true; record: Iv2LessonTemplate } | { saved: false; errors: string[] }>(
      `/lesson_templates/${id}`,
      data,
      { method: "PUT" },
    )
  },
  quipQuestionsIndex(
    request,
    query: {
      page?: number
      query?: string
      ids?: Id[]
    } = {},
  ) {
    return request<{
      records: IV2QuipQuestion[]
      meta: IIndexMeta
    }>(buildUrlWithQueryString("/quip_questions", query), undefined, { method: "GET" })
  },
  quipQuestionsShow(request, id: Id) {
    return request<IV2QuipQuestion>(`/quip_questions/${id}`, {}, { method: "GET" })
  },
  quipQuestionsUpdate(request, id: Id, data: Partial<IV2QuipQuestion>) {
    return request<{ saved: true; record: IV2QuipQuestion } | { saved: false; errors: string[] }>(
      `/quip_questions/${id}`,
      toFormData(data),
      { method: "PUT", formData: true },
    )
  },
  quipQuestionsCreate(request, data: Partial<IV2QuipQuestion>) {
    return request<{ saved: true; record: IV2QuipQuestion } | { saved: false; errors: string[] }>(
      `/quip_questions`,
      toFormData(data),
      { method: "POST", formData: true },
    )
  },
  slidesForEndOfUnitAssessment(request, unitId: Id) {
    return request<Iv2LessonSlide[]>(
      `/lesson_slides/for_end_of_unit_assessment`,
      { unitId },
      { method: "GET" },
    )
  },
  lessonPlanTemplates(request, ids: Id[]) {
    return request<(Iv2LessonTemplate & { keywords: Iv2Keyword[] })[]>(
      `/lesson_templates/new_lesson_template_plans_index`,
      { ids },
      { method: "POST" },
    )
  },
  hubspotSyncsStats(request) {
    return request<IHubspotSyncRecord[]>("/hubspot_syncs", {}, { method: "GET" })
  },
  presentationsIndex(
    request,
    query: { ids?: Id[]; page?: number; query?: string; lessonTemplateId?: Id } = {},
  ) {
    return request<{
      records: Iv2Presentation[]
      meta: IIndexMeta
    }>(buildUrlWithQueryString("/presentations", query), {}, { method: "GET" })
  },
  presentationsShow(request, id: Id) {
    return request<Iv2Presentation & { error?: string }>(
      `/presentations/${id}`,
      {},
      { method: "GET" },
    )
  },
  presentationsShowWithReviews(
    request,
    id: string,
    query?: { with_feedback?: boolean; rating?: number },
  ) {
    return request<Iv2Presentation & { reviews: Iv2PresentationFeedback[] }>(
      `/presentations/${id}/with_reviews`,
      query,
      { method: "GET" },
    )
  },
  presentationsCreate(request, data: any) {
    return request<
      { success: true; record: Iv2Presentation } | { success: false; errors: string[] }
    >("/presentations/create", data)
  },
  presentationsUpdate(request, id: Id, data: any) {
    return request<
      { success: true; record: Iv2Presentation } | { success: false; errors: string[] }
    >(`/presentations/${id}/update`, data)
  },
  presentationsSetPublishedTo(request, id: Id, published: IPresentationPublishedState) {
    return request<{ published: keyof typeof IPresentationPublishedState }>(
      `/presentations/${id}/set_published_to`,
      { published },
    )
  },
  presentationsLeaveFeedback(request, id: Id, data: { feedback: string; rating: number }) {
    return request<
      { saved: true; record: Iv2PresentationFeedback } | { saved: false; errors: string[] }
    >(`/presentations/${id}/leave_review`, data)
  },
  generatePresentationWithAi(request, id: Id) {
    return request<{ success: boolean }>(
      `/lessons/${id}/generate_presentation_with_ai`,
      {},
      { method: "POST" },
    )
  },
  generateKeywordsWithAi(request, id: Id) {
    return request<{ success: boolean }>(
      `/lessons/${id}/generate_keywords_with_ai`,
      {},
      { method: "POST" },
    )
  },
  generateKeywordFromName(request, lessonId: Id, name: string) {
    return request<{ body: string; fileboy_image_id: string | null; question: string }>(
      `/lessons/${lessonId}/generate_keyword_from_name`,
      { name },
      { method: "POST" },
    )
  },
  hubspotContactSync(request, id: Id) {
    return request<any>(`/users/${id}/hubspot_contact_sync`)
  },
  templateVersions(request, templateId: Id) {
    return request<
      { name: string; id: Id; user?: { name: string }; is_default: boolean; is_source: boolean }[]
    >(
      `/user_lesson_templates/${templateId}/template_versions`,
      {},
      {
        method: "GET",
      },
    )
  },
  lessonKeywordsIndex(request, query?: { template_id: Id; with_question_data?: boolean }) {
    return request<{ records: Iv2Keyword[]; meta: IIndexMeta }>("/lesson_keywords", query, {
      method: "GET",
    })
  },
  lessonKeywordsUpdate(request, id: Id, data: any) {
    return request<{ saved: true; record: Iv2Keyword } | { saved: false; errors: string[] }>(
      `/lesson_keywords/${id}`,
      data,
      { method: "PUT" },
    )
  },
  lessonKeywordsCreate(request, data: any) {
    return request<{ saved: true; record: Iv2Keyword } | { saved: false; errors: string[] }>(
      `/lesson_keywords`,
      data,
      { method: "POST" },
    )
  },
  lessonKeywordsUpdateOrder(request, data: { keyword_ids: Id[] }) {
    return request(`/lesson_keywords/update_order`, data, { method: "POST" })
  },
  lessonKeywordsDelete(request, id: Id) {
    return request<{ success?: boolean; error?: string }>(
      `/lesson_keywords/${id}`,
      {},
      { method: "DELETE" },
    )
  },
  lessonDocumentsIndex(request, query?: { template_id: Id }) {
    return request<{ records: Iv2Document[]; meta: IIndexMeta }>("/lesson_documents", query, {
      method: "GET",
    })
  },
  lessonDocumentsUpdate(request, id: Id, data: any) {
    return request<{ saved: true; record: Iv2Document } | { saved: false; errors: string[] }>(
      `/lesson_documents/${id}`,
      data,
      { method: "PUT" },
    )
  },
  lessonDocumentsCreate(request, data: any) {
    return request<{ saved: true; record: Iv2Document } | { saved: false; errors: string[] }>(
      `/lesson_documents`,
      data,
      { method: "POST" },
    )
  },
  lessonDocumentsDelete(request, id: Id) {
    return request<{ success?: boolean; error?: string }>(
      `/lesson_documents/${id}`,
      {},
      { method: "DELETE" },
    )
  },
  authorsIndex(
    request,
    query?: {
      template_id?: Id
      template_ids?: Id[]
      user_id?: Id
      organisation_id?: Id
      has_user?: boolean
    },
  ) {
    return request<{ records: Iv2Author[]; meta: IIndexMeta }>(
      buildUrlWithQueryString("/authors", query ?? {}),
      {},
      {
        method: "GET",
      },
    )
  },
  authorsUpdate(request, id: Id, data: any) {
    return request<{ saved: true; record: Iv2Author } | { saved: false; errors: string[] }>(
      `/authors/${id}`,
      data,
      { method: "PUT" },
    )
  },
  authorsCreate(request, data: any) {
    return request<{ saved: true; record: Iv2Author } | { saved: false; errors: string[] }>(
      `/authors`,
      data,
      { method: "POST" },
    )
  },
  myOrganisationAuthor(request) {
    return request<Iv2Author>(`/authors/my_organisation_author`, {}, { method: "GET" })
  },
  myAuthor(request) {
    return request<Iv2Author>("/authors/my_author", {}, { method: "GET" })
  },
  quipQuizzesShow(request, id: Id) {
    return request<IBuildQuipQuizData>(`/quip_quizzes/${id}`, {}, { method: "GET" })
  },
  quipQuizzesBuild(request, data: IBuildQuipQuizData) {
    return request<{ saved: true; record: IV2QuipQuestion } | { saved: false; errors: string[] }>(
      `/quip_quizzes/build_quiz`,
      toFormData(data),
      { method: "POST", formData: true },
    )
  },
  quipQuizzesUpdateBuild(request, id: Id, data: IBuildQuipQuizData) {
    return request<{ saved: true; record: IV2QuipQuestion } | { saved: false; errors: string[] }>(
      `/quip_quizzes/${id}/update_quiz`,
      toFormData(data),
      { method: "POST", formData: true },
    )
  },
  leaderboard2(request, opts: { type: "school" | "global" }) {
    type Row = { user_id: number; name: string | null; score: number; rank: number; me?: boolean }
    type Leaderboard = {
      rows: Row[]
      topRows?: Row[]
    }
    return request<Leaderboard>(`/leaderboards/leaderboard2`, opts, { method: "POST" })
  },
  myLeaderboard2(request, opts?: { date?: Date }) {
    return request<{ schoolRank: number | null; globalRank: number | null; points: number }>(
      `/leaderboards/my_leaderboard2`,
      opts,
      { method: "POST" },
    )
  },
  leaderboard2Dashboard(request) {
    type Leaderboard = { user_id: number; name: string | null; score: number; rank: number }[]
    return request<{ school: Leaderboard; teacher: Leaderboard }>(
      `/leaderboards/dashboard2`,
      {},
      { method: "POST" },
    )
  },
  getAnonLibraryUnit(request, id) {
    return request<ILibraryUnit>(`/new_library_units/${id}`, {}, { method: "GET" })
  },
  allCurriculaIndex(request) {
    return request<Iv2Curricula[]>("/new_library_curricula/all_curricula", undefined, {
      method: "GET",
    })
  },
  newLibraryCurriculumIndex(request, query: { unpaginated?: boolean; query?: string } = {}) {
    return request<{ records: Iv2Curricula[]; meta: IIndexMeta }>(
      buildUrlWithQueryString("/new_library_curricula", query),
      undefined,
      { method: "GET" },
    )
  },
  newLibraryCurriculumShow(request, id: Id) {
    return request<Iv2Curricula>(`/new_library_curricula/${id}/show_data`, undefined, {
      method: "GET",
    })
  },
  newLibraryYearsShow(request, id: Id) {
    return request<Iv2Year>(`/new_library_years/${id}`, undefined, { method: "GET" })
  },
  newLibraryYearsIndex(
    request,
    query: { curriculum_id?: string; unpaginated?: boolean; query?: string } = {},
  ) {
    return request<{ records: Iv2Year[]; meta: IIndexMeta }>(
      buildUrlWithQueryString("/new_library_years", query),
      undefined,
      { method: "GET" },
    )
  },
  schoolsSubscriptionStats(request) {
    return request<{
      date_order: string[]
      data: { name: string } & { [key: string]: number | null }[]
    }>("/schools/schools_subscriptions_stats", {}, { method: "GET" })
  },
  questionnaireIndex(request, options?: { unpaginated?: boolean; query?: string; page: number }) {
    return request<{ meta: IIndexMeta; records: IQuestionnaire[] }>(
      `/questionnaires`,
      options ?? {},
      { method: "GET" },
    )
  },
  questionnaireTake(request, data: { id?: Id; is_onboarding_questionnaire?: boolean }) {
    return request<{
      questionnaire: IQuestionnaire
      questions: IQuestionnaireQuestionWithOptions[]
    } | null>(`/questionnaires/take`, data, { method: "POST" })
  },
  questionnaireDuplicate(request, id: Id) {
    return request<
      | { saved: true; message: "success"; record: IQuestionnaire }
      | { saved: false; message: "success"; error: string }
    >(`/questionnaires/${id}/duplicate`, {}, { method: "POST" })
  },
  questionnaireShow(request, id: Id) {
    return request<IQuestionnaire>(`/questionnaires/${id}`, {}, { method: "GET" })
  },
  questionnaireCreate(request, data: Partial<IQuestionnaire>) {
    return request<{ saved: true; record: IQuestionnaire } | { saved: false; error: string }>(
      `/questionnaires`,
      data ?? {},
      { method: "POST" },
    )
  },
  questionnaireUpdate(request, id: Id, data: Partial<IQuestionnaire>) {
    return request<{ saved: true; record: IQuestionnaire } | { saved: false; error: string }>(
      `/questionnaires/${id}`,
      data,
      { method: "PUT" },
    )
  },
  questionnaireDestroy(request, id: Id) {
    return request<{ saved: boolean; error: string }>(
      `/questionnaires/${id}`,
      {},
      {
        method: "DELETE",
      },
    )
  },
  questionnaireStatistics(request, id: Id, options?: {}) {
    return request<IQuestionnaireStats[]>(`/questionnaires/${id}/statistics`, options ?? {}, {
      method: "POST",
    })
  },
  questionnaireQuestionDemographicsStatistics(
    request,
    id: string,
    options?: {
      ethnicity?: string
      gender?: string
      age_range_start?: Date | null
      age_range_end?: Date | null
      date_from?: Date | null
      date_to?: Date | null
    },
  ) {
    return request<IQuestionnaireDemographicsStats[]>(
      `/questionnaire_questions/${id}/demographics_stats`,
      options ?? {},
      {
        method: "POST",
      },
    )
  },
  questionnaireQuestionDemographicsCounties(request, id: string) {
    return request<string[]>(
      `/questionnaire_questions/${id}/demographics_counties`,
      {},
      {
        method: "POST",
      },
    )
  },
  questionnaireQuestions(
    request,
    options?: { questions_only?: boolean; questionnaireId?: string },
  ) {
    return request<(IQuestionnaireQuestion & { options: IQuestionnaireOption<any>[] })[]>(
      `/questionnaire_questions`,
      options ?? {},
      { method: "GET" },
    )
  },
  questionnaireQuestionShow(request, id: Id) {
    return request<IQuestionnaireQuestion & { options: IQuestionnaireOption<any>[] }>(
      `/questionnaire_questions/${id}`,
      {},
      { method: "GET" },
    )
  },
  questionnaireQuestionDestroy(request, id: Id) {
    return request<{ saved: boolean }>(`/questionnaire_questions/${id}`, {}, { method: "DELETE" })
  },
  questionnaireQuestionsReorder(request, data: { id: Id; weight: number }[]) {
    return request<{ saved: boolean }>(
      `/questionnaire_questions/reorder`,
      { data },
      { method: "POST" },
    )
  },
  questionnaireQuestionUpdate(request, id: Id, data: Partial<IQuestionnaireQuestion>) {
    return request<IQuestionnaireQuestion>(`/questionnaire_questions/${id}`, data, {
      method: "PUT",
    })
  },
  questionnaireQuestionsCreate(request, data: Partial<IQuestionnaireQuestion>) {
    return request<
      { saved: true; record: IQuestionnaireQuestion } | { saved: false; error: string }
    >(`/questionnaire_questions`, data, {
      method: "POST",
    })
  },
  questionnaireOptionCreate(request, data: Partial<IQuestionnaireOption<any>>) {
    return request<
      { saved: false; message: string } | { saved: true; record: IQuestionnaireOption<any> }
    >(`/questionnaire_options`, data, { method: "POST" })
  },
  questionnaireOptionUpdate(request, id: Id, data: Partial<IQuestionnaireOption<any>>) {
    return request<
      { saved: false; message: string } | { saved: true; record: IQuestionnaireOption<any> }
    >(`/questionnaire_options/${id}`, data, { method: "PUT" })
  },
  questionnaireOptionDestroy(request, id: Id) {
    return request<{ saved: boolean; message: string }>(
      `/questionnaire_options/${id}`,
      {},
      { method: "DELETE" },
    )
  },
  questionnaireAnswersCreate(
    request,
    options: {
      session_id: string
      data: Partial<IQuestionnaireAnswer>[]
      questionnaire_question_id: Id
    },
  ) {
    return request<{ saved: true } | { saved: false; message: string }>(
      `/questionnaire_answers`,
      options,
      { method: "POST" },
    )
  },
  questionnaireTaken(request, id: Id) {
    return request<boolean>(`/questionnaires/${id}/taken_questionnaire`, {}, { method: "GET" })
  },
  questionnaireUserTook(request, id: Id) {
    return request<boolean>(`/questionnaires/${id}/user_took_questionnaire`, {}, { method: "POST" })
  },
  lessonTemplateFoldersTree(request) {
    return request<ILessonTemplateFolder[]>("/lesson_template_folders", {}, { method: "GET" })
  },
  lessonTemplateFoldersCreate(request, data: { name: string; parent_folder_id?: Id }) {
    return request<
      | { saved: false; message: string }
      | { saved: true; record: Omit<ILessonTemplateFolder, "templates" | "folders"> }
    >("/lesson_template_folders", data, { method: "POST" })
  },
  lessonTemplateFoldersUpdate(request, id: Id, data: { name: string; parent_folder_id?: Id }) {
    return request<
      | { saved: false; message: string }
      | { saved: true; record: Omit<ILessonTemplateFolder, "templates" | "folders"> }
    >(`/lesson_template_folders/${id}`, data, { method: "PUT" })
  },
  lessonTemplateFoldersDestroy(request, id: Id) {
    return request<any>(`/lesson_template_folders/${id}`, {}, { method: "DELETE" })
  },
  lessonTemplateFoldersAddTemplates(request, folderId: Id, templateIds: Id[]) {
    return request<{ saved: false; message: string } | { saved: true }>(
      `/lesson_template_folders/${folderId}/add_templates_to_folder`,
      { template_ids: templateIds },
      { method: "POST" },
    )
  },
  userLessonTemplateSetDefaultVersion(request, userTemplateId: Id) {
    return request<{ saved: boolean }>(
      `/user_lesson_templates/${userTemplateId}/set_as_default`,
      {},
      { method: "POST" },
    )
  },
  userLessonTemplateGetDefaultVersion(request, templateId: Id) {
    return request<Id | null>(
      `/user_lesson_templates/${templateId}/get_default`,
      {},
      { method: "GET" },
    )
  },
  userLessonTemplateDuplicate(request, id: Id) {
    return request<{ saved: boolean; record?: Iv2LessonTemplate; errors?: string[] }>(
      `/user_lesson_templates/${id}/duplicate`,
      {},
      { method: "POST" },
    )
  },
  userLessonTemplateDuplicateFiles(request, id: Id, templateIds: Id[]) {
    return request<{ saved: boolean; record?: Iv2LessonTemplate }>(
      `/user_lesson_templates/${id}/duplicate_files`,
      { template_ids: templateIds },
      { method: "POST" },
    )
  },
  userLessonTemplateQuipQuiz(request, id: Id) {
    return request<IBuildQuipQuizData>(
      `/user_lesson_templates/${id}/quip_quiz`,
      {},
      { method: "GET" },
    )
  },
  userLessonTemplatesPresentation(request, id: Id) {
    return request<Iv2Presentation>(`/user_lesson_templates/${id}/presentation`, undefined, {
      method: "GET",
    })
  },
  userLessonTemplatesDestroy(request, id: Id) {
    return request<{ saved: true; record: Iv2LessonTemplate } | { saved: false; errors: string[] }>(
      `/user_lesson_templates/${id}`,
      {},
      { method: "DELETE" },
    )
  },
  userLessonTemplatesCreate(request, data: { name: string }) {
    return request<{ saved: true; record: Iv2LessonTemplate } | { saved: false; errors: string[] }>(
      "/user_lesson_templates",
      data,
      { method: "POST" },
    )
  },
  userLessonTemplatesUpdate(
    request,
    id: Id,
    data: {
      name?: string
      fileboy_image_id?: string
      author_ids?: Id[]
      objectives?: string[]
      recommended_career_ids?: string[]
    },
  ) {
    return request<{ saved: true; record: Iv2LessonTemplate } | { saved: false; errors: string[] }>(
      `/user_lesson_templates/${id}`,
      data,
      { method: "PUT" },
    )
  },
  exemplarWorksIndex(
    request,
    params: {
      school_id?: Id
      user_id?: Id
      approver_id?: Id
      query?: string
      status?: IExemplarWorkStatus
      unit_id?: Id
      template_id?: Id
      page?: number
      per_page?: number
    },
  ) {
    return request<{ records: IExemplarWork[]; meta: IIndexMeta }>(`/exemplar_works`, params, {
      method: "GET",
    })
  },
  exemplarWorksShow(request, id: string) {
    return request<IExemplarWork>(`/exemplar_works/${id}`, {}, { method: "GET" })
  },
  exemplarWorkDelete(request, id: Id) {
    return request(`/exemplar_works/${id}`, {}, { method: "DELETE" })
  },
  userLessonTemplatesShow(request, id: Id) {
    return request<Iv2LessonTemplateShow>(`/user_lesson_templates/${id}`, {}, { method: "GET" })
  },
  exemplarWorksNew(request, data: Partial<IExemplarWork>) {
    return request<{ saved: true; record: IExemplarWork } | { saved: false; errors: string[] }>(
      "/exemplar_works",
      data,
      { method: "POST" },
    )
  },
  exemplarWorksUpdate(request, id: string, data: Partial<IExemplarWork>) {
    return request<{ saved: true; record: IExemplarWork } | { saved: false; errors: string[] }>(
      `/exemplar_works/${id}`,
      data,
      { method: "PUT" },
    )
  },
  userLessonTemplatesIndex(
    request,
    query: {
      page?: number
      query?: string
      unpaginated?: boolean
      order_recent?: boolean
      without_folder?: boolean
    } = {},
  ) {
    return request<{
      records: (Iv2LessonTemplate & { new_presentation_index_id?: Id })[]
      meta: IIndexMeta
    }>(buildUrlWithQueryString("/user_lesson_templates", query), undefined, {
      method: "GET",
    })
  },
  userLessonTemplatePublish(request, id: Id) {
    return request<{
      published: boolean
      saved: boolean
    }>(`/user_lesson_templates/${id}/publish`, {}, { method: "POST" })
  },
  userLessonTemplateUnpublish(request, id: Id) {
    return request<{
      published: boolean
      saved: boolean
    }>(`/user_lesson_templates/${id}/unpublish`, {}, { method: "POST" })
  },
  userLessonTemplatePresentation(request, id: Id) {
    return request<Iv2Presentation>(
      `/user_lesson_templates/${id}/presentation`,
      {},
      { method: "GET" },
    )
  },
  youtubeDocumentsCreate(request, data: { name: string; published: boolean; fileboy_id: string }) {
    return request("/live_stream_documents", data, { method: "POST" })
  },
  youtubeDocumentsUpdate(
    request,
    id: Id,
    data: { name: string; published: boolean; fileboy_id: string },
  ) {
    return request(`/live_stream_documents/${id}`, data, { method: "PUT" })
  },
  youtubeDocumentsDestroy(request, id: Id) {
    return request(`/live_stream_documents/${id}`, {}, { method: "DELETE" })
  },
  youtubeDocumentsReorder(request, data: { id: Id; weight: number }[]) {
    return request(`/live_stream_documents/reorder`, { data }, { method: "POST" })
  },
  youtubeStreamsIndex(request, data: { page: number; query: string }) {
    return request<{ records: Iv2YoutubeStream[]; meta: IIndexMeta }>("/live_streams", data, {
      method: "GET",
    })
  },
  youtubeStreamsDestroy(request, id: Id) {
    return request<{ saved: boolean; message?: string }>(
      `/live_streams/${id}`,
      {},
      {
        method: "DELETE",
      },
    )
  },
  youtubeStreamCreate(
    request,
    data: {
      title: string
      url: string
      start_time?: Date | null
      end_time?: Date | null
      school_ids?: Id[]
      fileboy_image_id?: string
    },
  ) {
    return request<
      { success: true; record: Iv2YoutubeStream } | { success: false; errors: string[] }
    >("/live_streams", data, { method: "POST" })
  },
  youtubeStreamShow(request, id: Id) {
    return request<Iv2YoutubeStream>(`/live_streams/${id}`, {}, { method: "GET" })
  },
  youtubeStreamUpdate(request, id: Id, data: Partial<Iv2YoutubeStream>) {
    return request<
      { success: true; record: Iv2YoutubeStream } | { success: false; errors: string[] }
    >(`/live_streams/${id}`, data, { method: "PUT" })
  },
  youtubeStreamLive(request) {
    return request<Iv2YoutubeStream>("/live_streams/active_live_stream", {}, { method: "GET" })
  },
  youtubeStreamUpcoming(request) {
    return request<Iv2YoutubeStream[]>("/live_streams/upcoming_live_streams", {}, { method: "GET" })
  },
  youtubeStreamPastStreams(request) {
    return request<Iv2YoutubeStream[]>("/live_streams/past_live_streams", {}, { method: "GET" })
  },
  youtubeStreamStartStream(request, id: Id) {
    return request<boolean>(`/live_streams/${id}/publish_stream`, {}, { method: "POST" })
  },
  youtubeStreamEndStream(request, id: Id) {
    return request<boolean>(`/live_streams/${id}/unpublish_stream`, {}, { method: "POST" })
  },
  youtubeStreamPinMessage(request, id: Id, message_id: Id) {
    return request(`/live_streams/${id}/pin_message`, { message_id }, { method: "POST" })
  },
  youtubeStreamMessageSend(request, data: { live_stream_id: Id; message: string }) {
    return request("/live_stream_messages", data, { method: "POST" })
  },
  youtubeStreamMessagesIndex(
    request,
    query: { live_stream_id?: Id; pinned?: boolean; unpaginated?: boolean },
  ) {
    return request<{ records: Iv2YoutubeStreamMessages[]; meta: IIndexMeta }>(
      "/live_stream_messages",
      query,
      { method: "GET" },
    )
  },
  youtubeStreamMessagesReply(request, id: Id, data: any) {
    return request<{ success: true; record: Iv2YoutubeStreamMessages }>(
      `/live_stream_messages/${id}/reply`,
      data,
      { method: "POST" },
    )
  },
  updateSchoolScienceLeaders(
    request,
    data: {
      science_leader_ids: string[]
    },
  ) {
    return request<{ saved: true } | { saved: false; errors: string[] }>(
      "/schools/update_science_leaders",
      data,
      { method: "PUT" },
    )
  },
  formUnitsIndex(
    request,
    params?: {
      query?: string
      unpaginated?: boolean
      form_id?: Id
      page?: number
      user_id?: Id
      start_date?: Date
      end_date?: Date
      active?: boolean
      sort_dir?: "asc" | "desc"
    },
  ) {
    return request<{ meta: IIndexMeta; records: Iv2FormUnit[] }>("/form_units", params, {
      method: "GET",
    })
  },
  formUnitsActiveUpcoming(
    request,
    params?: {
      user_id: Id
      form_id?: Id
    },
  ) {
    return request<{ active: Iv2FormUnit; upcoming: Iv2FormUnit; form: Iv2Form }[]>(
      "/form_units/active_upcoming_units",
      params,
      {
        method: "GET",
      },
    )
  },
  formUnitsShow(request, id: Id) {
    return request<Iv2FormUnit>(
      `/form_units/${id}`,
      {},
      {
        method: "GET",
      },
    )
  },
  formUnitsWithoutUnits(request) {
    return request<Iv2Form[]>("/form_units/forms_without", {}, { method: "GET" })
  },
  userLessonTemplateReset(request, id: Id, options: IResetOptions) {
    return request<
      { saved: boolean; message?: string } & {
        lessonDetails: boolean
        lessonPlan: boolean
        presentation: boolean | null
        quiz: boolean
        documents: boolean
        keywords: boolean
        record: { id: Id; name: string }
      }
    >(`/user_lesson_templates/${id}/reset`, options, { method: "POST" })
  },
  signUpEventsIndex(
    request,
    params: { query?: string; published?: boolean; unpaginated?: boolean },
  ) {
    return request<IIndexRequest<ISignUpEvent>>("/sign_up_events", params, { method: "GET" })
  },
  schoolResourceHistory(request, schoolId: Id) {
    return request<Iv2ResourceChange[]>(
      `/schools/${schoolId}/resource_history`,
      {},
      { method: "GET" },
    )
  },
  ukSchoolSignUpConflicts(request) {
    return request<{ id: Id; name: string; conflict_sign_ups_count: number }[]>(
      "/uk_schools/sign_up_attempts",
      {},
      { method: "GET" },
    )
  },
  scientificEnquiryTypeIndex(
    request,
    params: {
      query?: string
      page?: number
      ids?: Id[]
      unpaginated?: boolean
    },
  ) {
    return request<IIndexRequest<Iv2ScientificEnquiryType>>("/scientific_enquiry_types", params, {
      method: "GET",
    })
  },
  homeworkDuplicate(request, id: Id) {
    return request<{ saved: true; data: IHomework } | { saved: false; data: null; error: string }>(
      `/homeworks/${id}/duplicate`,
      {},
      { method: "POST" },
    )
  },
  homeworkDestroy(request, id: Id) {
    return request<{ success: true } | { success: false; error: string }>(
      `/homeworks/${id}`,
      {},
      { method: "DELETE" },
    )
  },
  homeworksIndex(
    request,
    params: {
      query?: string
      page?: number
      unpaginated?: boolean
      school_id?: Id
      user_id?: Id
      scope?: "upcoming" | "published"
    },
  ) {
    return request<IIndexRequest<IHomeworkIndexRecord>>("/homeworks", params, { method: "GET" })
  },
  homeworksCreate(request, data: Partial<IHomework>) {
    return request<IUpdateResponse<IHomework>>("/homeworks", data, { method: "POST" })
  },
  homeworkTasksCreate(request, data: Partial<IHomeworkTask>) {
    return request<IUpdateResponse<IHomeworkTask>>("/homework_tasks", data, { method: "POST" })
  },
  homeworkTaskSubmissionsCreate(request, data: Partial<IHomeworkTaskSubmission>) {
    return request<IUpdateResponse<IHomeworkTaskSubmission>>("/homework_task_submissions", data, {
      method: "POST",
    })
  },
  homeworksUpdate(request, id: Id, data: Record<string, any>) {
    return request<IUpdateResponse<IHomework>>(`/homeworks/${id}`, data, { method: "PUT" })
  },
  homeworkTasksUpdate(request, id: Id, data: Partial<IHomeworkTask>) {
    return request<IUpdateResponse<IHomeworkTask>>(`/homework_tasks/${id}`, data, { method: "PUT" })
  },
  homeworkTasksDestroy(request, id: Id) {
    return request<IUpdateResponse<null>>(`/homework_tasks/${id}`, {}, { method: "DELETE" })
  },
  homeworkTaskSubmissionsUpdate(request, id: Id, data: Partial<IHomeworkTaskSubmission>) {
    return request<IUpdateResponse<IHomeworkTaskSubmission>>(
      `/homework_task_submissions/${id}`,
      data,
      {
        method: "PUT",
      },
    )
  },
  homeworkShow(request, id: Id) {
    return request<IHomeworkShow>(`/homeworks/${id}`, {}, { method: "GET" })
  },
  formsWithPupils(request, userId: Id) {
    return request<(Iv2Form & { pupils: Iv2Pupil[] })[]>(
      `/forms/with_pupils`,
      { user_id: userId },
      { method: "GET" },
    )
  },
  currentUserLessons(request) {
    return request<{ records: Iv2LessonLesson[] }>("/lessons/my_lessons", {}, { method: "GET" })
  },
  pupilHomeworkIndex(request) {
    return request<
      (IHomework & {
        lesson: Iv2LessonTemplate
        tasks: (IHomeworkTask & { submission?: IHomeworkTaskSubmission })[]
      })[]
    >("/homeworks/pupil_index", {}, { method: "GET" })
  },
  pupilHomeworkShow(request, id: Id) {
    return request<
      | (IHomework & { tasks: (IHomeworkTask & { submission?: IHomeworkTaskSubmission })[] })
      | { error: any }
    >(`/homeworks/${id}/pupil_show`, {}, { method: "GET" })
  },
  pupilGetActiveHomework(request, id: Id) {
    return request<
      (IHomework & {
        lesson: Iv2LessonTemplate
        tasks: (IHomeworkTask & { submission?: IHomeworkTaskSubmission })[]
      })[]
    >(`/pupils/${id}/active_homework`, {}, { method: "GET" })
  },
  homeworkPupilSubmit(request, id: Id) {
    return request<
      { saved: false; error: string } | { saved: true; submitted_at: string; submitted: number }
    >(`/homeworks/${id}/pupil_submit`, {}, { method: "POST" })
  },
  schoolsGetNonLatestWondeImportData(request, id: Id) {
    return request<{
      wonde_import: any
      extra_pupil_ids: Id[]
      extra_pupils: { id: Id; name: string; identifier: string; wonde_id: string }[]
      extra_teacher_ids: Id[]
      extra_teachers: { id: Id; name: string; email: string; wonde_id: string }[]
      extra_form_ids: Id[]
      extra_forms: { id: Id; name: string; wonde_id: string }[]
    }>(`/schools/${id}/get_non_latest_wonde_import_data`, {}, { method: "GET" })
  },
  schoolsDeleteNonLatestWondeImportData(request, id: Id) {
    return request<{ deleted: boolean }>(
      `/schools/${id}/delete_non_latest_wonde_import_data`,
      {},
      { method: "DELETE" },
    )
  },
  errorLogCreate(request, data: { error: string }) {
    return request<any>(`/error_logs`, data, { method: "POST" })
  },
  getUserSignInToken(request, userId: Id) {
    return request<{ target_token: string; return_token: string }>(
      `/users/${userId}/get_sign_in_token`,
      {},
    )
  },
  getSchoolHubspotDetails(request, id: Id) {
    return request<ISchoolHubspotDetails>(`/schools/${id}/hubspot_details`, {}, { method: "GET" })
  },
  updateSchoolHubspotDetails(
    request,
    id: Id,
    submitData: { hubspotId: string | null; hubspotDealId: string | null },
  ) {
    return request<ISchoolHubspotDetails>(`/schools/${id}/update_hubspot_details`, submitData, {
      method: "PATCH",
    })
  },
  adminWipeSchoolData(request, id: Id) {
    return request<{ ok: boolean }>(`/schools/${id}/admin_wipe_data`, {}, { method: "DELETE" })
  },
  wondeDisableAutoSync(request, id: Id) {
    return request<{ ok: boolean }>(
      `/schools/${id}/disable_wonde_auto_sync`,
      {},
      { method: "POST" },
    )
  },
  getLessonPlanPdf(request, id: Id | Id[], name?: string, unit_id?: string) {
    if (Array.isArray(id)) {
      return request<{ redirect?: string; error?: string }>(
        "/lesson_templates/new_lesson_plan_pdfs",
        { ids: id.join(","), name, unit_id },
        { method: "GET" },
      )
    } else {
      return request<{ redirect?: string; error?: string }>(
        `/lesson_templates/${id}/new_lesson_plan_pdf`,
        {},
        { method: "GET" },
      )
    }
  },
})

type ISchoolHubspotDetails = {
  hubspotId: string
  hubspotIdError: string
  hubspotDealId: string
  hubspotDealIdError: string
}

export interface IKpiFormat {
  lesson_views: number
  presentation_views: number
  unit_views: number
  // unit_clicks: number
  template_views: number
  film_views: number
  rock_word_interactions: number
  summative_quiz_interactions: number
  word_search_interactions: number
}

export const useApi2 = apiProvider.use
export const ApiProvider2 = apiProvider.Provider

interface IRequestOptions {
  formData?: boolean
  method?: string
  throwErrorOn404?: boolean
}

type IApiRequest = <T>(path: string, data?: any, opts?: IRequestOptions) => Promise<T>
type IProviderOptions = { [key: string]: (request: IApiRequest, ...data: [...any[]]) => any }
type IBoundApiProviderOptions<T extends IProviderOptions> = {
  [key in keyof T]: T[key] extends (request: any, ...data: infer Data) => infer Ret
    ? (...data: Data) => Ret
    : never
}

function createApiProvider<T extends IProviderOptions>(routes: T) {
  const Context = React.createContext((null as any) as IBoundApiProviderOptions<T>)

  return {
    use() {
      const context = useContext(Context)

      if (!context) {
        throw new Error("must use the ApiProvider")
      }

      return context
    },
    Context,
    Provider({ children }: { children: React.ReactNode }) {
      async function boundApiRequest(
        path: string,
        data?: any,
        opts: IRequestOptions = { formData: false, method: "POST", throwErrorOn404: true },
      ) {
        let response: Response
        if (opts.method === "GET") {
          const queryString =
            data && Object.keys(data).length !== 0 ? `/?${qs.stringify(data)}` : ""
          response = await apiFetch(
            `${path}${queryString}`,
            { method: opts.method },
            undefined,
            true,
          )
        } else if (opts.method === "DELETE") {
          response = await apiFetch(path, { method: opts.method }, undefined, true)
        } else if (opts.formData) {
          response = await apiFetch(
            path,
            { method: opts.method || "POST", body: data },
            undefined,
            true,
          )
        } else {
          response = await apiFetch(
            path,
            {
              method: opts.method || "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify(data),
            },
            undefined,
            true,
          )
        }

        let error
        if (response.status !== 200) {
          error = new Error(response.statusText)
        }
        if (error) {
          ;(error as any).response = response
        }

        if (
          (response.headers.get("content-type")?.includes("text/html") ||
            response.status === 500 ||
            response.status === 403) &&
          response.status !== 401 // dont log for 401s
        ) {
          apiFetch(
            `error_logs`,
            {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                error: JSON.stringify({
                  error: response.statusText,
                  status: response.status,
                  request: { path, data, opts },
                  location: window?.location?.href,
                  text: await response.text(),
                }),
              }),
            },
            undefined,
            true,
          )
            .then(() => console.log("Log created"))
            .catch(console.error)
        }
        if (error) {
          throw error
        }
        return response.json()
      }

      const value: any = {}

      for (const key in routes) {
        value[key] = (...args: any[]) => routes[key](boundApiRequest, ...args)
      }

      return <Context.Provider value={value}>{children}</Context.Provider>
    },
  }
}

function buildUrlWithQueryString(url: string, query: Record<string, any>): string {
  const queryObj: Record<string, any> = {}

  for (const key in query) {
    if (query[key] === undefined) {
      continue
    }
    queryObj[key] = String(query[key])
  }

  return url + "?" + new URLSearchParams(queryObj).toString()
}
